import { inject } from '@angular/core';
import { GuardResult, MaybeAsync } from '@angular/router';
import { selectUserPublicProfileId } from '@fincloud/state/login';
import { selectUserId } from '@fincloud/state/user';
import { StateLibUsersPageActions } from '@fincloud/state/users';
import { Store } from '@ngrx/store';
import { Observable, filter, map, merge, tap } from 'rxjs';

function waitForData$(store: Store): Observable<string> {
  return merge(
    store.select(selectUserPublicProfileId),
    store.select(selectUserId),
  ).pipe(
    filter((userId) => {
      return !!userId;
    }),
  );
}

export function getProfileImageGuard(): MaybeAsync<GuardResult> {
  const store = inject(Store);

  return waitForData$(store).pipe(
    tap((userId) => {
      if (userId) {
        store.dispatch(
          StateLibUsersPageActions.loadUserProfileImage({
            payload: { userId },
          }),
        );
      }
    }),
    map(() => {
      return true;
    }),
  );
}
