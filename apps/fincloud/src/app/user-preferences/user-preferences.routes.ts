import { Routes } from '@angular/router';

import { StateLibUserPreferencesEffects } from '@fincloud/state/user';
import { provideEffects } from '@ngrx/effects';

export const ROUTES: Routes = [
  {
    path: '',
    providers: [provideEffects(StateLibUserPreferencesEffects)],
    children: [
      {
        path: '',
        loadComponent: () =>
          import(
            './components/user-settings-preferences-tab/user-settings-preferences-tab.component'
          ).then((m) => m.UserSettingsPreferencesTabComponent),
      },
    ],
  },
];
