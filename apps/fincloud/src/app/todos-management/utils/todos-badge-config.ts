import { TodosApiUserAssignmentType } from '@fincloud/types/enums';
import { FinBadgeStatus } from '@fincloud/ui/badges';

export const TODOS_BADGE_CONFIG: Record<
  string,
  { text: string; type: FinBadgeStatus }
> = {
  [TodosApiUserAssignmentType.PROVIDE_DATA]: {
    text: $localize`:@@todos.badge.provideData:Informationen bereitstellen`,
    type: FinBadgeStatus.IN_PROGRESS,
  },
  [TodosApiUserAssignmentType.REVIEW_CONTRACT]: {
    text: $localize`:@@todos.badge.reviewContract:Vertrag prüfen`,
    type: FinBadgeStatus.DRAFT,
  },
};
