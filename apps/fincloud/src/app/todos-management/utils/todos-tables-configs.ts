import { TodosStatus, TodosType } from '@fincloud/types/enums';

export const TODOS_TABLE_CONFIG = {
  [TodosType.MY_TASKS]: {
    [TodosStatus.PENDING]: [
      {
        name: $localize`:@@todosManagement.table.columns.type:Aufgabe`,
        prop: 'type',
        flexGrow: 1.5,
        isSortable: true,
        templateName: 'type',
      },
      {
        prop: 'description',
        name: $localize`:@@todosManagement.table.columns.description:Beschreibung`,
        flexGrow: 3,
        isSortable: false,
        templateName: 'description',
      },
      {
        prop: 'dueDate',
        name: $localize`:@@todosManagement.table.columns.dueDate:Zu erledigen bis`,
        flexGrow: 1,
        isSortable: true,
        templateName: 'date',
      },
      {
        prop: 'assignedBy',
        name: $localize`:@@todosManagement.table.columns.assignedBy:<PERSON><PERSON><PERSON><PERSON><PERSON> von`,
        flexGrow: 2,
        isSortable: false,
        templateName: 'pendingAssignBy',
      },
    ],
    [TodosStatus.CLOSED]: [
      {
        name: $localize`:@@todosManagement.table.columns.type:Aufgabe`,
        prop: 'type',
        templateName: 'type',
        isSortable: true,
        flexGrow: 1.5,
      },
      {
        prop: 'description',
        name: $localize`:@@todosManagement.table.columns.description:Beschreibung`,
        flexGrow: 3,
        isSortable: false,
        templateName: 'description',
      },
      {
        prop: 'status',
        name: $localize`:@@todosManagement.table.columns.status:Status`,
        flexGrow: 1.5,
        isSortable: true,
        templateName: 'status',
      },
      {
        prop: 'lastUpdatedDate',
        name: $localize`:@@todosManagement.table.columns.closedOn:Geschlossen am`,
        flexGrow: 1.5,
        isSortable: true,
        templateName: 'closedOn',
      },
      {
        prop: 'closedBy',
        name: $localize`:@@todosManagement.table.columns.closedBy:Geschlossen von`,
        flexGrow: 2,
        isSortable: false,
        templateName: 'closedAssignBy',
      },
    ],
    [TodosStatus.COMPLETED]: [
      {
        name: $localize`:@@todosManagement.table.columns.type:Aufgabe`,
        prop: 'type',
        templateName: 'type',
        isSortable: true,
        flexGrow: 1.5,
      },
      {
        prop: 'description',
        name: $localize`:@@todosManagement.table.columns.description:Beschreibung`,
        flexGrow: 3,
        isSortable: false,
        templateName: 'description',
      },
      {
        prop: 'lastUpdatedDate',
        name: $localize`:@@todosManagement.table.columns.completedOn:Abgeschlossen am`,
        flexGrow: 1.5,
        isSortable: true,
        templateName: 'completedOn',
        headerTemplate: 'completedOn',
      },
      {
        prop: 'completedBy',
        name: $localize`:@@todosManagement.table.columns.completedBy:Abgeschlossen von`,
        flexGrow: 2,
        isSortable: false,
        templateName: 'completedAssignBy',
      },
    ],
  },
  [TodosType.DELEGATED]: {
    [TodosStatus.PENDING]: [
      {
        name: $localize`:@@todosManagement.table.columns.type:Aufgabe`,
        prop: 'type',
        templateName: 'type',
        isSortable: true,
        flexGrow: 1.5,
      },
      {
        prop: 'description',
        name: $localize`:@@todosManagement.table.columns.description:Beschreibung`,
        flexGrow: 3,
        isSortable: false,
        templateName: 'description',
      },
      {
        prop: 'dueDate',
        name: $localize`:@@todosManagement.table.columns.dueDate:Zu erledigen bis`,
        flexGrow: 1,
        isSortable: true,
        templateName: 'date',
      },
      {
        prop: 'closedBy',
        name: $localize`:@@todosManagement.table.columns.assignedTo:Zugewiesen an`,
        flexGrow: 2,
        isSortable: false,
        templateName: 'pendingAssignTo',
      },
    ],
    [TodosStatus.CLOSED]: [
      {
        name: $localize`:@@todosManagement.table.columns.type:Aufgabe`,
        prop: 'type',
        templateName: 'type',
        isSortable: true,
        flexGrow: 1.5,
      },
      {
        prop: 'description',
        name: $localize`:@@todosManagement.table.columns.description:Beschreibung`,
        flexGrow: 3,
        isSortable: false,
        templateName: 'description',
      },
      {
        prop: 'status',
        name: $localize`:@@todosManagement.table.columns.status:Status`,
        flexGrow: 1.5,
        isSortable: true,
        templateName: 'status',
      },
      {
        prop: 'lastUpdatedDate',
        name: $localize`:@@todosManagement.table.columns.closedOn:Geschlossen am`,
        flexGrow: 1.5,
        isSortable: true,
        templateName: 'closedOn',
      },
      {
        prop: 'closedBy',
        name: $localize`:@@todosManagement.table.columns.closedBy:Geschlossen von`,
        flexGrow: 2,
        isSortable: false,
        templateName: 'closedAssignBy',
      },
    ],
    [TodosStatus.COMPLETED]: [
      {
        name: $localize`:@@todosManagement.table.columns.type:Aufgabe`,
        prop: 'type',
        templateName: 'type',
        isSortable: true,
        flexGrow: 1.5,
      },
      {
        prop: 'description',
        name: $localize`:@@todosManagement.table.columns.description:Beschreibung`,
        flexGrow: 3,
        isSortable: false,
        templateName: 'description',
      },
      {
        prop: 'lastUpdatedDate',
        name: $localize`:@@todosManagement.table.columns.completedOn:Abgeschlossen am`,
        flexGrow: 1.5,
        isSortable: true,
        templateName: 'completedOn',
        headerTemplate: 'completedOn',
      },
      {
        prop: 'completedBy',
        name: $localize`:@@todosManagement.table.columns.completedBy:Abgeschlossen von`,
        flexGrow: 2,
        isSortable: false,
        templateName: 'completedAssignBy',
      },
    ],
  },
};
