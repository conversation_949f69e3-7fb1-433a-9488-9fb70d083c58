import { StateLibTodosManagementPageActions } from '@fincloud/state/todos-management';
import {
  AssignmentsByStatusesCount,
  PageUserAssignment,
  UserAssignmentResponse,
} from '@fincloud/swagger-generator/internal-tools';
import { TodosRouterParams } from '@fincloud/types/models';
import { createFeature, createReducer, on } from '@ngrx/store';
import { TodosSortingDirections } from '../../enums/todos-sorting-directions';
import { BusinessCasePaging } from '../../models/business-case-paging';
import { TodosBusinessCaseFilters } from '../../models/todos-business-case-filters';
import { TodosFilters } from '../../models/todos-filters';
import { TodosManagementState } from '../../models/todos-management-state';
import {
  TodosManagementApiActions,
  TodosManagementPageActions,
} from '../actions';
import { selectTodosManagementSelectors } from '../selectors/todos-management.selectors';

const initialState: TodosManagementState = {
  businessCases: {} as UserAssignmentResponse,
  todos: {} as PageUserAssignment,
  counters: {} as AssignmentsByStatusesCount,
  filters: {
    searchPhrase: '',
    orderBy: 'lastUpdatedDate',
    direction: TodosSortingDirections.DESC,
  } as TodosBusinessCaseFilters,
  todosFilters: {
    orderBy: 'lastUpdatedDate',
    direction: TodosSortingDirections.DESC,
  } as TodosFilters,
  businessCasePaging: { limit: 10, offset: 0 } as BusinessCasePaging,
  currentRouteParams: {} as TodosRouterParams,
  isLoadingBusinessCases: false,
  isLoadingTodos: false,
};

export const todosManagementFeature = createFeature({
  name: 'todosManagement',
  reducer: createReducer(
    initialState,
    on(
      TodosManagementPageActions.getBusinessCases,
      (state): TodosManagementState => {
        return {
          ...state,
          businessCasePaging: initialState.businessCasePaging,
          businessCases: initialState.businessCases,
          todos: initialState.todos,
          isLoadingBusinessCases: true,
        };
      },
    ),
    on(
      TodosManagementApiActions.getBusinessCasesSuccess,
      (state, { businessCases }): TodosManagementState => {
        return {
          ...state,
          businessCases,
          isLoadingBusinessCases: false,
        };
      },
    ),
    on(
      TodosManagementApiActions.getBusinessCasesFailure,
      (state): TodosManagementState => {
        return {
          ...state,
          businessCases: initialState.businessCases,
          isLoadingBusinessCases: false,
        };
      },
    ),
    on(
      TodosManagementPageActions.setBusinessCaseFilters,
      (state, { filters }): TodosManagementState => {
        return {
          ...state,
          filters: {
            ...state.filters,
            ...filters,
          },
          businessCasePaging: initialState.businessCasePaging,
          businessCases: initialState.businessCases,
          todos: initialState.todos,
          isLoadingBusinessCases: true,
        };
      },
    ),
    on(
      TodosManagementPageActions.setTodoOrderBy,
      (state, { filters }): TodosManagementState => {
        return {
          ...state,
          todosFilters: {
            ...state.todosFilters,
            ...filters,
          },
          todos: {
            ...state.todos,
            content: [],
          },
        };
      },
    ),
    on(
      TodosManagementApiActions.getAssignmentsCountSuccess,
      (state, counters): TodosManagementState => {
        return {
          ...state,
          counters: counters.counters,
        };
      },
    ),
    on(
      TodosManagementPageActions.changeBusinessCasePage,
      (state, { pageNumber }): TodosManagementState => {
        return {
          ...state,
          businessCasePaging: {
            ...state.businessCasePaging,
            offset: state.businessCasePaging.limit * (pageNumber - 1),
          },
        };
      },
    ),
    on(
      TodosManagementPageActions.changeUrlParams,
      (state): TodosManagementState => {
        return {
          ...state,
          filters: {
            ...state.filters,
            searchPhrase: initialState.filters.searchPhrase,
            direction: initialState.filters.direction,
          },
        };
      },
    ),
    on(TodosManagementPageActions.getTodos, (state): TodosManagementState => {
      return {
        ...state,
        isLoadingTodos: true,
        todos: initialState.todos,
      };
    }),
    on(
      TodosManagementApiActions.getTodosSuccess,
      (state, { todos }): TodosManagementState => {
        return {
          ...state,
          todos,
          isLoadingTodos: false,
        };
      },
    ),
    on(
      TodosManagementApiActions.getTodosFailure,
      TodosManagementApiActions.getMoreTodosFailure,
      (state): TodosManagementState => {
        return {
          ...state,
          isLoadingTodos: false,
        };
      },
    ),
    on(
      TodosManagementPageActions.getMoreTodos,
      (state): TodosManagementState => {
        return {
          ...state,
          isLoadingTodos: true,
        };
      },
    ),
    on(
      TodosManagementApiActions.getMoreTodosSuccess,
      (state, { todos }): TodosManagementState => {
        return {
          ...state,
          todos: {
            ...todos,
            content: [
              ...(state.todos?.content || []),
              ...(todos?.content || []),
            ],
          },
          isLoadingTodos: false,
        };
      },
    ),
    on(
      StateLibTodosManagementPageActions.setCurrentRouteParams,
      (state, { currentRouteParams }): TodosManagementState => {
        return {
          ...state,
          currentRouteParams,
        };
      },
    ),
  ),
  extraSelectors: selectTodosManagementSelectors,
});
