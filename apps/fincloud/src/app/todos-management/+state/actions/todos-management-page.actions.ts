import { TodosApiPerspective, TodosStatus } from '@fincloud/types/enums';
import { createAction, props } from '@ngrx/store';
import { TodosBusinessCaseFilters } from '../../models/todos-business-case-filters';
import { TodosFilters } from '../../models/todos-filters';

export const getTodos = createAction(
  '[Todos Page] Request todos for business case',
  props<{
    businessCaseId: string;
  }>(),
);

export const getMoreTodos = createAction(
  '[Todos Page] Request next todos page for current business case',
  props<{
    businessCaseId: string;
  }>(),
);

export const getBusinessCases = createAction(
  '[Todos Page] Get the Business cases in which the todos are grouped',
);

export const setTodoOrderBy = createAction(
  '[Todos Page] Change todos order by',
  props<{
    businessCaseId: string;
    filters: Partial<TodosFilters>;
  }>(),
);

export const setBusinessCaseFilters = createAction(
  '[Todos Page] Change business case filters',
  props<{
    filters: Partial<TodosBusinessCaseFilters>;
  }>(),
);

export const changeUrlParams = createAction(
  '[Todos Page] Change URL params',
  props<{ todoType?: TodosApiPerspective; todoStatus: TodosStatus }>(),
);

export const getAssignmentsCount = createAction(
  '[Todos Page] Get counts for all cases',
);

export const changeBusinessCasePage = createAction(
  '[Todos Page] Change business cases page',
  props<{
    pageNumber: number;
  }>(),
);
