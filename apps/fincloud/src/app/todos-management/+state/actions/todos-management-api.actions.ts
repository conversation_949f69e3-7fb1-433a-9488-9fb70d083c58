import { HttpErrorResponse } from '@angular/common/http';
import {
  AssignmentsByStatusesCount,
  PageUserAssignment,
  UserAssignmentResponse,
} from '@fincloud/swagger-generator/internal-tools';
import { createAction, props } from '@ngrx/store';

export const getTodosSuccess = createAction(
  '[Todos API] Request todos for business case Success',
  props<{ todos: PageUserAssignment }>(),
);

export const getTodosFailure = createAction(
  '[Todos API] Request todos for business case Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const getMoreTodosSuccess = createAction(
  '[Todos API] Request next todos page for current business case Success',
  props<{ todos: PageUserAssignment }>(),
);

export const getMoreTodosFailure = createAction(
  '[Todos API] Request next todos page for current business case Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const getBusinessCasesSuccess = createAction(
  '[Todos API] Get Business Cases Success',
  props<{ businessCases: UserAssignmentResponse }>(),
);

export const getBusinessCasesFailure = createAction(
  '[Todos API] Get Business cases Failure',
);

export const getAssignmentsCountSuccess = createAction(
  '[Todos API] Get counts for all cases Success',
  props<{
    counters: Partial<AssignmentsByStatusesCount>;
  }>(),
);

export const getAssignmentsCountFailure = createAction(
  '[Todos API] Get counts for all cases Failure',
  props<{ error: HttpErrorResponse }>(),
);
