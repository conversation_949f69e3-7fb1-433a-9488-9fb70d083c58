import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { AvatarComponent } from '@fincloud/components/avatar';
import { TruncatedTextComponent } from '@fincloud/components/truncated-text';
import { User } from '@fincloud/swagger-generator/authorization-server';

@Component({
  selector: 'app-user-card',
  templateUrl: './user-card.component.html',
  styleUrls: ['./user-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AvatarComponent, TruncatedTextComponent],
})
export class UserCardComponent {
  @Input() user: Pick<
    User,
    'firstName' | 'lastName' | 'id' | 'username' | 'enabled'
  >;

  get userFullName() {
    if (this.user) {
      return `${this.user.firstName} ${this.user.lastName}`;
    }

    return '';
  }
}
