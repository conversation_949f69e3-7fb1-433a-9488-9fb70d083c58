@if (user.id) {
  <div
    class="tw-flex tw-items-center tw-py-[2.2rem] tw-px-[1.6rem] tw-cursor-pointer"
    [class.tw-opacity-50]="!user.enabled"
  >
    <ui-avatar
      size="large"
      class="avatar"
      [id]="user.id"
      [names]="userFullName"
    >
    </ui-avatar>
    <div class="tw-flex tw-justify-between tw-w-full">
      <div class="tw-flex tw-flex-col tw-ml-[1.6rem] tw-max-w-[20rem]">
        <ui-truncated-text class="tw-block heading6 tw-text-color-text-primary">
          {{ userFullName }}
        </ui-truncated-text>
        <ui-truncated-text
          class="tw-block heading8 tw-text-color-brand-tertiary"
        >
          {{ user.username }}
        </ui-truncated-text>
      </div>
      <ng-content></ng-content>
    </div>
  </div>
}
