import { AsyncPipe } from '@angular/common';
import { HttpContext } from '@angular/common/http';
import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AvatarComponent } from '@fincloud/components/avatar';
import { CheckboxComponent } from '@fincloud/components/booleans';
import {
  ButtonComponent,
  ButtonLinkComponent,
} from '@fincloud/components/buttons';
import { ValidationErrorComponent } from '@fincloud/components/forms';
import { IconComponent } from '@fincloud/components/icons';
import { CurrencyInputComponent } from '@fincloud/components/number';
import { SpecialFieldLabelOrKeyEnum } from '@fincloud/components/refs';
import {
  SelectComponent,
  SelectExternalListComponent,
} from '@fincloud/components/selects';
import { TooltipComponent } from '@fincloud/components/tooltip';
import { AnchorPathBuilderDirective } from '@fincloud/core/directives';
import { ModalResult } from '@fincloud/core/modal';
import {
  POST_HOG_TRACK_EVENT,
  PostHogEventsToCapture,
} from '@fincloud/core/posthog';
import { Toast } from '@fincloud/core/toast';
import { TEMPLATE_FIELD_KEYS } from '@fincloud/core/types';
import {
  BaseCreateUpdateBusinessCaseComponent,
  CreationStep,
} from '@fincloud/neoshare/base-create-update-business-case';
import {
  BusinessCaseResponse,
  CreateBusinessCaseRequest,
  Param,
  Template,
} from '@fincloud/swagger-generator/business-case-manager';
import { Company } from '@fincloud/swagger-generator/company';
import { Permission } from '@fincloud/types/enums';
import { FormlyModule } from '@ngx-formly/core';
import { NgxPermissionsModule } from 'ngx-permissions';
import { NgScrollbar } from 'ngx-scrollbar';
import { Observable, concat, forkJoin, of } from 'rxjs';
import { catchError, map, switchMap, takeLast } from 'rxjs/operators';
import { CustomerUserDtoExtended } from '../../models/customer-user-dto-extended';
import { BusinessCaseOptionCardComponent } from '../business-case-option-card/business-case-option-card.component';
import { BusinessCaseSelectorForDuplicationComponent } from '../business-case-selector-for-duplication/business-case-selector-for-duplication.component';
import { CancelBusinessCaseCreationModalComponent } from '../cancel-business-case-creation-modal/cancel-business-case-creation-modal.component';
import { DuplicateBusinessCaseCardComponent } from '../duplicate-business-case-card/duplicate-business-case-card.component';

@Component({
  selector: 'app-create-business-case',
  templateUrl: './create-business-case.component.html',
  styleUrls: ['./create-business-case.component.scss'],
  imports: [
    BusinessCaseOptionCardComponent,
    NgxPermissionsModule,
    DuplicateBusinessCaseCardComponent,
    BusinessCaseSelectorForDuplicationComponent,
    FormsModule,
    ReactiveFormsModule,
    IconComponent,
    NgScrollbar,
    TooltipComponent,
    SelectComponent,
    ButtonLinkComponent,
    AnchorPathBuilderDirective,
    CurrencyInputComponent,
    ValidationErrorComponent,
    CheckboxComponent,
    FormlyModule,
    ButtonComponent,
    SelectExternalListComponent,
    AvatarComponent,
    AsyncPipe,
  ],
})
export class CreateBusinessCaseComponent extends BaseCreateUpdateBusinessCaseComponent {
  readonly totalFinancingVolume = $localize`:@@createBusinessCase.formField.total-financing-volume:Finanzierungsvolumen`;
  readonly realEstateTotalFinancingVolume = $localize`:@@createBusinessCase.formField.real-estate-total-financing-volume:Gesamtinvestitionskosten`;
  readonly ownCapacityBiggerThanTotalErrorMessage = $localize`:@@createBusinessCase.ownCapacityField.valueBiggerThanTotalError:Das Eigenkapital darf nicht größer sein als die Gesamtinvestitionskosten`;

  isBusinessCaseCreated = false;
  createBusinessCaseHeader = $localize`:@@createBusinessCase.header:Finanzierungsfall anlegen`;
  duplicateBusinessCaseHeader = $localize`:@@createBusinessCase.header:Finanzierungsfall kopieren`;
  isDuplicateBusinessCaseActive = false;

  get disableCreateButton() {
    return !this.formsValid || this.isLoading || this.isBusinessCaseCreated;
  }

  get permissionHtml(): typeof Permission {
    return Permission;
  }

  onSubmit() {
    if (!this.formsValid) {
      return;
    }

    this.isLoading = true;

    const businessCaseInfo: CreateBusinessCaseRequest = {
      companyId: (
        this.businessCaseBaseForm.controls['company'].value as Company
      )?.id,
      templateId: (
        this.businessCaseBaseForm.controls['template'].value as Template
      )?.id,
      businessCaseType: this.businessCaseType,
      facilityParameters: [
        {
          name: TEMPLATE_FIELD_KEYS.FinancingVolume,
          val: this.businessCaseBaseForm.controls['finanzierungsVolumen']
            .value as number,
        },
      ],
      parameters: [
        ...this.getParams(),
        {
          name: TEMPLATE_FIELD_KEYS.FinancingVolume,
          val: this.businessCaseBaseForm.controls['finanzierungsVolumen']
            .value as number,
        },
      ],
    };

    if (this.isFinancingStructure) {
      const totalValue = (this.businessCaseBaseForm.controls[
        'finanzierungsVolumen'
      ].value || 0) as number;
      const ownCapitalValue = (this.businessCaseBaseForm.controls[
        'eigenkapital'
      ].value || 0) as number;

      businessCaseInfo.finStructureParameters = [
        {
          name: SpecialFieldLabelOrKeyEnum.TOTAL_INVESTMENT_AMOUNT,
          val: totalValue,
        },
        {
          name: SpecialFieldLabelOrKeyEnum.TOTAL_EQUITY,
          val: ownCapitalValue,
        },
      ];
    }
    const context = new HttpContext().set(
      POST_HOG_TRACK_EVENT,
      PostHogEventsToCapture.BUSINESS_CASE_CREATED,
    );

    this.businessCaseControllerService
      .createBusinessCase(
        {
          body: businessCaseInfo,
        },
        context,
      )
      .pipe(
        switchMap((response) => {
          const requests: Observable<unknown>[] = [];
          if (this.selectedCustomerUsers.length) {
            this.selectedCustomerUsers.forEach((user) => {
              requests.push(
                this.participantControllerService
                  .addParticipantUserToBusinessCase({
                    businessCaseId: response.createdBusinessCase.id,
                    userId: user.id,
                  })
                  .pipe(catchError(() => of(null))),
              );
            });
          }

          if (requests?.length) {
            return concat(...requests).pipe(
              takeLast(1),
              map(() => response),
            );
          }

          return of(response);
        }),
        switchMap((response: BusinessCaseResponse) => {
          return forkJoin([
            of(response),
            this.informationController.getAllInformation({
              businessCaseId: response.createdBusinessCase.id,
              body: {
                fieldKeysToBeIncluded: null,
              },
            }),
            this.businessCaseControllerService.linkCadr({
              businessCaseId: response.createdBusinessCase.id,
              linked: this.businessCaseBaseForm.controls['isCADRLinked'].value,
            }),
          ]);
        }),
      )
      .subscribe({
        next: ([response, businessCaseInformation]) => {
          this.isBusinessCaseCreated = true;
          this.finToastService.show(
            Toast.success(
              $localize`:@@toaster.message.success:Fall erfolgreich angelegt`,
            ),
          );
          this.navigateToBusinessCase(
            response.createdBusinessCase.id,
            response.createdBusinessCase.leadCustomerKey,
          );
          setTimeout(() => {
            this.executePortalActionsTemplate(
              response.createdBusinessCase,
              businessCaseInformation,
            );
            // navigation guard will take some time - delay the button re-enabling artificially
            this.isLoading = false;
          }, 2000);
        },
        error: (err) => {
          console.error(err?.message);
          this.finToastService.show(
            Toast.error(
              $localize`:@@createBusinessCase.toast.error:Etwas ist schiefgelaufen.`,
            ),
          );
          this.isLoading = false;
        },
      });
  }

  setCreationState(step: string) {
    this.creationStep = step as CreationStep;
    this.isDuplicateBusinessCaseActive = false;
  }

  onBackBtnClick() {
    this._templateSelectFocused.next(true);
    this.creationStep =
      this.creationStep === CreationStep.USERS
        ? CreationStep.TEMPLATE
        : CreationStep.TYPE_SELECTION;
  }

  onContinueBtnClick() {
    this.creationStep = CreationStep.USERS;
    this._userSelectFocused.next(true);
  }

  onCancel() {
    this.modalService.openComponent<CancelBusinessCaseCreationModalComponent>(
      CancelBusinessCaseCreationModalComponent,
      {},
      { keyboard: false },
      (res: ModalResult<unknown>) => {
        if (res.success) {
          void this.router.navigate(['/']);
        }
      },
    );
  }

  onParticipantAdded(selectedCustomerUser: CustomerUserDtoExtended) {
    if (selectedCustomerUser) {
      this.selectedCustomerUsers.push(selectedCustomerUser);
    }
  }

  onParticipantRemoved(deselectedCustomerUser: CustomerUserDtoExtended) {
    this.selectedCustomerUsers = this.selectedCustomerUsers.filter(
      (user) => user.id !== deselectedCustomerUser.id,
    );
  }

  navigateToCompanyManagement(customerKey: string) {
    void this.router.navigate([customerKey, 'company-management']);
  }

  private getParams(): Param[] {
    const params = Object.keys(this.templateForm.controls).reduce(
      (params: Param[], key: string) => {
        params.push({
          name: key,
          val: this.templateForm.get(key).value as unknown,
        });
        return params;
      },
      [],
    );

    if (this.businessCaseBaseForm.controls['company'].value) {
      params.push({
        name: 'companyId',
        val: (this.businessCaseBaseForm.controls['company'].value as Company)
          ?.id,
      });
    }

    return params;
  }

  onDuplicateBusinessCaseSelect() {
    this.creationStep = CreationStep.DUPLICATE_CASE;
    this.isDuplicateBusinessCaseActive = true;
  }
}
