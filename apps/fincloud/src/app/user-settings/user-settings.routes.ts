import { Routes } from '@angular/router';

export const ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./components/user-settings/user-settings.component').then(
        (m) => m.UserSettingsComponent,
      ),
    children: [
      {
        path: '',
        redirectTo: 'personal-data',
        pathMatch: 'full',
      },
      {
        path: 'personal-data',
        loadComponent: () =>
          import(
            './components/user-settings-personal-data-tab/user-settings-personal-data-tab.component'
          ).then((m) => m.UserSettingsPersonalDataTabComponent),
      },
      {
        path: 'account-security',
        loadComponent: () =>
          import(
            './components/user-settings-account-security-tab/user-settings-account-security-tab.component'
          ).then((m) => m.UserSettingsAccountSecurityTabComponent),
      },
      {
        path: 'preferences',
        loadChildren: () =>
          import('../user-preferences/user-preferences.routes').then(
            (m) => m.ROUTES,
          ),
      },
    ],
  },
];
