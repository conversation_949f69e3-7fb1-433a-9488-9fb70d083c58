import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  GuardR<PERSON>ult,
  MaybeAsync,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import {
  selectCustomer,
  selectGuestCustomerData,
} from '@fincloud/state/customer';
import {
  StateLibInvitationPageActions,
  selectValidGuestCustomerInvitations,
} from '@fincloud/state/invitation';
import { CustomerStatus } from '@fincloud/types/enums';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { isEmpty } from 'lodash-es';
import { filter, iif, map, of, switchMap, take } from 'rxjs';

export function invitedGuestGuard(
  route: ActivatedRouteSnapshot,
  routerState: RouterStateSnapshot,
): MaybeAsync<GuardResult> {
  const router = inject(Router);
  const store = inject(Store);

  const businessCaseIdFromUrl = setBusinessCaseId(routerState);

  if (businessCaseIdFromUrl) {
    store.dispatch(
      StateLibInvitationPageActions.setBusinessCaseIdFromUrl({
        businessCaseIdFromUrl,
      }),
    );
  }

  return store.select(selectCustomer).pipe(
    filter((customer) => !isEmpty(customer)),
    switchMap((customer) =>
      iif(
        () =>
          !isEmpty(customer) &&
          customer?.customerStatus !== CustomerStatus.GUEST,
        of(true),
        store.select(selectGuestCustomerData).pipe(
          concatLatestFrom(() => [
            store.select(selectValidGuestCustomerInvitations),
          ]),
          filter(
            ([{ customer, participantions }, guestCustomerInvitations]) =>
              !isEmpty(customer) &&
              !!participantions &&
              !!guestCustomerInvitations,
          ),
          take(1),
          map(([{ customer, participantions }, guestCustomerInvitations]) => {
            if (isEmpty(participantions) && isEmpty(guestCustomerInvitations)) {
              return router.createUrlTree([
                `${customer.key}/guest/invitation/cancelled`,
              ]);
            }

            return true;
          }),
        ),
      ),
    ),
  );
}

function setBusinessCaseId(routerState: RouterStateSnapshot): string | null {
  const url = routerState.url;
  const regexPattern = /\/business-case\/(?<id>[A-Za-z0-9-]+)/gim;
  const foundRegex = regexPattern.exec(url);

  return foundRegex?.groups?.id || null;
}
