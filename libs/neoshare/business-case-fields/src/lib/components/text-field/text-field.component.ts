import { Async<PERSON>ip<PERSON>, NgTemplateOutlet } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FieldLabelAndActionsComponent } from '@fincloud/components/field-label-and-actions';
import { FinAiSuggestionComponent } from '@fincloud/ui/ai-suggestion';
import {
  FinFieldMessageDirective,
  FinFieldMessagesComponent,
} from '@fincloud/ui/field-message';
import { FinIconComponent } from '@fincloud/ui/icon';
import { FinInputComponent } from '@fincloud/ui/input';
import { FinSize } from '@fincloud/ui/types';
import { FieldBase } from '../../directives/field-base.directive';
@Component({
  selector: 'ui-text-field',
  templateUrl: './text-field.component.html',
  styleUrls: ['./text-field.component.scss'],
  imports: [
    FieldLabelAndActionsComponent,
    NgTemplateOutlet,
    FinInputComponent,
    FormsModule,
    ReactiveFormsModule,
    FinAiSuggestionComponent,
    FinIconComponent,
    FinFieldMessagesComponent,
    FinFieldMessageDirective,
    AsyncPipe,
  ],
})
export class TextFieldComponent extends FieldBase {
  readonly finSize = FinSize;

  @Input() label: string;
  @Input() aiEnabled?: boolean;

  @Output() aiSuggestionReady = new EventEmitter<void>();

  onAiSuggestionReady(): void {
    this.aiSuggestionReady.emit();
  }
}
