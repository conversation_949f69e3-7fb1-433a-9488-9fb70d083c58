import { Pipe, PipeTransform } from '@angular/core';
import { FieldDtoWithIndex } from '@fincloud/core/business-case';
import { Information } from '@fincloud/swagger-generator/business-case-manager';
import { Information as CompanyInformation } from '@fincloud/swagger-generator/company';

@Pipe({ name: 'labelField' })
export class LabelFieldPipe implements PipeTransform {
  translationMapping: { [key: string]: string } = {
    ['Finanzierungsvolumen']: $localize`:@@contractManagement.contract.businessCaseSelectableItem.title:Finanzierungsvolumen`,
  };

  transform(
    fieldInformation: Information | CompanyInformation,
    field: FieldDtoWithIndex,
  ): string {
    if (!fieldInformation) {
      return '';
    }

    return `${field.index} ${this.translationMapping[fieldInformation.field?.label] || fieldInformation.field?.label || field.label}`;
  }
}
