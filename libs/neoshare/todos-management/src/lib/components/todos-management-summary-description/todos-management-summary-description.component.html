@if (todo.type === userAssignmentType.REVIEW_CONTRACT) {
  <div i18n="@@todosManagement.summary.description.reviewContract">
    Prüfen Sie Vertrag
    <span class="tw-font-bold">“{{ todo?.metadata?.contractName }}”.</span>
  </div>
}

@if (todo.type === userAssignmentType.PROVIDE_DATA) {
  <span i18n="@@todosManagement.summary.description.provideData">
    Stellen Sie im Data Room Informationen für das Feld
    <span class="tw-font-bold">{{
      todo?.metadata?.fieldInputRequestInformationLabel
    }}</span>
    bereit.
  </span>
}

@if (todo.type === userAssignmentType.MANUAL_ASSIGNMENT) {
  <span i18n="@@todosManagement.summary.description.manualAssignment">
    <PERSON><PERSON> zuordnung
  </span>
}

@if (todo.type === userAssignmentType.REVIEW_APPLICATION) {
  <span i18n="@@todosManagement.summary.description.reviewApplicationFrom">
    Überprüfung des Antrags von
  </span>
  <span class="tw-font-semibold">
    {{ todo?.metadata?.companyName }}
  </span>
}
