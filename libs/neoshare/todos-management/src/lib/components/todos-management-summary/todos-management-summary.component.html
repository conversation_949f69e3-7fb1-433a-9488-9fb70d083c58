@if (customerKey$ | async; as customerKey) {
  <div
    class="tw-flex tw-items-center tw-justify-between tw-py-[1.2rem] tw-px-[1.6rem]"
  >
    <h2
      class="tw-font-semibold tw-text-[1.6rem] tw-leading-[2.4rem]"
      i18n="@@todoList"
    >
      To-do-Liste
    </h2>
    <button
      fin-button
      [size]="size.M"
      [shape]="shape.ROUND"
      [appearance]="appearance.STEALTH"
      (click)="navigateToTodos(customerKey)"
      routerLinkActive="router-link-active"
      i18n="@@todosList.openTodoPage.button"
    >
      To-do-Seite öffnen
    </button>
  </div>
  <hr finHorizontalSeparator [type]="separatorTypes.SUBTLE" />
  @if (todosBadgeCount$ | async; as count) {
    <fin-tabs
      #finTabs
      [type]="tabType.SECONDARY_COMPACT"
      [size]="size.XL"
      [selectedIndex]="
        (selectedSummaryTab$ | async) === todoType.MY_TASKS ? 0 : 1
      "
      (selectedTabChange)="tabChange($event.index)"
    >
      @for (tab of tabs; let index = $index; track tab.name) {
        <fin-tab>
          <ng-template finTabLabel>
            <div class="tw-flex">
              <span class="tw-me-[0.6rem]">{{ tab.label }}</span>
              <fin-badge-indicator
                [count]="count[tab.countKey] || 0"
                [type]="
                  finTabs.selectedIndex === index
                    ? badgeType.DEFAULT
                    : badgeType.INACTIVE
                "
              ></fin-badge-indicator>
            </div>
          </ng-template>
          <ng-template finTabBody>
            <div class="tw-h-[37.4rem] tw-p-[1.6rem]">
              @if (showSummaryLoadingView$ | async) {
                <ng-container *ngTemplateOutlet="loader"></ng-container>
              }

              @if (showSummaryEmptyView$ | async) {
                <ng-container *ngTemplateOutlet="emptyList"></ng-container>
              }

              @if ((todos$ | async).length > 0) {
                <ng-template [ngTemplateOutlet]="todosListing"></ng-template>
              }
            </div>
          </ng-template>
        </fin-tab>
      }
    </fin-tabs>
  }

  <ng-template #todosListing>
    <fin-scrollbar
      (reachedBottom)="loadMoreTodos()"
      [enableInfinityScroll]="true"
      [reachedBottomOffset]="150"
    >
      @for (todo of todos$ | async; track todo.id) {
        <ng-template
          [ngTemplateOutlet]="todoCard"
          [ngTemplateOutletContext]="{ todo }"
          class="tw-mb-[0.4rem]"
        ></ng-template>
      }

      @if (isTodosLoading$ | async) {
        <div class="tw-flex tw-justify-center tw-pt-[1rem]">
          <fin-loader></fin-loader>
        </div>
      }
    </fin-scrollbar>
  </ng-template>

  <ng-template #todoCard let-todo="todo">
    <div
      class="tw-text-[1.4rem] tw-text-color-text-primary tw-bg-color-surface-secondary tw-mb-[0.4rem] tw-rounded-[0.4rem] tw-p-[1.6rem] tw-cursor-pointer hover:tw-bg-color-surface-hover"
      (click)="redirectByTodoType(todo, customerKey)"
    >
      @if (todo.metadata.isCaseRelated) {
        <div
          class="tw-text-[1.2rem] tw-text-color-text-tertiary tw-mb-[1.2rem]"
        >
          {{ todo.metadata.autoGeneratedBusinessCaseName
          }}<span class="tw-px-[0.4rem]">|</span
          >{{
            todo.metadata?.financingVolumeValue
              | currency
              | removeTrailingZeros
          }}<span class="tw-px-[0.4rem]">|</span
          >{{ todo.metadata?.businessCaseLeadCustomerName }}
        </div>
      } @else {
        <div
          class="tw-text-[1.2rem] tw-text-color-text-tertiary tw-mb-[1.2rem]"
          i18n="@@todosManagement.noCaseRelated"
        >
          Nicht fallbezogen
        </div>
      }

      <div class="tw-mb-[1.2rem]">
        <app-todos-management-summary-description
          [todo]="todo"
        ></app-todos-management-summary-description>

        @if (todo?.metadata?.contractDueDate) {
          <ng-container i18n="@@todosManagement.summary.dueDate">
            Zu erledigen bis:
            <span class="tw-font-bold">{{
              todo?.metadata?.contractDueDate | date
            }}</span>
          </ng-container>
        }
      </div>

      @if ((selectedSummaryTab$ | async) === todoType.DELEGATED_TASKS) {
        <div class="tw-text-[1.2rem] tw-text-color-text-tertiary">
          {{ todo.metadata.assigneeUserFullName
          }}<span class="tw-px-[0.4rem]">|</span
          >{{ todo.metadata?.assigneeUserCustomerName }}
        </div>
      } @else {
        <div class="tw-text-[1.2rem] tw-text-color-text-tertiary">
          {{ todo.metadata.creatorUserFullName
          }}<span class="tw-px-[0.4rem]">|</span
          >{{ todo.metadata?.creatorUserCustomerName }}
        </div>
      }
    </div>
  </ng-template>

  <ng-template #emptyList>
    <div
      class="tw-flex tw-flex-col tw-justify-center tw-items-center tw-ps-[6.4rem] tw-pe-[7.2rem] tw-pt-[16rem] tw-text-color-text-disabled"
    >
      <fin-icon name="checklist" [size]="size.XL"></fin-icon>
      <div
        class="tw-text-[1.6rem] tw-font-semibold"
        i18n="@@todoList.summary.noNewTasks"
      >
        Keine neuen Aufgaben
      </div>
    </div>
  </ng-template>

  <ng-template #loader>
    <div
      class="tw-flex tw-flex-col tw-justify-center tw-items-center tw-ps-[6.4rem] tw-pe-[7.2rem] tw-pt-[16rem] tw-text-color-text-disabled"
    >
      <fin-loader></fin-loader>
    </div>
  </ng-template>
}
