import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Output,
} from '@angular/core';
import {
  FinButtonAppearance,
  FinButtonComponent,
  FinButtonShape,
} from '@fincloud/ui/button';
import { FinIconComponent } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';

@Component({
  selector: 'ns-neogpt-onboarding',
  templateUrl: './neogpt-onboarding.component.html',
  styleUrl: './neogpt-onboarding.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FinIconComponent, FinButtonComponent],
})
export class NeogptOnboardingComponent {
  @Output() markTipsAsRead = new EventEmitter<void>();

  finSize = FinSize;
  finButtonShape = FinButtonShape;
  appearance = FinButtonAppearance;

  finishGptOnbaording() {
    this.markTipsAsRead.emit();
  }
}
