import { NgClass } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { IconComponent } from '@fincloud/components/icons';
import { SuffixPipe } from '@fincloud/core/pipes';
import { NeoGptActiveSession } from '@fincloud/types/enums';
import { NeoGptChatFieldSource } from '@fincloud/types/models';
import { HIGHLIGHT_PREFIX } from '@fincloud/utils';
import { GoToSourceData } from '../../models/go-to-source-data';

@Component({
  selector: 'ns-neogpt-chat-source-section',
  templateUrl: './neogpt-chat-source-section.component.html',
  styleUrl: './neogpt-chat-source-section.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgClass, IconComponent, SuffixPipe],
})
export class NeogptChatSourceSectionComponent {
  @Input() fields: NeoGptChatFieldSource[] = [];
  @Input() session: NeoGptActiveSession;

  @Output() goToFieldSource = new EventEmitter<GoToSourceData>();

  areFieldsVisible = false;
  hightLightPrefix = HIGHLIGHT_PREFIX;
  maxLenghtPerSource = 10;

  toggleFieldsVisibility() {
    this.areFieldsVisible = !this.areFieldsVisible;
  }
}
