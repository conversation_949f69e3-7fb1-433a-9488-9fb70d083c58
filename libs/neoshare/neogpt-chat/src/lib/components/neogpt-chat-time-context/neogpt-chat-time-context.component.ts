import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { DateToTodayTextPipe, DateToYesterdayText } from '@fincloud/core/date';

@Component({
  selector: 'ns-neogpt-chat-time-context',
  templateUrl: './neogpt-chat-time-context.component.html',
  styleUrls: ['./neogpt-chat-time-context.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [DateToTodayTextPipe, DateToYesterdayText],
})
export class NeogptChatTimeContextComponent {
  // example DD.MM.YYYY
  @Input() dateText: string;
}
