import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconComponent } from '@fincloud/components/icons';

@Component({
  selector: 'ns-neogpt-chat-initial-message',
  templateUrl: './neogpt-chat-initial-message.component.html',
  styleUrls: ['./neogpt-chat-initial-message.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent, DatePipe],
})
export class NeogptChatInitialMessageComponent {
  timeStamp = new Date().getTime();
  userName = 'neoshare AI';
}
