import { ChangeDetectionStrategy, Component } from '@angular/core';
import { DotsLoaderComponent } from '@fincloud/components/dots-loader';
import { IconComponent } from '@fincloud/components/icons';

@Component({
  selector: 'ns-neogpt-chat-message-loading',
  templateUrl: './neogp-chat-message-loading.component.html',
  styleUrls: ['./neogp-chat-message-loading.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent, DotsLoaderComponent],
})
export class NeogptChatMessageLoadingComponent {
  userName = 'neoshare AI';
}
