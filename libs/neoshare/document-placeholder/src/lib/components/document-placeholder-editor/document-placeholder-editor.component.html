<div class="modal-header">
  <h2 class="modal-title" i18n="@@documentPlaceholder.modal.title">
    Signaturplatzierung
  </h2>

  <button
    type="button"
    class="btn-close"
    aria-label="Close"
    (click)="requestClosingModal()"
  ></button>
</div>

<div class="modal-body d-flex">
  <div class="col-2 sidebar placeholders-container">
    <ng-scrollbar appearance="native">
      <div class="group">
        <p class="group-header" i18n="@@documentPlaceholder.groupHeader">
          Signaturfelder
        </p>
        <div
          draggable="true"
          class="document-placeholder"
          (dragstart)="setPlaceholderInfo('SIGNATURE', $event)"
          (dragend)="destroyDragElement()"
        >
          <span class="label" i18n="@@documentPlaceholder.label.signature"
            >Signatur</span
          >
          <ui-icon name="drag-indicator" color="gray"></ui-icon>
        </div>
        <div
          draggable="true"
          class="document-placeholder"
          (dragstart)="setPlaceholderInfo('INITIALS', $event)"
          (dragend)="destroyDragElement()"
        >
          <span class="label" i18n="@@documentPlaceholder.label.initials"
            >Initialen</span
          >
          <ui-icon name="drag-indicator" color="gray"></ui-icon>
        </div>
      </div>

      <div class="group">
        <p
          class="group-header"
          i18n="@@documentPlaceholder.groupHeader.autofill"
        >
          Autofill-Felder
        </p>
        <div
          draggable="true"
          class="document-placeholder"
          (dragstart)="setPlaceholderInfo('SALUTATION', $event)"
          (dragend)="destroyDragElement()"
        >
          <span class="label" i18n="@@documentPlaceholder.label.salutation"
            >Anrede</span
          >
          <ui-icon name="drag-indicator" color="gray"></ui-icon>
        </div>
        <div
          draggable="true"
          class="document-placeholder"
          (dragstart)="setPlaceholderInfo('TITLE', $event)"
          (dragend)="destroyDragElement()"
        >
          <span class="label" i18n="@@documentPlaceholder.label.title"
            >Titel</span
          >
          <ui-icon name="drag-indicator" color="gray"></ui-icon>
        </div>
        <div
          draggable="true"
          class="document-placeholder"
          (dragstart)="setPlaceholderInfo('FULL_NAME', $event)"
          (dragend)="destroyDragElement()"
        >
          <span class="label" i18n="@@documentPlaceholder.label.name"
            >Name</span
          >
          <ui-icon name="drag-indicator" color="gray"></ui-icon>
        </div>
        <div
          draggable="true"
          class="document-placeholder"
          (dragstart)="setPlaceholderInfo('POSITION', $event)"
          (dragend)="destroyDragElement()"
        >
          <span class="label" i18n="@@documentPlaceholder.label.position"
            >Position</span
          >
          <ui-icon name="drag-indicator" color="gray"></ui-icon>
        </div>
        <div
          draggable="true"
          class="document-placeholder"
          (dragstart)="setPlaceholderInfo('EMAIL', $event)"
          (dragend)="destroyDragElement()"
        >
          <span class="label" i18n="@@documentPlaceholder.label.email"
            >E-Mail-Adresse</span
          >
          <ui-icon name="drag-indicator" color="gray"></ui-icon>
        </div>
        <div
          draggable="true"
          class="document-placeholder"
          (dragstart)="setPlaceholderInfo('COMPANY', $event)"
          (dragend)="destroyDragElement()"
        >
          <span class="label" i18n="@@documentPlaceholder.label.organization"
            >Organisation</span
          >
          <ui-icon name="drag-indicator" color="gray"></ui-icon>
        </div>
        <div
          draggable="true"
          class="document-placeholder"
          (dragstart)="setPlaceholderInfo('DATE_SIGNED', $event)"
          (dragend)="destroyDragElement()"
        >
          <span class="label" i18n="@@documentPlaceholder.label.signed"
            >Unterzeichnet am</span
          >
          <ui-icon name="drag-indicator" color="gray"></ui-icon>
        </div>
      </div>
    </ng-scrollbar>
  </div>

  <div class="col-8 document-container">
    <ng-scrollbar>
      @for (page of pages; track page; let pageIndex = $index) {
        <div
          (dragover)="allowDrop($event)"
          (drop)="placeholderDropped($event, pageIndex)"
          class="pdf-page-container"
          [ngClass]="'page-' + pageIndex"
        >
          <ng-template #viewContainerRef></ng-template>
          <div
            [id]="'pdf-page-container' + pageIndex"
            (pointerdown)="unselectPlaceholders($event)"
          >
            <canvas #canvas hidden></canvas>
            <img [src]="page" alt="pdf" class="document-image" id="image" />
          </div>
          <div class="page-number float-end">
            {{ pageIndex + 1 }}/{{ pages.length }}
          </div>
        </div>
      }
    </ng-scrollbar>
  </div>

  <div class="col-2 sidebar">
    <div class="d-flex flex-column justify-content-between h-100">
      <div>
        @if (
          !placeholders.size ||
          !(selectedPlaceholder || selectedPlaceholders?.length)
        ) {
          <div>
            <ns-document-placeholder-empty-state></ns-document-placeholder-empty-state>
          </div>
        }
        @if (selectedPlaceholders?.length > 1) {
          <div class="group">
            <p
              class="group-header"
              i18n="@@documentPlaceholder.groupHeader.alignment"
            >
              Ausrichtung
            </p>
            <div
              class="btn-group btn-group-lg placeholder-alignment w-100"
              role="group"
            >
              <input
                type="checkbox"
                id="align-left"
                class="btn-check"
                autocomplete="off"
              />
              <label
                class="btn text-format"
                for="align-left"
                (click)="alignPlaceholders('LEFT')"
              >
                <ui-icon name="align_horizontal_left"></ui-icon>
              </label>
              <input
                type="checkbox"
                id="align-right"
                class="btn-check"
                autocomplete="off"
              />
              <label
                class="btn text-format"
                for="align-right"
                (click)="alignPlaceholders('RIGHT')"
              >
                <ui-icon name="align_horizontal_right"></ui-icon
              ></label>
              <input
                type="checkbox"
                id="align-top"
                class="btn-check"
                autocomplete="off"
              />
              <label
                class="btn text-format"
                for="align-top"
                (click)="alignPlaceholders('BOTTOM')"
              >
                <ui-icon name="align_vertical_bottom"></ui-icon
              ></label>
              <input
                type="checkbox"
                id="align-bottom"
                class="btn-check"
                autocomplete="off"
              />
              <label
                class="btn text-format"
                for="align-bottom"
                (click)="alignPlaceholders('TOP')"
              >
                <ui-icon name="align_vertical_top"></ui-icon
              ></label>
            </div>
            <div class="delete-button" (click)="deletePlaceholdersBulk()">
              <ui-icon
                name="delete"
                size="medium-large"
                type="outline"
                color="gray"
              ></ui-icon>
              <span class="label" i18n="@@userManagement.user.userEditor.delete"
                >Löschen</span
              >
            </div>
          </div>
        }

        @if (selectedPlaceholder) {
          <div class="group">
            <p
              class="group-header"
              i18n="@@documentPlaceholder.groupHeader.signerList"
            >
              Unterzeichnerliste
            </p>
            <div class="d-flex flex-column gap-3">
              <ui-select
                [customOptionItemTemplate]="signerTemplateOptionItem"
                [customLabelTemplate]="signerLabelTemplate"
                [(ngModel)]="lastSelectedSigner"
                [options]="signers"
                [isClearable]="false"
                background="dark"
                labelKey="name"
                (selectionChange)="updatePlaceholderValue($event)"
              ></ui-select>
              @if (
                selectedPlaceholder.placeholderType === 'FULL_NAME' ||
                selectedPlaceholder.placeholderType === 'LAST_NAME' ||
                selectedPlaceholder.placeholderType === 'FIRST_NAME'
              ) {
                <ui-select
                  background="dark"
                  [(ngModel)]="this.selectedNameOption"
                  [options]="nameOptions"
                  [isClearable]="false"
                  (selectionChange)="updateNamePreference($event.value)"
                ></ui-select>
              }
            </div>
          </div>
          @if (
            selectedPlaceholder.placeholderType !== 'SIGNATURE' &&
            selectedPlaceholder.placeholderType !== 'INITIALS'
          ) {
            <div class="group">
              <p
                class="group-header"
                i18n="@@documentPlaceholder.groupHeader.formatting"
              >
                Formatierung
              </p>
              <div class="d-flex gap-3">
                <div
                  class="btn-group btn-group-lg placeholder-formatting w-100"
                  role="group"
                >
                  <input
                    [ngModel]="placeholderCustomization.bold"
                    (ngModelChange)="
                      updatePlaceholderCustomization(
                        'bold',
                        !placeholderCustomization.bold
                      )
                    "
                    type="checkbox"
                    class="btn-check"
                    id="btn-bold"
                    autocomplete="off"
                  />
                  <label class="btn text-format" for="btn-bold"><b>B</b></label>
                  <input
                    [ngModel]="placeholderCustomization.italic"
                    (ngModelChange)="
                      updatePlaceholderCustomization(
                        'italic',
                        !placeholderCustomization.italic
                      )
                    "
                    type="checkbox"
                    class="btn-check"
                    id="btn-italic"
                    autocomplete="off"
                  />
                  <label class="btn text-format" for="btn-italic"
                    ><i>I</i></label
                  >
                  <input
                    [ngModel]="placeholderCustomization.underline"
                    (ngModelChange)="
                      updatePlaceholderCustomization(
                        'underline',
                        !placeholderCustomization.underline
                      )
                    "
                    type="checkbox"
                    class="btn-check"
                    id="btn-underline"
                    autocomplete="off"
                  />
                  <label class="btn text-format" for="btn-underline"
                    ><u>U</u></label
                  >
                </div>
                <ui-select
                  [(ngModel)]="selectedFontSize"
                  background="dark"
                  [options]="fontSizeOptions"
                  [isClearable]="false"
                  (selectionChange)="
                    updatePlaceholderCustomization('fontSize', $event.value)
                  "
                ></ui-select>
              </div>
              <!-- For now it will be not possible to change the font type -->
              <!-- <ui-select
                                          [(ngModel)]="selectedFontLabel"
                                          [options]="fontOptions"
                                          [isClearable]="false"
                (selectionChange)="
                  updatePlaceholderCustomization('fontType', $event.value)
                "></ui-select> -->
            </div>
          }
          @if (selectedPlaceholder.placeholderType === 'SIGNATURE') {
            <div class="group">
              <p
                class="group-header"
                i18n="@@documentPlaceholder.groupHeader.scaling"
              >
                Skalierung
              </p>
              <ui-basic-slider
                [floor]="50"
                [ceil]="200"
                [value]="selectedPlaceholder.scaleValue * 100 || 100"
                (valueChanged)="updateSignatureScale($event / 100)"
                suffix="%"
              ></ui-basic-slider>
            </div>
          }
          @if (selectedPlaceholder.placeholderType === 'INITIALS') {
            <div class="group">
              <p
                class="group-header"
                i18n="@@documentPlaceholder.groupHeader.scaling"
              >
                Skalierung
              </p>
              <ui-basic-slider
                [floor]="50"
                [ceil]="100"
                [value]="selectedPlaceholder.scaleValue * 100 || 100"
                (valueChanged)="updateSignatureScale($event / 100)"
                suffix="%"
              ></ui-basic-slider>
            </div>
          }
          <div class="delete-button" (click)="deletePlaceholder()">
            <ui-icon
              name="delete"
              size="medium-large"
              type="outline"
              color="gray"
            ></ui-icon>
            <span class="label" i18n="@@userManagement.user.userEditor.delete"
              >Löschen</span
            >
          </div>
        }
      </div>

      <ui-button
        [disabled]="createContractInProgress || !!emptyPlaceholderIds.size"
        size="large"
        label="Senden"
        i18n-label="@@contract.create.modal.fourthStep.button.forward"
        [hasDotsLoader]="createContractInProgress"
        [block]="true"
        (clicked)="createContract()"
      >
      </ui-button>
    </div>
  </div>
</div>

<ng-template #signerTemplateOptionItem let-item="item.value">
  <div class="d-flex align-items-center signer-template-option">
    <div
      class="color"
      [ngStyle]="{ 'background-color': getSignerColor(item) }"
    ></div>
    <div>{{ item.name }}</div>
  </div>
</ng-template>

<ng-template #signerLabelTemplate let-item="item">
  @if (item) {
    <div class="d-flex align-items-center signer-label">
      <div
        class="color"
        [ngStyle]="{ 'background-color': getSignerColor(item) }"
      ></div>
      <div>{{ item.name }}</div>
    </div>
  }
</ng-template>

<ng-template #missingSignersTemplate>
  @if (signerWithoutSignatureAdded.length > 2) {
    <ng-scrollbar class="side-scroll">
      <ng-template [ngTemplateOutlet]="missingSignersContent"></ng-template>
    </ng-scrollbar>
  } @else {
    <ng-template [ngTemplateOutlet]="missingSignersContent"></ng-template>
  }
</ng-template>

<ng-template #missingSignersContent>
  @for (user of signerWithoutSignatureAdded; track user) {
    <div class="missing-signers">
      <div
        class="avatar-container"
        [ngStyle]="{ 'background-color': getSignerColor(user) }"
      >
        <div class="initials">
          <div>
            {{ user.name | initials }}
          </div>
        </div>
      </div>
      <span class="heading4"> {{ user.name }}</span>
    </div>
  }
</ng-template>
