import {
  FieldDto,
  Folder,
} from '@fincloud/swagger-generator/business-case-manager';
export interface FolderContentBaseServiceDefinition {
  // Folder related methods
  showFolderDetails(folder: Folder): void;
  renameFolder(folder: Folder, groupKey: string): void;
  moveFolder(folderId: string, groupKey: string): void;
  deleteFolder(folderId: string, groupKey: string): void;
  // Document related methods
  moveDocument(documentField: FieldDto, groupKey: string): void;
}
