import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'mapKeyToText' })
export class MapKpiKeyPipe implements PipeTransform {
  // Map key to real text and capitalize if required
  transform<K extends string | number | symbol, V>(
    key: K,
    mapping: Record<K, V>,
    capitalize = false,
  ): V | string {
    let result = mapping[key] || '';

    // Check if the result is a string and capitalize if necessary
    if (capitalize && typeof result === 'string') {
      result = result.toUpperCase();
    }

    return result;
  }
}
