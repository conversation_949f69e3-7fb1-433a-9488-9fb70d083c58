/* tslint:disable */
/* eslint-disable */
import { Injectable } from '@angular/core';
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';
import { RequestBuilder } from '../request-builder';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';


@Injectable({
  providedIn: 'root',
})
export class BankService extends BaseService {
  constructor(
    config: ApiConfiguration,
    http: HttpClient
  ) {
    super(config, http);
  }

  /**
   * Path part for operation emailControllerSendBankCreatedEmail
   */
  static readonly EmailControllerSendBankCreatedEmailPath = '/email/bank/{bankKey}/created';

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `emailControllerSendBankCreatedEmail()` instead.
   *
   * This method doesn't expect any request body.
   */
  emailControllerSendBankCreatedEmail$Response(params: {
                                                 bankKey: string;
                                                 context?: HttpContext
                                               }
  ): Observable<StrictHttpResponse<void>> {

    const rb = new RequestBuilder(this.rootUrl, BankService.EmailControllerSendBankCreatedEmailPath, 'post');
    if (params) {
      rb.path('bankKey', params.bankKey, {});
    }

    return this.http.request(rb.build({
      responseType: 'text',
      accept: '*/*',
      context: params?.context
    })).pipe(
      filter((r: any) => r instanceof HttpResponse),
      map((r: HttpResponse<any>) => {
        return (r as HttpResponse<any>).clone({body: undefined}) as StrictHttpResponse<void>;
      })
    );
  }

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to only to the response body.
   * To access the full response (for headers, for example), `emailControllerSendBankCreatedEmail$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  emailControllerSendBankCreatedEmail(params: {
                                        bankKey: string;
                                        context?: HttpContext
                                      }
  ): Observable<void> {

    return this.emailControllerSendBankCreatedEmail$Response(params).pipe(
      map((r: StrictHttpResponse<void>) => r.body as void)
    );
  }

}
