/* tslint:disable */
/* eslint-disable */
import { Injectable } from '@angular/core';
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';
import { RequestBuilder } from '../request-builder';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';

import { ContactSupportDto } from '../models/contact-support-dto';
import { MissingGlobalContactPersonDto } from '../models/missing-global-contact-person-dto';

@Injectable({
  providedIn: 'root',
})
export class TeamsService extends BaseService {
  constructor(
    config: ApiConfiguration,
    http: HttpClient
  ) {
    super(config, http);
  }

  /**
   * Path part for operation teamsControllerSendMissingContactPersonMessage
   */
  static readonly TeamsControllerSendMissingContactPersonMessagePath = '/teams/missing-contact-person';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `teamsControllerSendMissingContactPersonMessage()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  teamsControllerSendMissingContactPersonMessage$Response(params: {
                                                            context?: HttpContext
                                                            body: MissingGlobalContactPersonDto
                                                          }
  ): Observable<StrictHttpResponse<void>> {

    const rb = new RequestBuilder(this.rootUrl, TeamsService.TeamsControllerSendMissingContactPersonMessagePath, 'post');
    if (params) {
      rb.body(params.body, 'application/json');
    }

    return this.http.request(rb.build({
      responseType: 'text',
      accept: '*/*',
      context: params?.context
    })).pipe(
      filter((r: any) => r instanceof HttpResponse),
      map((r: HttpResponse<any>) => {
        return (r as HttpResponse<any>).clone({body: undefined}) as StrictHttpResponse<void>;
      })
    );
  }

  /**
   * This method provides access to only to the response body.
   * To access the full response (for headers, for example), `teamsControllerSendMissingContactPersonMessage$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  teamsControllerSendMissingContactPersonMessage(params: {
                                                   context?: HttpContext
                                                   body: MissingGlobalContactPersonDto
                                                 }
  ): Observable<void> {

    return this.teamsControllerSendMissingContactPersonMessage$Response(params).pipe(
      map((r: StrictHttpResponse<void>) => r.body as void)
    );
  }

  /**
   * Path part for operation teamsControllerSendSupportRequest
   */
  static readonly TeamsControllerSendSupportRequestPath = '/teams/support-request';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `teamsControllerSendSupportRequest()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  teamsControllerSendSupportRequest$Response(params: {
                                               context?: HttpContext
                                               body: ContactSupportDto
                                             }
  ): Observable<StrictHttpResponse<void>> {

    const rb = new RequestBuilder(this.rootUrl, TeamsService.TeamsControllerSendSupportRequestPath, 'post');
    if (params) {
      rb.body(params.body, 'application/json');
    }

    return this.http.request(rb.build({
      responseType: 'text',
      accept: '*/*',
      context: params?.context
    })).pipe(
      filter((r: any) => r instanceof HttpResponse),
      map((r: HttpResponse<any>) => {
        return (r as HttpResponse<any>).clone({body: undefined}) as StrictHttpResponse<void>;
      })
    );
  }

  /**
   * This method provides access to only to the response body.
   * To access the full response (for headers, for example), `teamsControllerSendSupportRequest$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  teamsControllerSendSupportRequest(params: {
                                      context?: HttpContext
                                      body: ContactSupportDto
                                    }
  ): Observable<void> {

    return this.teamsControllerSendSupportRequest$Response(params).pipe(
      map((r: StrictHttpResponse<void>) => r.body as void)
    );
  }

}
