/* tslint:disable */
/* eslint-disable */
import { Injectable } from '@angular/core';
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';
import { RequestBuilder } from '../request-builder';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';


@Injectable({
  providedIn: 'root',
})
export class BusinessCaseService extends BaseService {
  constructor(
    config: ApiConfiguration,
    http: HttpClient
  ) {
    super(config, http);
  }

  /**
   * Path part for operation emailControllerSendBusinessCaseUpdatedEmail
   */
  static readonly EmailControllerSendBusinessCaseUpdatedEmailPath = '/email/consortium/{consortiumId}/updated';

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `emailControllerSendBusinessCaseUpdatedEmail()` instead.
   *
   * This method doesn't expect any request body.
   */
  emailControllerSendBusinessCaseUpdatedEmail$Response(params: {
                                                       consortiumId: string;
                                                       context?: HttpContext
                                                     }
  ): Observable<StrictHttpResponse<void>> {

    const rb = new RequestBuilder(this.rootUrl, BusinessCaseService.EmailControllerSendBusinessCaseUpdatedEmailPath, 'post');
    if (params) {
      rb.path('consortiumId', params.consortiumId, {});
    }

    return this.http.request(rb.build({
      responseType: 'text',
      accept: '*/*',
      context: params?.context
    })).pipe(
      filter((r: any) => r instanceof HttpResponse),
      map((r: HttpResponse<any>) => {
        return (r as HttpResponse<any>).clone({body: undefined}) as StrictHttpResponse<void>;
      })
    );
  }

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to only to the response body.
   * To access the full response (for headers, for example), `emailControllerSendBusinessCaseUpdatedEmail$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  emailControllerSendBusinessCaseUpdatedEmail(params: {
                                              consortiumId: string;
                                              context?: HttpContext
                                            }
  ): Observable<void> {

    return this.emailControllerSendBusinessCaseUpdatedEmail$Response(params).pipe(
      map((r: StrictHttpResponse<void>) => r.body as void)
    );
  }

  /**
   * Path part for operation emailControllerSendBusinessCaseParticipantRemovedEmail
   */
  static readonly EmailControllerSendBusinessCaseParticipantRemovedEmailPath = '/email/consortium/{consortiumId}/participant/removed';

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `emailControllerSendBusinessCaseParticipantRemovedEmail()` instead.
   *
   * This method doesn't expect any request body.
   */
  emailControllerSendBusinessCaseParticipantRemovedEmail$Response(params: {
                                                                  consortiumId: string;
                                                                  context?: HttpContext
                                                                }
  ): Observable<StrictHttpResponse<void>> {

    const rb = new RequestBuilder(this.rootUrl, BusinessCaseService.EmailControllerSendBusinessCaseParticipantRemovedEmailPath, 'post');
    if (params) {
      rb.path('consortiumId', params.consortiumId, {});
    }

    return this.http.request(rb.build({
      responseType: 'text',
      accept: '*/*',
      context: params?.context
    })).pipe(
      filter((r: any) => r instanceof HttpResponse),
      map((r: HttpResponse<any>) => {
        return (r as HttpResponse<any>).clone({body: undefined}) as StrictHttpResponse<void>;
      })
    );
  }

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to only to the response body.
   * To access the full response (for headers, for example), `emailControllerSendBusinessCaseParticipantRemovedEmail$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  emailControllerSendBusinessCaseParticipantRemovedEmail(params: {
                                                         consortiumId: string;
                                                         context?: HttpContext
                                                       }
  ): Observable<void> {

    return this.emailControllerSendBusinessCaseParticipantRemovedEmail$Response(params).pipe(
      map((r: StrictHttpResponse<void>) => r.body as void)
    );
  }

  /**
   * Path part for operation emailControllerSendBusinessCaseParticipantAddedEmail
   */
  static readonly EmailControllerSendBusinessCaseParticipantAddedEmailPath = '/email/consortium/{consortiumId}/participant/added';

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `emailControllerSendBusinessCaseParticipantAddedEmail()` instead.
   *
   * This method doesn't expect any request body.
   */
  emailControllerSendBusinessCaseParticipantAddedEmail$Response(params: {
                                                                consortiumId: string;
                                                                context?: HttpContext
                                                              }
  ): Observable<StrictHttpResponse<void>> {

    const rb = new RequestBuilder(this.rootUrl, BusinessCaseService.EmailControllerSendBusinessCaseParticipantAddedEmailPath, 'post');
    if (params) {
      rb.path('consortiumId', params.consortiumId, {});
    }

    return this.http.request(rb.build({
      responseType: 'text',
      accept: '*/*',
      context: params?.context
    })).pipe(
      filter((r: any) => r instanceof HttpResponse),
      map((r: HttpResponse<any>) => {
        return (r as HttpResponse<any>).clone({body: undefined}) as StrictHttpResponse<void>;
      })
    );
  }

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to only to the response body.
   * To access the full response (for headers, for example), `emailControllerSendBusinessCaseParticipantAddedEmail$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  emailControllerSendBusinessCaseParticipantAddedEmail(params: {
                                                       consortiumId: string;
                                                       context?: HttpContext
                                                     }
  ): Observable<void> {

    return this.emailControllerSendBusinessCaseParticipantAddedEmail$Response(params).pipe(
      map((r: StrictHttpResponse<void>) => r.body as void)
    );
  }

  /**
   * Path part for operation emailControllerSendBusinessCaseInviteEmail
   */
  static readonly EmailControllerSendBusinessCaseInviteEmailPath = '/email/bank/{bankKey}/invite/{consortiumId}';

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `emailControllerSendBusinessCaseInviteEmail()` instead.
   *
   * This method doesn't expect any request body.
   */
  emailControllerSendBusinessCaseInviteEmail$Response(params: {
                                                      bankKey: string;
                                                      consortiumId: string;
                                                      context?: HttpContext
                                                    }
  ): Observable<StrictHttpResponse<void>> {

    const rb = new RequestBuilder(this.rootUrl, BusinessCaseService.EmailControllerSendBusinessCaseInviteEmailPath, 'post');
    if (params) {
      rb.path('bankKey', params.bankKey, {});
      rb.path('consortiumId', params.consortiumId, {});
    }

    return this.http.request(rb.build({
      responseType: 'text',
      accept: '*/*',
      context: params?.context
    })).pipe(
      filter((r: any) => r instanceof HttpResponse),
      map((r: HttpResponse<any>) => {
        return (r as HttpResponse<any>).clone({body: undefined}) as StrictHttpResponse<void>;
      })
    );
  }

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to only to the response body.
   * To access the full response (for headers, for example), `emailControllerSendBusinessCaseInviteEmail$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  emailControllerSendBusinessCaseInviteEmail(params: {
                                             bankKey: string;
                                             consortiumId: string;
                                             context?: HttpContext
                                           }
  ): Observable<void> {

    return this.emailControllerSendBusinessCaseInviteEmail$Response(params).pipe(
      map((r: StrictHttpResponse<void>) => r.body as void)
    );
  }

  /**
   * Path part for operation emailControllerSendBusinessCaseApplicationSubmittedEmail
   */
  static readonly EmailControllerSendBusinessCaseApplicationSubmittedEmailPath = '/email/consortium/{consortiumId}/application-submitted';

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `emailControllerSendBusinessCaseApplicationSubmittedEmail()` instead.
   *
   * This method doesn't expect any request body.
   */
  emailControllerSendBusinessCaseApplicationSubmittedEmail$Response(params: {
                                                                    consortiumId: string;
                                                                    context?: HttpContext
                                                                  }
  ): Observable<StrictHttpResponse<void>> {

    const rb = new RequestBuilder(this.rootUrl, BusinessCaseService.EmailControllerSendBusinessCaseApplicationSubmittedEmailPath, 'post');
    if (params) {
      rb.path('consortiumId', params.consortiumId, {});
    }

    return this.http.request(rb.build({
      responseType: 'text',
      accept: '*/*',
      context: params?.context
    })).pipe(
      filter((r: any) => r instanceof HttpResponse),
      map((r: HttpResponse<any>) => {
        return (r as HttpResponse<any>).clone({body: undefined}) as StrictHttpResponse<void>;
      })
    );
  }

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to only to the response body.
   * To access the full response (for headers, for example), `emailControllerSendBusinessCaseApplicationSubmittedEmail$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  emailControllerSendBusinessCaseApplicationSubmittedEmail(params: {
                                                           consortiumId: string;
                                                           context?: HttpContext
                                                         }
  ): Observable<void> {

    return this.emailControllerSendBusinessCaseApplicationSubmittedEmail$Response(params).pipe(
      map((r: StrictHttpResponse<void>) => r.body as void)
    );
  }

}
