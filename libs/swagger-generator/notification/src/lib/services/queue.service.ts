/* tslint:disable */
/* eslint-disable */
import { Injectable } from '@angular/core';
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';
import { RequestBuilder } from '../request-builder';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';


@Injectable({
  providedIn: 'root',
})
export class QueueService extends BaseService {
  constructor(
    config: ApiConfiguration,
    http: HttpClient
  ) {
    super(config, http);
  }

  /**
   * Path part for operation emailControllerClearQueues
   */
  static readonly EmailControllerClearQueuesPath = '/email/queues/clear';

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `emailControllerClearQueues()` instead.
   *
   * This method doesn't expect any request body.
   */
  emailControllerClearQueues$Response(params?: {
                                        context?: HttpContext
                                      }
  ): Observable<StrictHttpResponse<void>> {

    const rb = new RequestBuilder(this.rootUrl, QueueService.EmailControllerClearQueuesPath, 'post');
    if (params) {
    }

    return this.http.request(rb.build({
      responseType: 'text',
      accept: '*/*',
      context: params?.context
    })).pipe(
      filter((r: any) => r instanceof HttpResponse),
      map((r: HttpResponse<any>) => {
        return (r as HttpResponse<any>).clone({body: undefined}) as StrictHttpResponse<void>;
      })
    );
  }

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to only to the response body.
   * To access the full response (for headers, for example), `emailControllerClearQueues$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  emailControllerClearQueues(params?: {
                               context?: HttpContext
                             }
  ): Observable<void> {

    return this.emailControllerClearQueues$Response(params).pipe(
      map((r: StrictHttpResponse<void>) => r.body as void)
    );
  }

}
