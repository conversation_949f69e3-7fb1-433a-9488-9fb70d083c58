/* tslint:disable */
/* eslint-disable */
import { Injectable } from '@angular/core';
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';
import { RequestBuilder } from '../request-builder';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';

import { UserAccountUpdatedDto } from '../models/user-account-updated-dto';

@Injectable({
  providedIn: 'root',
})
export class UserService extends BaseService {
  constructor(
    config: ApiConfiguration,
    http: HttpClient
  ) {
    super(config, http);
  }

  /**
   * Path part for operation emailControllerSendUserAccountCreatedEmail
   */
  static readonly EmailControllerSendUserAccountCreatedEmailPath = '/email/bankKey/{bankKey}/user/{userId}/password/{password}/created';

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `emailControllerSendUserAccountCreatedEmail()` instead.
   *
   * This method doesn't expect any request body.
   */
  emailControllerSendUserAccountCreatedEmail$Response(params: {
                                                        userId: string;
                                                        bankKey: string;
                                                        password: string;
                                                        context?: HttpContext
                                                      }
  ): Observable<StrictHttpResponse<void>> {

    const rb = new RequestBuilder(this.rootUrl, UserService.EmailControllerSendUserAccountCreatedEmailPath, 'post');
    if (params) {
      rb.path('userId', params.userId, {});
      rb.path('bankKey', params.bankKey, {});
      rb.path('password', params.password, {});
    }

    return this.http.request(rb.build({
      responseType: 'text',
      accept: '*/*',
      context: params?.context
    })).pipe(
      filter((r: any) => r instanceof HttpResponse),
      map((r: HttpResponse<any>) => {
        return (r as HttpResponse<any>).clone({body: undefined}) as StrictHttpResponse<void>;
      })
    );
  }

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to only to the response body.
   * To access the full response (for headers, for example), `emailControllerSendUserAccountCreatedEmail$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  emailControllerSendUserAccountCreatedEmail(params: {
                                               userId: string;
                                               bankKey: string;
                                               password: string;
                                               context?: HttpContext
                                             }
  ): Observable<void> {

    return this.emailControllerSendUserAccountCreatedEmail$Response(params).pipe(
      map((r: StrictHttpResponse<void>) => r.body as void)
    );
  }

  /**
   * Path part for operation emailControllerSendUserAccountUpdatedEmail
   */
  static readonly EmailControllerSendUserAccountUpdatedEmailPath = '/email/user/updated';

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `emailControllerSendUserAccountUpdatedEmail()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  emailControllerSendUserAccountUpdatedEmail$Response(params: {
                                                        context?: HttpContext
                                                        body: UserAccountUpdatedDto
                                                      }
  ): Observable<StrictHttpResponse<void>> {

    const rb = new RequestBuilder(this.rootUrl, UserService.EmailControllerSendUserAccountUpdatedEmailPath, 'post');
    if (params) {
      rb.body(params.body, 'application/json');
    }

    return this.http.request(rb.build({
      responseType: 'text',
      accept: '*/*',
      context: params?.context
    })).pipe(
      filter((r: any) => r instanceof HttpResponse),
      map((r: HttpResponse<any>) => {
        return (r as HttpResponse<any>).clone({body: undefined}) as StrictHttpResponse<void>;
      })
    );
  }

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to only to the response body.
   * To access the full response (for headers, for example), `emailControllerSendUserAccountUpdatedEmail$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  emailControllerSendUserAccountUpdatedEmail(params: {
                                               context?: HttpContext
                                               body: UserAccountUpdatedDto
                                             }
  ): Observable<void> {

    return this.emailControllerSendUserAccountUpdatedEmail$Response(params).pipe(
      map((r: StrictHttpResponse<void>) => r.body as void)
    );
  }

}
