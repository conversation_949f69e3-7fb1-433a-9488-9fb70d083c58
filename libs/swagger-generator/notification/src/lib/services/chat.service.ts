/* tslint:disable */
/* eslint-disable */
import { Injectable } from '@angular/core';
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';
import { RequestBuilder } from '../request-builder';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';

import { KafkaMessageDto } from '../models/kafka-message-dto';
import { UserEmailNotificationDto } from '../models/user-email-notification-dto';

@Injectable({
  providedIn: 'root',
})
export class ChatService extends BaseService {
  constructor(
    config: ApiConfiguration,
    http: HttpClient
  ) {
    super(config, http);
  }

  /**
   * Path part for operation emailControllerSendChatMessageEmail
   */
  static readonly EmailControllerSendChatMessageEmailPath = '/email/consortium/chat';

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `emailControllerSendChatMessageEmail()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  emailControllerSendChatMessageEmail$Response(params: {
                                                 context?: HttpContext
                                                 body: KafkaMessageDto
                                               }
  ): Observable<StrictHttpResponse<void>> {

    const rb = new RequestBuilder(this.rootUrl, ChatService.EmailControllerSendChatMessageEmailPath, 'post');
    if (params) {
      rb.body(params.body, 'application/json');
    }

    return this.http.request(rb.build({
      responseType: 'text',
      accept: '*/*',
      context: params?.context
    })).pipe(
      filter((r: any) => r instanceof HttpResponse),
      map((r: HttpResponse<any>) => {
        return (r as HttpResponse<any>).clone({body: undefined}) as StrictHttpResponse<void>;
      })
    );
  }

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to only to the response body.
   * To access the full response (for headers, for example), `emailControllerSendChatMessageEmail$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  emailControllerSendChatMessageEmail(params: {
                                        context?: HttpContext
                                        body: KafkaMessageDto
                                      }
  ): Observable<void> {

    return this.emailControllerSendChatMessageEmail$Response(params).pipe(
      map((r: StrictHttpResponse<void>) => r.body as void)
    );
  }

  /**
   * Path part for operation emailControllerSendBusinessCaseTaggedUsersEmail
   */
  static readonly EmailControllerSendBusinessCaseTaggedUsersEmailPath = '/email/consortium/chat/tagged-users';

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `emailControllerSendBusinessCaseTaggedUsersEmail()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  emailControllerSendBusinessCaseTaggedUsersEmail$Response(params: {
                                                           context?: HttpContext
                                                           body: UserEmailNotificationDto
                                                         }
  ): Observable<StrictHttpResponse<void>> {

    const rb = new RequestBuilder(this.rootUrl, ChatService.EmailControllerSendBusinessCaseTaggedUsersEmailPath, 'post');
    if (params) {
      rb.body(params.body, 'application/json');
    }

    return this.http.request(rb.build({
      responseType: 'text',
      accept: '*/*',
      context: params?.context
    })).pipe(
      filter((r: any) => r instanceof HttpResponse),
      map((r: HttpResponse<any>) => {
        return (r as HttpResponse<any>).clone({body: undefined}) as StrictHttpResponse<void>;
      })
    );
  }

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to only to the response body.
   * To access the full response (for headers, for example), `emailControllerSendBusinessCaseTaggedUsersEmail$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  emailControllerSendBusinessCaseTaggedUsersEmail(params: {
                                                  context?: HttpContext
                                                  body: UserEmailNotificationDto
                                                }
  ): Observable<void> {

    return this.emailControllerSendBusinessCaseTaggedUsersEmail$Response(params).pipe(
      map((r: StrictHttpResponse<void>) => r.body as void)
    );
  }

  /**
   * Path part for operation emailControllerCancelBusinessCaseNewMessageEmail
   */
  static readonly EmailControllerCancelBusinessCaseNewMessageEmailPath = '/email/consortium/chat/cancel';

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `emailControllerCancelBusinessCaseNewMessageEmail()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  emailControllerCancelBusinessCaseNewMessageEmail$Response(params: {
                                                            context?: HttpContext
                                                            body: KafkaMessageDto
                                                          }
  ): Observable<StrictHttpResponse<void>> {

    const rb = new RequestBuilder(this.rootUrl, ChatService.EmailControllerCancelBusinessCaseNewMessageEmailPath, 'post');
    if (params) {
      rb.body(params.body, 'application/json');
    }

    return this.http.request(rb.build({
      responseType: 'text',
      accept: '*/*',
      context: params?.context
    })).pipe(
      filter((r: any) => r instanceof HttpResponse),
      map((r: HttpResponse<any>) => {
        return (r as HttpResponse<any>).clone({body: undefined}) as StrictHttpResponse<void>;
      })
    );
  }

  /**
   * FOR DEBUGGING ONLY!
   *
   * This method provides access to only to the response body.
   * To access the full response (for headers, for example), `emailControllerCancelBusinessCaseNewMessageEmail$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  emailControllerCancelBusinessCaseNewMessageEmail(params: {
                                                   context?: HttpContext
                                                   body: KafkaMessageDto
                                                 }
  ): Observable<void> {

    return this.emailControllerCancelBusinessCaseNewMessageEmail$Response(params).pipe(
      map((r: StrictHttpResponse<void>) => r.body as void)
    );
  }

}
