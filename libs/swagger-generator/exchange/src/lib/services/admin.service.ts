/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { adminControllerDeleteAllExchangeBusinessCases } from '../fn/admin/admin-controller-delete-all-exchange-business-cases';
import { AdminControllerDeleteAllExchangeBusinessCases$Params } from '../fn/admin/admin-controller-delete-all-exchange-business-cases';
import { adminControllerReinitializeExchangeBusinessCases } from '../fn/admin/admin-controller-reinitialize-exchange-business-cases';
import { AdminControllerReinitializeExchangeBusinessCases$Params } from '../fn/admin/admin-controller-reinitialize-exchange-business-cases';

@Injectable({ providedIn: 'root' })
export class AdminService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `adminControllerReinitializeExchangeBusinessCases()` */
  static readonly AdminControllerReinitializeExchangeBusinessCasesPath = '/admin/exchange-business-cases/reinitialize';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `adminControllerReinitializeExchangeBusinessCases()` instead.
   *
   * This method doesn't expect any request body.
   */
  adminControllerReinitializeExchangeBusinessCases$Response(params?: AdminControllerReinitializeExchangeBusinessCases$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return adminControllerReinitializeExchangeBusinessCases(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `adminControllerReinitializeExchangeBusinessCases$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  adminControllerReinitializeExchangeBusinessCases(params?: AdminControllerReinitializeExchangeBusinessCases$Params, context?: HttpContext): Observable<void> {
    return this.adminControllerReinitializeExchangeBusinessCases$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `adminControllerDeleteAllExchangeBusinessCases()` */
  static readonly AdminControllerDeleteAllExchangeBusinessCasesPath = '/admin/exchange-business-cases';

  /**
   * <b>WARNING: </b> This will delete all data. Use with caution.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `adminControllerDeleteAllExchangeBusinessCases()` instead.
   *
   * This method doesn't expect any request body.
   */
  adminControllerDeleteAllExchangeBusinessCases$Response(params?: AdminControllerDeleteAllExchangeBusinessCases$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return adminControllerDeleteAllExchangeBusinessCases(this.http, this.rootUrl, params, context);
  }

  /**
   * <b>WARNING: </b> This will delete all data. Use with caution.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `adminControllerDeleteAllExchangeBusinessCases$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  adminControllerDeleteAllExchangeBusinessCases(params?: AdminControllerDeleteAllExchangeBusinessCases$Params, context?: HttpContext): Observable<void> {
    return this.adminControllerDeleteAllExchangeBusinessCases$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
