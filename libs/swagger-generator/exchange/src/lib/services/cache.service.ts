/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { cacheControllerGetCachedValueByKey } from '../fn/cache/cache-controller-get-cached-value-by-key';
import { CacheControllerGetCachedValueByKey$Params } from '../fn/cache/cache-controller-get-cached-value-by-key';
import { cacheControllerGetCachedValues } from '../fn/cache/cache-controller-get-cached-values';
import { CacheControllerGetCachedValues$Params } from '../fn/cache/cache-controller-get-cached-values';

@Injectable({ providedIn: 'root' })
export class CacheService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `cacheControllerGetCachedValueByKey()` */
  static readonly CacheControllerGetCachedValueByKeyPath = '/cache/byKey/{key}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `cacheControllerGetCachedValueByKey()` instead.
   *
   * This method doesn't expect any request body.
   */
  cacheControllerGetCachedValueByKey$Response(params: CacheControllerGetCachedValueByKey$Params, context?: HttpContext): Observable<StrictHttpResponse<{
}>> {
    return cacheControllerGetCachedValueByKey(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `cacheControllerGetCachedValueByKey$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  cacheControllerGetCachedValueByKey(params: CacheControllerGetCachedValueByKey$Params, context?: HttpContext): Observable<{
}> {
    return this.cacheControllerGetCachedValueByKey$Response(params, context).pipe(
      map((r: StrictHttpResponse<{
}>): {
} => r.body)
    );
  }

  /** Path part for operation `cacheControllerGetCachedValues()` */
  static readonly CacheControllerGetCachedValuesPath = '/cache/allKeys';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `cacheControllerGetCachedValues()` instead.
   *
   * This method doesn't expect any request body.
   */
  cacheControllerGetCachedValues$Response(params?: CacheControllerGetCachedValues$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return cacheControllerGetCachedValues(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `cacheControllerGetCachedValues$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  cacheControllerGetCachedValues(params?: CacheControllerGetCachedValues$Params, context?: HttpContext): Observable<void> {
    return this.cacheControllerGetCachedValues$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
