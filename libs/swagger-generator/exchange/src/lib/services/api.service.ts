/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { prometheusControllerIndex } from '../fn/operations/prometheus-controller-index';
import { PrometheusControllerIndex$Params } from '../fn/operations/prometheus-controller-index';
import { readinessProbeControllerIsReady } from '../fn/operations/readiness-probe-controller-is-ready';
import { ReadinessProbeControllerIsReady$Params } from '../fn/operations/readiness-probe-controller-is-ready';

@Injectable({ providedIn: 'root' })
export class ApiService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `prometheusControllerIndex()` */
  static readonly PrometheusControllerIndexPath = '/metrics';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `prometheusControllerIndex()` instead.
   *
   * This method doesn't expect any request body.
   */
  prometheusControllerIndex$Response(params?: PrometheusControllerIndex$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return prometheusControllerIndex(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `prometheusControllerIndex$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  prometheusControllerIndex(params?: PrometheusControllerIndex$Params, context?: HttpContext): Observable<void> {
    return this.prometheusControllerIndex$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `readinessProbeControllerIsReady()` */
  static readonly ReadinessProbeControllerIsReadyPath = '/ready';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `readinessProbeControllerIsReady()` instead.
   *
   * This method doesn't expect any request body.
   */
  readinessProbeControllerIsReady$Response(params?: ReadinessProbeControllerIsReady$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return readinessProbeControllerIsReady(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `readinessProbeControllerIsReady$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  readinessProbeControllerIsReady(params?: ReadinessProbeControllerIsReady$Params, context?: HttpContext): Observable<void> {
    return this.readinessProbeControllerIsReady$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
