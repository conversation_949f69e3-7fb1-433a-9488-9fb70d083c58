/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { UserUpdatedLog } from '../models/user-updated-log';
import { userUpdatedLogControllerLoadLatestUserUpdatedLog } from '../fn/user-updated-log/user-updated-log-controller-load-latest-user-updated-log';
import { UserUpdatedLogControllerLoadLatestUserUpdatedLog$Params } from '../fn/user-updated-log/user-updated-log-controller-load-latest-user-updated-log';
import { userUpdatedLogControllerLoadUserUpdatedLogs } from '../fn/user-updated-log/user-updated-log-controller-load-user-updated-logs';
import { UserUpdatedLogControllerLoadUserUpdatedLogs$Params } from '../fn/user-updated-log/user-updated-log-controller-load-user-updated-logs';
import { UserUpdatedLogDto } from '../models/user-updated-log-dto';

@Injectable({ providedIn: 'root' })
export class UserUpdatedLogService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `userUpdatedLogControllerLoadUserUpdatedLogs()` */
  static readonly UserUpdatedLogControllerLoadUserUpdatedLogsPath = '/user-updated-log';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `userUpdatedLogControllerLoadUserUpdatedLogs()` instead.
   *
   * This method doesn't expect any request body.
   */
  userUpdatedLogControllerLoadUserUpdatedLogs$Response(params?: UserUpdatedLogControllerLoadUserUpdatedLogs$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<UserUpdatedLog>>> {
    return userUpdatedLogControllerLoadUserUpdatedLogs(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `userUpdatedLogControllerLoadUserUpdatedLogs$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  userUpdatedLogControllerLoadUserUpdatedLogs(params?: UserUpdatedLogControllerLoadUserUpdatedLogs$Params, context?: HttpContext): Observable<Array<UserUpdatedLog>> {
    return this.userUpdatedLogControllerLoadUserUpdatedLogs$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<UserUpdatedLog>>): Array<UserUpdatedLog> => r.body)
    );
  }

  /** Path part for operation `userUpdatedLogControllerLoadLatestUserUpdatedLog()` */
  static readonly UserUpdatedLogControllerLoadLatestUserUpdatedLogPath = '/user-updated-log/latest';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `userUpdatedLogControllerLoadLatestUserUpdatedLog()` instead.
   *
   * This method doesn't expect any request body.
   */
  userUpdatedLogControllerLoadLatestUserUpdatedLog$Response(params?: UserUpdatedLogControllerLoadLatestUserUpdatedLog$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<UserUpdatedLogDto>>> {
    return userUpdatedLogControllerLoadLatestUserUpdatedLog(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `userUpdatedLogControllerLoadLatestUserUpdatedLog$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  userUpdatedLogControllerLoadLatestUserUpdatedLog(params?: UserUpdatedLogControllerLoadLatestUserUpdatedLog$Params, context?: HttpContext): Observable<Array<UserUpdatedLogDto>> {
    return this.userUpdatedLogControllerLoadLatestUserUpdatedLog$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<UserUpdatedLogDto>>): Array<UserUpdatedLogDto> => r.body)
    );
  }

}
