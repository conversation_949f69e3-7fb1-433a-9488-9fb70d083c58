/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { BusinessCaseBasicInfoDto } from '../models/business-case-basic-info-dto';
import { BusinessCaseInfoDto } from '../models/business-case-info-dto';
import { BusinessCaseResultDto } from '../models/business-case-result-dto';
import { CustomerBusinessCasesStatisticsDto } from '../models/customer-business-cases-statistics-dto';
import { ExchangeBusinessCase } from '../models/exchange-business-case';
import { exchangeControllerFindById } from '../fn/exchange/exchange-controller-find-by-id';
import { ExchangeControllerFindById$Params } from '../fn/exchange/exchange-controller-find-by-id';
import { exchangeControllerGetAll } from '../fn/exchange/exchange-controller-get-all';
import { ExchangeControllerGetAll$Params } from '../fn/exchange/exchange-controller-get-all';
import { exchangeControllerGetCustomerBusinessCasesStatistics } from '../fn/exchange/exchange-controller-get-customer-business-cases-statistics';
import { ExchangeControllerGetCustomerBusinessCasesStatistics$Params } from '../fn/exchange/exchange-controller-get-customer-business-cases-statistics';
import { exchangeControllerGetIdsByCustomerKey } from '../fn/exchange/exchange-controller-get-ids-by-customer-key';
import { ExchangeControllerGetIdsByCustomerKey$Params } from '../fn/exchange/exchange-controller-get-ids-by-customer-key';
import { exchangeControllerLoadBusinessCasesBasicInfoByIds } from '../fn/exchange/exchange-controller-load-business-cases-basic-info-by-ids';
import { ExchangeControllerLoadBusinessCasesBasicInfoByIds$Params } from '../fn/exchange/exchange-controller-load-business-cases-basic-info-by-ids';
import { exchangeControllerLoadBusinessCasesByCompanyId } from '../fn/exchange/exchange-controller-load-business-cases-by-company-id';
import { ExchangeControllerLoadBusinessCasesByCompanyId$Params } from '../fn/exchange/exchange-controller-load-business-cases-by-company-id';
import { exchangeControllerLoadBusinessCasesByIds } from '../fn/exchange/exchange-controller-load-business-cases-by-ids';
import { ExchangeControllerLoadBusinessCasesByIds$Params } from '../fn/exchange/exchange-controller-load-business-cases-by-ids';
import { exchangeControllerLoadCustomerActiveBusinessCases } from '../fn/exchange/exchange-controller-load-customer-active-business-cases';
import { ExchangeControllerLoadCustomerActiveBusinessCases$Params } from '../fn/exchange/exchange-controller-load-customer-active-business-cases';
import { exchangeControllerLoadMyBusinessCases } from '../fn/exchange/exchange-controller-load-my-business-cases';
import { ExchangeControllerLoadMyBusinessCases$Params } from '../fn/exchange/exchange-controller-load-my-business-cases';
import { exchangeControllerLoadMyOrganizationBusinessCases } from '../fn/exchange/exchange-controller-load-my-organization-business-cases';
import { ExchangeControllerLoadMyOrganizationBusinessCases$Params } from '../fn/exchange/exchange-controller-load-my-organization-business-cases';
import { exchangeControllerLoadMyOrganizationBusinessCasesAsLead } from '../fn/exchange/exchange-controller-load-my-organization-business-cases-as-lead';
import { ExchangeControllerLoadMyOrganizationBusinessCasesAsLead$Params } from '../fn/exchange/exchange-controller-load-my-organization-business-cases-as-lead';
import { exchangeControllerSearch } from '../fn/exchange/exchange-controller-search';
import { ExchangeControllerSearch$Params } from '../fn/exchange/exchange-controller-search';
import { SwaggerCustomerBusinessCaseResultsDto } from '../models/swagger-customer-business-case-results-dto';
import { SwaggerSearchBusinessCaseResultsDto } from '../models/swagger-search-business-case-results-dto';
import { SwaggerUserBusinessCaseResultsDto } from '../models/swagger-user-business-case-results-dto';

@Injectable({ providedIn: 'root' })
export class ExchangeService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `exchangeControllerSearch()` */
  static readonly ExchangeControllerSearchPath = '/exchange/search';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `exchangeControllerSearch()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  exchangeControllerSearch$Response(params: ExchangeControllerSearch$Params, context?: HttpContext): Observable<StrictHttpResponse<SwaggerSearchBusinessCaseResultsDto>> {
    return exchangeControllerSearch(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `exchangeControllerSearch$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  exchangeControllerSearch(params: ExchangeControllerSearch$Params, context?: HttpContext): Observable<SwaggerSearchBusinessCaseResultsDto> {
    return this.exchangeControllerSearch$Response(params, context).pipe(
      map((r: StrictHttpResponse<SwaggerSearchBusinessCaseResultsDto>): SwaggerSearchBusinessCaseResultsDto => r.body)
    );
  }

  /** Path part for operation `exchangeControllerGetAll()` */
  static readonly ExchangeControllerGetAllPath = '/exchange/business-case';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `exchangeControllerGetAll()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerGetAll$Response(params?: ExchangeControllerGetAll$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<BusinessCaseInfoDto>>> {
    return exchangeControllerGetAll(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `exchangeControllerGetAll$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerGetAll(params?: ExchangeControllerGetAll$Params, context?: HttpContext): Observable<Array<BusinessCaseInfoDto>> {
    return this.exchangeControllerGetAll$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<BusinessCaseInfoDto>>): Array<BusinessCaseInfoDto> => r.body)
    );
  }

  /** Path part for operation `exchangeControllerLoadMyOrganizationBusinessCases()` */
  static readonly ExchangeControllerLoadMyOrganizationBusinessCasesPath = '/exchange/business-case/my-organization';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `exchangeControllerLoadMyOrganizationBusinessCases()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerLoadMyOrganizationBusinessCases$Response(params?: ExchangeControllerLoadMyOrganizationBusinessCases$Params, context?: HttpContext): Observable<StrictHttpResponse<SwaggerUserBusinessCaseResultsDto>> {
    return exchangeControllerLoadMyOrganizationBusinessCases(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `exchangeControllerLoadMyOrganizationBusinessCases$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerLoadMyOrganizationBusinessCases(params?: ExchangeControllerLoadMyOrganizationBusinessCases$Params, context?: HttpContext): Observable<SwaggerUserBusinessCaseResultsDto> {
    return this.exchangeControllerLoadMyOrganizationBusinessCases$Response(params, context).pipe(
      map((r: StrictHttpResponse<SwaggerUserBusinessCaseResultsDto>): SwaggerUserBusinessCaseResultsDto => r.body)
    );
  }

  /** Path part for operation `exchangeControllerLoadMyBusinessCases()` */
  static readonly ExchangeControllerLoadMyBusinessCasesPath = '/exchange/business-case/my-cases';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `exchangeControllerLoadMyBusinessCases()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerLoadMyBusinessCases$Response(params?: ExchangeControllerLoadMyBusinessCases$Params, context?: HttpContext): Observable<StrictHttpResponse<SwaggerUserBusinessCaseResultsDto>> {
    return exchangeControllerLoadMyBusinessCases(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `exchangeControllerLoadMyBusinessCases$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerLoadMyBusinessCases(params?: ExchangeControllerLoadMyBusinessCases$Params, context?: HttpContext): Observable<SwaggerUserBusinessCaseResultsDto> {
    return this.exchangeControllerLoadMyBusinessCases$Response(params, context).pipe(
      map((r: StrictHttpResponse<SwaggerUserBusinessCaseResultsDto>): SwaggerUserBusinessCaseResultsDto => r.body)
    );
  }

  /** Path part for operation `exchangeControllerLoadMyOrganizationBusinessCasesAsLead()` */
  static readonly ExchangeControllerLoadMyOrganizationBusinessCasesAsLeadPath = '/exchange/business-case/leadCustomer';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `exchangeControllerLoadMyOrganizationBusinessCasesAsLead()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerLoadMyOrganizationBusinessCasesAsLead$Response(params?: ExchangeControllerLoadMyOrganizationBusinessCasesAsLead$Params, context?: HttpContext): Observable<StrictHttpResponse<SwaggerCustomerBusinessCaseResultsDto>> {
    return exchangeControllerLoadMyOrganizationBusinessCasesAsLead(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `exchangeControllerLoadMyOrganizationBusinessCasesAsLead$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerLoadMyOrganizationBusinessCasesAsLead(params?: ExchangeControllerLoadMyOrganizationBusinessCasesAsLead$Params, context?: HttpContext): Observable<SwaggerCustomerBusinessCaseResultsDto> {
    return this.exchangeControllerLoadMyOrganizationBusinessCasesAsLead$Response(params, context).pipe(
      map((r: StrictHttpResponse<SwaggerCustomerBusinessCaseResultsDto>): SwaggerCustomerBusinessCaseResultsDto => r.body)
    );
  }

  /** Path part for operation `exchangeControllerLoadCustomerActiveBusinessCases()` */
  static readonly ExchangeControllerLoadCustomerActiveBusinessCasesPath = '/exchange/business-case/customer/{customerKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `exchangeControllerLoadCustomerActiveBusinessCases()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerLoadCustomerActiveBusinessCases$Response(params: ExchangeControllerLoadCustomerActiveBusinessCases$Params, context?: HttpContext): Observable<StrictHttpResponse<SwaggerCustomerBusinessCaseResultsDto>> {
    return exchangeControllerLoadCustomerActiveBusinessCases(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `exchangeControllerLoadCustomerActiveBusinessCases$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerLoadCustomerActiveBusinessCases(params: ExchangeControllerLoadCustomerActiveBusinessCases$Params, context?: HttpContext): Observable<SwaggerCustomerBusinessCaseResultsDto> {
    return this.exchangeControllerLoadCustomerActiveBusinessCases$Response(params, context).pipe(
      map((r: StrictHttpResponse<SwaggerCustomerBusinessCaseResultsDto>): SwaggerCustomerBusinessCaseResultsDto => r.body)
    );
  }

  /** Path part for operation `exchangeControllerGetCustomerBusinessCasesStatistics()` */
  static readonly ExchangeControllerGetCustomerBusinessCasesStatisticsPath = '/exchange/business-case/customer/{customerKey}/statistics';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `exchangeControllerGetCustomerBusinessCasesStatistics()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerGetCustomerBusinessCasesStatistics$Response(params: ExchangeControllerGetCustomerBusinessCasesStatistics$Params, context?: HttpContext): Observable<StrictHttpResponse<CustomerBusinessCasesStatisticsDto>> {
    return exchangeControllerGetCustomerBusinessCasesStatistics(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `exchangeControllerGetCustomerBusinessCasesStatistics$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerGetCustomerBusinessCasesStatistics(params: ExchangeControllerGetCustomerBusinessCasesStatistics$Params, context?: HttpContext): Observable<CustomerBusinessCasesStatisticsDto> {
    return this.exchangeControllerGetCustomerBusinessCasesStatistics$Response(params, context).pipe(
      map((r: StrictHttpResponse<CustomerBusinessCasesStatisticsDto>): CustomerBusinessCasesStatisticsDto => r.body)
    );
  }

  /** Path part for operation `exchangeControllerFindById()` */
  static readonly ExchangeControllerFindByIdPath = '/exchange/business-case/{id}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `exchangeControllerFindById()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerFindById$Response(params: ExchangeControllerFindById$Params, context?: HttpContext): Observable<StrictHttpResponse<ExchangeBusinessCase>> {
    return exchangeControllerFindById(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `exchangeControllerFindById$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerFindById(params: ExchangeControllerFindById$Params, context?: HttpContext): Observable<ExchangeBusinessCase> {
    return this.exchangeControllerFindById$Response(params, context).pipe(
      map((r: StrictHttpResponse<ExchangeBusinessCase>): ExchangeBusinessCase => r.body)
    );
  }

  /** Path part for operation `exchangeControllerLoadBusinessCasesByCompanyId()` */
  static readonly ExchangeControllerLoadBusinessCasesByCompanyIdPath = '/exchange/business-case/company/{companyId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `exchangeControllerLoadBusinessCasesByCompanyId()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerLoadBusinessCasesByCompanyId$Response(params: ExchangeControllerLoadBusinessCasesByCompanyId$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<ExchangeBusinessCase>>> {
    return exchangeControllerLoadBusinessCasesByCompanyId(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `exchangeControllerLoadBusinessCasesByCompanyId$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerLoadBusinessCasesByCompanyId(params: ExchangeControllerLoadBusinessCasesByCompanyId$Params, context?: HttpContext): Observable<Array<ExchangeBusinessCase>> {
    return this.exchangeControllerLoadBusinessCasesByCompanyId$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<ExchangeBusinessCase>>): Array<ExchangeBusinessCase> => r.body)
    );
  }

  /** Path part for operation `exchangeControllerLoadBusinessCasesByIds()` */
  static readonly ExchangeControllerLoadBusinessCasesByIdsPath = '/exchange/business-case/byIds';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `exchangeControllerLoadBusinessCasesByIds()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  exchangeControllerLoadBusinessCasesByIds$Response(params: ExchangeControllerLoadBusinessCasesByIds$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<BusinessCaseResultDto>>> {
    return exchangeControllerLoadBusinessCasesByIds(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `exchangeControllerLoadBusinessCasesByIds$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  exchangeControllerLoadBusinessCasesByIds(params: ExchangeControllerLoadBusinessCasesByIds$Params, context?: HttpContext): Observable<Array<BusinessCaseResultDto>> {
    return this.exchangeControllerLoadBusinessCasesByIds$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<BusinessCaseResultDto>>): Array<BusinessCaseResultDto> => r.body)
    );
  }

  /** Path part for operation `exchangeControllerLoadBusinessCasesBasicInfoByIds()` */
  static readonly ExchangeControllerLoadBusinessCasesBasicInfoByIdsPath = '/exchange/business-case/byIds/basicInfo';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `exchangeControllerLoadBusinessCasesBasicInfoByIds()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  exchangeControllerLoadBusinessCasesBasicInfoByIds$Response(params: ExchangeControllerLoadBusinessCasesBasicInfoByIds$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<BusinessCaseBasicInfoDto>>> {
    return exchangeControllerLoadBusinessCasesBasicInfoByIds(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `exchangeControllerLoadBusinessCasesBasicInfoByIds$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  exchangeControllerLoadBusinessCasesBasicInfoByIds(params: ExchangeControllerLoadBusinessCasesBasicInfoByIds$Params, context?: HttpContext): Observable<Array<BusinessCaseBasicInfoDto>> {
    return this.exchangeControllerLoadBusinessCasesBasicInfoByIds$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<BusinessCaseBasicInfoDto>>): Array<BusinessCaseBasicInfoDto> => r.body)
    );
  }

  /** Path part for operation `exchangeControllerGetIdsByCustomerKey()` */
  static readonly ExchangeControllerGetIdsByCustomerKeyPath = '/exchange/business-case/ids/{customerKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `exchangeControllerGetIdsByCustomerKey()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerGetIdsByCustomerKey$Response(params: ExchangeControllerGetIdsByCustomerKey$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return exchangeControllerGetIdsByCustomerKey(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `exchangeControllerGetIdsByCustomerKey$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  exchangeControllerGetIdsByCustomerKey(params: ExchangeControllerGetIdsByCustomerKey$Params, context?: HttpContext): Observable<Array<string>> {
    return this.exchangeControllerGetIdsByCustomerKey$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

}
