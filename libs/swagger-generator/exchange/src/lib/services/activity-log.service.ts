/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { activityLogControllerLoadActivityLogsForBusinessCase } from '../fn/activity-log/activity-log-controller-load-activity-logs-for-business-case';
import { ActivityLogControllerLoadActivityLogsForBusinessCase$Params } from '../fn/activity-log/activity-log-controller-load-activity-logs-for-business-case';
import { activityLogControllerLoadLatestActivityLogForBusinessCase } from '../fn/activity-log/activity-log-controller-load-latest-activity-log-for-business-case';
import { ActivityLogControllerLoadLatestActivityLogForBusinessCase$Params } from '../fn/activity-log/activity-log-controller-load-latest-activity-log-for-business-case';
import { ActivityLogDto } from '../models/activity-log-dto';

@Injectable({ providedIn: 'root' })
export class ActivityLogService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `activityLogControllerLoadActivityLogsForBusinessCase()` */
  static readonly ActivityLogControllerLoadActivityLogsForBusinessCasePath = '/activity-log/{businessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `activityLogControllerLoadActivityLogsForBusinessCase()` instead.
   *
   * This method doesn't expect any request body.
   */
  activityLogControllerLoadActivityLogsForBusinessCase$Response(params: ActivityLogControllerLoadActivityLogsForBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<ActivityLogDto>>> {
    return activityLogControllerLoadActivityLogsForBusinessCase(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `activityLogControllerLoadActivityLogsForBusinessCase$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  activityLogControllerLoadActivityLogsForBusinessCase(params: ActivityLogControllerLoadActivityLogsForBusinessCase$Params, context?: HttpContext): Observable<Array<ActivityLogDto>> {
    return this.activityLogControllerLoadActivityLogsForBusinessCase$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<ActivityLogDto>>): Array<ActivityLogDto> => r.body)
    );
  }

  /** Path part for operation `activityLogControllerLoadLatestActivityLogForBusinessCase()` */
  static readonly ActivityLogControllerLoadLatestActivityLogForBusinessCasePath = '/activity-log/{businessCaseId}/latest';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `activityLogControllerLoadLatestActivityLogForBusinessCase()` instead.
   *
   * This method doesn't expect any request body.
   */
  activityLogControllerLoadLatestActivityLogForBusinessCase$Response(params: ActivityLogControllerLoadLatestActivityLogForBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<ActivityLogDto>>> {
    return activityLogControllerLoadLatestActivityLogForBusinessCase(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `activityLogControllerLoadLatestActivityLogForBusinessCase$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  activityLogControllerLoadLatestActivityLogForBusinessCase(params: ActivityLogControllerLoadLatestActivityLogForBusinessCase$Params, context?: HttpContext): Observable<Array<ActivityLogDto>> {
    return this.activityLogControllerLoadLatestActivityLogForBusinessCase$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<ActivityLogDto>>): Array<ActivityLogDto> => r.body)
    );
  }

}
