/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { ActivityLogDto } from '../models/activity-log-dto';
import { applicationsInvitationsStatusHistoryControllerGetAllForBusinessCase } from '../fn/applications-invitations-history/applications-invitations-status-history-controller-get-all-for-business-case';
import { ApplicationsInvitationsStatusHistoryControllerGetAllForBusinessCase$Params } from '../fn/applications-invitations-history/applications-invitations-status-history-controller-get-all-for-business-case';

@Injectable({ providedIn: 'root' })
export class ApplicationsInvitationsHistoryService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `applicationsInvitationsStatusHistoryControllerGetAllForBusinessCase()` */
  static readonly ApplicationsInvitationsStatusHistoryControllerGetAllForBusinessCasePath = '/applications-invitations-history/{businessCaseId}/customer/{customerKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `applicationsInvitationsStatusHistoryControllerGetAllForBusinessCase()` instead.
   *
   * This method doesn't expect any request body.
   */
  applicationsInvitationsStatusHistoryControllerGetAllForBusinessCase$Response(params: ApplicationsInvitationsStatusHistoryControllerGetAllForBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<ActivityLogDto>>> {
    return applicationsInvitationsStatusHistoryControllerGetAllForBusinessCase(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `applicationsInvitationsStatusHistoryControllerGetAllForBusinessCase$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  applicationsInvitationsStatusHistoryControllerGetAllForBusinessCase(params: ApplicationsInvitationsStatusHistoryControllerGetAllForBusinessCase$Params, context?: HttpContext): Observable<Array<ActivityLogDto>> {
    return this.applicationsInvitationsStatusHistoryControllerGetAllForBusinessCase$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<ActivityLogDto>>): Array<ActivityLogDto> => r.body)
    );
  }

}
