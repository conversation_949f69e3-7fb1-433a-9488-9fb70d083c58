/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { updateFloatingDocumentWithCustomerKey } from '../fn/update-floating-document-with-customer-key-migration/update-floating-document-with-customer-key';
import { UpdateFloatingDocumentWithCustomerKey$Params } from '../fn/update-floating-document-with-customer-key-migration/update-floating-document-with-customer-key';

@Injectable({ providedIn: 'root' })
export class UpdateFloatingDocumentWithCustomerKeyMigrationService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `updateFloatingDocumentWithCustomerKey()` */
  static readonly UpdateFloatingDocumentWithCustomerKeyPath = '/migration/update_floating_documents_with_customerKey';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `updateFloatingDocumentWithCustomerKey()` instead.
   *
   * This method doesn't expect any request body.
   */
  updateFloatingDocumentWithCustomerKey$Response(params: UpdateFloatingDocumentWithCustomerKey$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return updateFloatingDocumentWithCustomerKey(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `updateFloatingDocumentWithCustomerKey$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  updateFloatingDocumentWithCustomerKey(params: UpdateFloatingDocumentWithCustomerKey$Params, context?: HttpContext): Observable<Array<string>> {
    return this.updateFloatingDocumentWithCustomerKey$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

}
