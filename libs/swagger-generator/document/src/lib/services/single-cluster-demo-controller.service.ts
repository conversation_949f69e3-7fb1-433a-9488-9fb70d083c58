/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { deploy } from '../fn/single-cluster-demo-controller/deploy';
import { Deploy$Params } from '../fn/single-cluster-demo-controller/deploy';
import { DocumentEntity } from '../models/document-entity';
import { getAllCaseDocumentsForBusinessCaseDemo } from '../fn/single-cluster-demo-controller/get-all-case-documents-for-business-case-demo';
import { GetAllCaseDocumentsForBusinessCaseDemo$Params } from '../fn/single-cluster-demo-controller/get-all-case-documents-for-business-case-demo';
import { getAllCompanyDocumentsDemo } from '../fn/single-cluster-demo-controller/get-all-company-documents-demo';
import { GetAllCompanyDocumentsDemo$Params } from '../fn/single-cluster-demo-controller/get-all-company-documents-demo';
import { getAllInboxDocumentsForBusinessCase1 } from '../fn/single-cluster-demo-controller/get-all-inbox-documents-for-business-case-1';
import { GetAllInboxDocumentsForBusinessCase1$Params } from '../fn/single-cluster-demo-controller/get-all-inbox-documents-for-business-case-1';
import { getOrganizationLogoDemo } from '../fn/single-cluster-demo-controller/get-organization-logo-demo';
import { GetOrganizationLogoDemo$Params } from '../fn/single-cluster-demo-controller/get-organization-logo-demo';

@Injectable({ providedIn: 'root' })
export class SingleClusterDemoControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `deploy()` */
  static readonly DeployPath = '/demo/deploy';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deploy()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  deploy$Response(params: Deploy$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return deploy(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deploy$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  deploy(params: Deploy$Params, context?: HttpContext): Observable<void> {
    return this.deploy$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getOrganizationLogoDemo()` */
  static readonly GetOrganizationLogoDemoPath = '/demo/organization-logo/{customerKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getOrganizationLogoDemo()` instead.
   *
   * This method doesn't expect any request body.
   */
  getOrganizationLogoDemo$Response(params: GetOrganizationLogoDemo$Params, context?: HttpContext): Observable<StrictHttpResponse<DocumentEntity>> {
    return getOrganizationLogoDemo(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getOrganizationLogoDemo$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getOrganizationLogoDemo(params: GetOrganizationLogoDemo$Params, context?: HttpContext): Observable<DocumentEntity> {
    return this.getOrganizationLogoDemo$Response(params, context).pipe(
      map((r: StrictHttpResponse<DocumentEntity>): DocumentEntity => r.body)
    );
  }

  /** Path part for operation `getAllInboxDocumentsForBusinessCase1()` */
  static readonly GetAllInboxDocumentsForBusinessCase1Path = '/demo/get-all-inbox-documents/{businessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllInboxDocumentsForBusinessCase1()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllInboxDocumentsForBusinessCase1$Response(params: GetAllInboxDocumentsForBusinessCase1$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<DocumentEntity>>> {
    return getAllInboxDocumentsForBusinessCase1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllInboxDocumentsForBusinessCase1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllInboxDocumentsForBusinessCase1(params: GetAllInboxDocumentsForBusinessCase1$Params, context?: HttpContext): Observable<Array<DocumentEntity>> {
    return this.getAllInboxDocumentsForBusinessCase1$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<DocumentEntity>>): Array<DocumentEntity> => r.body)
    );
  }

  /** Path part for operation `getAllCompanyDocumentsDemo()` */
  static readonly GetAllCompanyDocumentsDemoPath = '/demo/get-all-company-documents/{companyId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllCompanyDocumentsDemo()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllCompanyDocumentsDemo$Response(params: GetAllCompanyDocumentsDemo$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<DocumentEntity>>> {
    return getAllCompanyDocumentsDemo(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllCompanyDocumentsDemo$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllCompanyDocumentsDemo(params: GetAllCompanyDocumentsDemo$Params, context?: HttpContext): Observable<Array<DocumentEntity>> {
    return this.getAllCompanyDocumentsDemo$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<DocumentEntity>>): Array<DocumentEntity> => r.body)
    );
  }

  /** Path part for operation `getAllCaseDocumentsForBusinessCaseDemo()` */
  static readonly GetAllCaseDocumentsForBusinessCaseDemoPath = '/demo/get-all-case-documents/{businessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllCaseDocumentsForBusinessCaseDemo()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllCaseDocumentsForBusinessCaseDemo$Response(params: GetAllCaseDocumentsForBusinessCaseDemo$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<DocumentEntity>>> {
    return getAllCaseDocumentsForBusinessCaseDemo(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllCaseDocumentsForBusinessCaseDemo$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllCaseDocumentsForBusinessCaseDemo(params: GetAllCaseDocumentsForBusinessCaseDemo$Params, context?: HttpContext): Observable<Array<DocumentEntity>> {
    return this.getAllCaseDocumentsForBusinessCaseDemo$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<DocumentEntity>>): Array<DocumentEntity> => r.body)
    );
  }

}
