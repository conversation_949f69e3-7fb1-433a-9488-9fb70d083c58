/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { updateDocumentWithCustomerKey } from '../fn/update-document-with-customer-key-migration/update-document-with-customer-key';
import { UpdateDocumentWithCustomerKey$Params } from '../fn/update-document-with-customer-key-migration/update-document-with-customer-key';

@Injectable({ providedIn: 'root' })
export class UpdateDocumentWithCustomerKeyMigrationService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `updateDocumentWithCustomerKey()` */
  static readonly UpdateDocumentWithCustomerKeyPath = '/migration/update_documents_with_customerKey';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `updateDocumentWithCustomerKey()` instead.
   *
   * This method doesn't expect any request body.
   */
  updateDocumentWithCustomerKey$Response(params: UpdateDocumentWithCustomerKey$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return updateDocumentWithCustomerKey(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `updateDocumentWithCustomerKey$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  updateDocumentWithCustomerKey(params: UpdateDocumentWithCustomerKey$Params, context?: HttpContext): Observable<Array<string>> {
    return this.updateDocumentWithCustomerKey$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

}
