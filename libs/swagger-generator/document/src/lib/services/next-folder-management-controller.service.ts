/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { createNextFolderCredentials } from '../fn/next-folder-management-controller/create-next-folder-credentials';
import { CreateNextFolderCredentials$Params } from '../fn/next-folder-management-controller/create-next-folder-credentials';
import { getAllNextFolderDocumentsState } from '../fn/next-folder-management-controller/get-all-next-folder-documents-state';
import { GetAllNextFolderDocumentsState$Params } from '../fn/next-folder-management-controller/get-all-next-folder-documents-state';
import { getAllNextFolderTemplates } from '../fn/next-folder-management-controller/get-all-next-folder-templates';
import { GetAllNextFolderTemplates$Params } from '../fn/next-folder-management-controller/get-all-next-folder-templates';
import { getAllProcesses } from '../fn/next-folder-management-controller/get-all-processes';
import { GetAllProcesses$Params } from '../fn/next-folder-management-controller/get-all-processes';
import { getCredentialsForCustomer } from '../fn/next-folder-management-controller/get-credentials-for-customer';
import { GetCredentialsForCustomer$Params } from '../fn/next-folder-management-controller/get-credentials-for-customer';
import { getNextFolderProcessByBusinessCaseIdAndCustomerKey } from '../fn/next-folder-management-controller/get-next-folder-process-by-business-case-id-and-customer-key';
import { GetNextFolderProcessByBusinessCaseIdAndCustomerKey$Params } from '../fn/next-folder-management-controller/get-next-folder-process-by-business-case-id-and-customer-key';
import { NextFolderCredentialsRecord } from '../models/next-folder-credentials-record';
import { NextFolderDocument } from '../models/next-folder-document';
import { NextFolderProcess } from '../models/next-folder-process';
import { NextFolderProcesscasesResponse } from '../models/next-folder-processcases-response';
import { NextFolderProcessResponse } from '../models/next-folder-process-response';

@Injectable({ providedIn: 'root' })
export class NextFolderManagementControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getCredentialsForCustomer()` */
  static readonly GetCredentialsForCustomerPath = '/next-folder/credentials';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getCredentialsForCustomer()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCredentialsForCustomer$Response(params?: GetCredentialsForCustomer$Params, context?: HttpContext): Observable<StrictHttpResponse<NextFolderCredentialsRecord>> {
    return getCredentialsForCustomer(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getCredentialsForCustomer$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCredentialsForCustomer(params?: GetCredentialsForCustomer$Params, context?: HttpContext): Observable<NextFolderCredentialsRecord> {
    return this.getCredentialsForCustomer$Response(params, context).pipe(
      map((r: StrictHttpResponse<NextFolderCredentialsRecord>): NextFolderCredentialsRecord => r.body)
    );
  }

  /** Path part for operation `createNextFolderCredentials()` */
  static readonly CreateNextFolderCredentialsPath = '/next-folder/credentials';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `createNextFolderCredentials()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  createNextFolderCredentials$Response(params: CreateNextFolderCredentials$Params, context?: HttpContext): Observable<StrictHttpResponse<NextFolderCredentialsRecord>> {
    return createNextFolderCredentials(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `createNextFolderCredentials$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  createNextFolderCredentials(params: CreateNextFolderCredentials$Params, context?: HttpContext): Observable<NextFolderCredentialsRecord> {
    return this.createNextFolderCredentials$Response(params, context).pipe(
      map((r: StrictHttpResponse<NextFolderCredentialsRecord>): NextFolderCredentialsRecord => r.body)
    );
  }

  /** Path part for operation `getAllProcesses()` */
  static readonly GetAllProcessesPath = '/next-folder/processes';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllProcesses()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllProcesses$Response(params?: GetAllProcesses$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<NextFolderProcessResponse>>> {
    return getAllProcesses(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllProcesses$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllProcesses(params?: GetAllProcesses$Params, context?: HttpContext): Observable<Array<NextFolderProcessResponse>> {
    return this.getAllProcesses$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<NextFolderProcessResponse>>): Array<NextFolderProcessResponse> => r.body)
    );
  }

  /** Path part for operation `getAllNextFolderTemplates()` */
  static readonly GetAllNextFolderTemplatesPath = '/next-folder/processcases';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllNextFolderTemplates()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllNextFolderTemplates$Response(params?: GetAllNextFolderTemplates$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<NextFolderProcesscasesResponse>>> {
    return getAllNextFolderTemplates(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllNextFolderTemplates$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllNextFolderTemplates(params?: GetAllNextFolderTemplates$Params, context?: HttpContext): Observable<Array<NextFolderProcesscasesResponse>> {
    return this.getAllNextFolderTemplates$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<NextFolderProcesscasesResponse>>): Array<NextFolderProcesscasesResponse> => r.body)
    );
  }

  /** Path part for operation `getNextFolderProcessByBusinessCaseIdAndCustomerKey()` */
  static readonly GetNextFolderProcessByBusinessCaseIdAndCustomerKeyPath = '/next-folder/process';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getNextFolderProcessByBusinessCaseIdAndCustomerKey()` instead.
   *
   * This method doesn't expect any request body.
   */
  getNextFolderProcessByBusinessCaseIdAndCustomerKey$Response(params: GetNextFolderProcessByBusinessCaseIdAndCustomerKey$Params, context?: HttpContext): Observable<StrictHttpResponse<NextFolderProcess>> {
    return getNextFolderProcessByBusinessCaseIdAndCustomerKey(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getNextFolderProcessByBusinessCaseIdAndCustomerKey$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getNextFolderProcessByBusinessCaseIdAndCustomerKey(params: GetNextFolderProcessByBusinessCaseIdAndCustomerKey$Params, context?: HttpContext): Observable<NextFolderProcess> {
    return this.getNextFolderProcessByBusinessCaseIdAndCustomerKey$Response(params, context).pipe(
      map((r: StrictHttpResponse<NextFolderProcess>): NextFolderProcess => r.body)
    );
  }

  /** Path part for operation `getAllNextFolderDocumentsState()` */
  static readonly GetAllNextFolderDocumentsStatePath = '/next-folder/documents/state';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllNextFolderDocumentsState()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllNextFolderDocumentsState$Response(params: GetAllNextFolderDocumentsState$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<NextFolderDocument>>> {
    return getAllNextFolderDocumentsState(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllNextFolderDocumentsState$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllNextFolderDocumentsState(params: GetAllNextFolderDocumentsState$Params, context?: HttpContext): Observable<Array<NextFolderDocument>> {
    return this.getAllNextFolderDocumentsState$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<NextFolderDocument>>): Array<NextFolderDocument> => r.body)
    );
  }

}
