/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { sendAllExistingDocumentsToIngest } from '../fn/neoshare-ai-migration-internal-controller/send-all-existing-documents-to-ingest';
import { SendAllExistingDocumentsToIngest$Params } from '../fn/neoshare-ai-migration-internal-controller/send-all-existing-documents-to-ingest';

@Injectable({ providedIn: 'root' })
export class NeoshareAiMigrationInternalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `sendAllExistingDocumentsToIngest()` */
  static readonly SendAllExistingDocumentsToIngestPath = '/internal/migrate/neoshare-ai';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `sendAllExistingDocumentsToIngest()` instead.
   *
   * This method doesn't expect any request body.
   */
  sendAllExistingDocumentsToIngest$Response(params: SendAllExistingDocumentsToIngest$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return sendAllExistingDocumentsToIngest(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `sendAllExistingDocumentsToIngest$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  sendAllExistingDocumentsToIngest(params: SendAllExistingDocumentsToIngest$Params, context?: HttpContext): Observable<void> {
    return this.sendAllExistingDocumentsToIngest$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
