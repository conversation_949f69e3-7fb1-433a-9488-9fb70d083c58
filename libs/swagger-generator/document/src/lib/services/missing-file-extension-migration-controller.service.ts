/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { addMissingFileExtensions } from '../fn/missing-file-extension-migration-controller/add-missing-file-extensions';
import { AddMissingFileExtensions$Params } from '../fn/missing-file-extension-migration-controller/add-missing-file-extensions';
import { MissingFileExtensionsMigrationResult } from '../models/missing-file-extensions-migration-result';

@Injectable({ providedIn: 'root' })
export class MissingFileExtensionMigrationControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `addMissingFileExtensions()` */
  static readonly AddMissingFileExtensionsPath = '/migration/documents/add-missing-file-extension';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `addMissingFileExtensions()` instead.
   *
   * This method doesn't expect any request body.
   */
  addMissingFileExtensions$Response(params: AddMissingFileExtensions$Params, context?: HttpContext): Observable<StrictHttpResponse<MissingFileExtensionsMigrationResult>> {
    return addMissingFileExtensions(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `addMissingFileExtensions$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  addMissingFileExtensions(params: AddMissingFileExtensions$Params, context?: HttpContext): Observable<MissingFileExtensionsMigrationResult> {
    return this.addMissingFileExtensions$Response(params, context).pipe(
      map((r: StrictHttpResponse<MissingFileExtensionsMigrationResult>): MissingFileExtensionsMigrationResult => r.body)
    );
  }

}
