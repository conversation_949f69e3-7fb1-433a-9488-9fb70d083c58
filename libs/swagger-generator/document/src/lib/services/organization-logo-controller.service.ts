/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { deleteOrganizationLogo } from '../fn/organization-logo-controller/delete-organization-logo';
import { DeleteOrganizationLogo$Params } from '../fn/organization-logo-controller/delete-organization-logo';
import { DocumentEntity } from '../models/document-entity';
import { getOrganizationLogo } from '../fn/organization-logo-controller/get-organization-logo';
import { GetOrganizationLogo$Params } from '../fn/organization-logo-controller/get-organization-logo';

@Injectable({ providedIn: 'root' })
export class OrganizationLogoControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getOrganizationLogo()` */
  static readonly GetOrganizationLogoPath = '/organization-logo/public/{customerKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getOrganizationLogo()` instead.
   *
   * This method doesn't expect any request body.
   */
  getOrganizationLogo$Response(params: GetOrganizationLogo$Params, context?: HttpContext): Observable<StrictHttpResponse<DocumentEntity>> {
    return getOrganizationLogo(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getOrganizationLogo$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getOrganizationLogo(params: GetOrganizationLogo$Params, context?: HttpContext): Observable<DocumentEntity> {
    return this.getOrganizationLogo$Response(params, context).pipe(
      map((r: StrictHttpResponse<DocumentEntity>): DocumentEntity => r.body)
    );
  }

  /** Path part for operation `deleteOrganizationLogo()` */
  static readonly DeleteOrganizationLogoPath = '/organization-logo/{customerKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteOrganizationLogo()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteOrganizationLogo$Response(params: DeleteOrganizationLogo$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return deleteOrganizationLogo(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteOrganizationLogo$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteOrganizationLogo(params: DeleteOrganizationLogo$Params, context?: HttpContext): Observable<void> {
    return this.deleteOrganizationLogo$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
