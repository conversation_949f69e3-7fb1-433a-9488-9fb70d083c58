/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { DocumentEntity } from '../models/document-entity';
import { uploadOrganizationLogoInternal } from '../fn/organization-logo-internal-controller/upload-organization-logo-internal';
import { UploadOrganizationLogoInternal$Params } from '../fn/organization-logo-internal-controller/upload-organization-logo-internal';

@Injectable({ providedIn: 'root' })
export class OrganizationLogoInternalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `uploadOrganizationLogoInternal()` */
  static readonly UploadOrganizationLogoInternalPath = '/internal/organization-logo/upload/{customerKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `uploadOrganizationLogoInternal()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  uploadOrganizationLogoInternal$Response(params: UploadOrganizationLogoInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<DocumentEntity>> {
    return uploadOrganizationLogoInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `uploadOrganizationLogoInternal$Response()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  uploadOrganizationLogoInternal(params: UploadOrganizationLogoInternal$Params, context?: HttpContext): Observable<DocumentEntity> {
    return this.uploadOrganizationLogoInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<DocumentEntity>): DocumentEntity => r.body)
    );
  }

}
