/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { deleteProfilePictureForUser } from '../fn/profile-picture-controller/delete-profile-picture-for-user';
import { DeleteProfilePictureForUser$Params } from '../fn/profile-picture-controller/delete-profile-picture-for-user';
import { DocumentEntity } from '../models/document-entity';
import { getProfilePictureForUser } from '../fn/profile-picture-controller/get-profile-picture-for-user';
import { GetProfilePictureForUser$Params } from '../fn/profile-picture-controller/get-profile-picture-for-user';

@Injectable({ providedIn: 'root' })
export class ProfilePictureControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getProfilePictureForUser()` */
  static readonly GetProfilePictureForUserPath = '/profile-picture/public/{userId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getProfilePictureForUser()` instead.
   *
   * This method doesn't expect any request body.
   */
  getProfilePictureForUser$Response(params: GetProfilePictureForUser$Params, context?: HttpContext): Observable<StrictHttpResponse<DocumentEntity>> {
    return getProfilePictureForUser(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getProfilePictureForUser$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getProfilePictureForUser(params: GetProfilePictureForUser$Params, context?: HttpContext): Observable<DocumentEntity> {
    return this.getProfilePictureForUser$Response(params, context).pipe(
      map((r: StrictHttpResponse<DocumentEntity>): DocumentEntity => r.body)
    );
  }

  /** Path part for operation `deleteProfilePictureForUser()` */
  static readonly DeleteProfilePictureForUserPath = '/profile-picture';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteProfilePictureForUser()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteProfilePictureForUser$Response(params?: DeleteProfilePictureForUser$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return deleteProfilePictureForUser(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteProfilePictureForUser$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteProfilePictureForUser(params?: DeleteProfilePictureForUser$Params, context?: HttpContext): Observable<void> {
    return this.deleteProfilePictureForUser$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
