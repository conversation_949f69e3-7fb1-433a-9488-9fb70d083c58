/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { getCustomerPages } from '../fn/salesforce-migration-controller/get-customer-pages';
import { GetCustomerPages$Params } from '../fn/salesforce-migration-controller/get-customer-pages';
import { migrateExistingBusinessCasesToSalesforce } from '../fn/salesforce-migration-controller/migrate-existing-business-cases-to-salesforce';
import { MigrateExistingBusinessCasesToSalesforce$Params } from '../fn/salesforce-migration-controller/migrate-existing-business-cases-to-salesforce';
import { migrateExistingCustomersToSalesforce } from '../fn/salesforce-migration-controller/migrate-existing-customers-to-salesforce';
import { MigrateExistingCustomersToSalesforce$Params } from '../fn/salesforce-migration-controller/migrate-existing-customers-to-salesforce';
import { migrateExistingCustomersToSalesforce1 } from '../fn/salesforce-migration-controller/migrate-existing-customers-to-salesforce-1';
import { MigrateExistingCustomersToSalesforce1$Params } from '../fn/salesforce-migration-controller/migrate-existing-customers-to-salesforce-1';
import { migrateExistingUsersToSalesforce } from '../fn/salesforce-migration-controller/migrate-existing-users-to-salesforce';
import { MigrateExistingUsersToSalesforce$Params } from '../fn/salesforce-migration-controller/migrate-existing-users-to-salesforce';

@Injectable({ providedIn: 'root' })
export class SalesforceMigrationControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `migrateExistingUsersToSalesforce()` */
  static readonly MigrateExistingUsersToSalesforcePath = '/migration/users';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `migrateExistingUsersToSalesforce()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateExistingUsersToSalesforce$Response(params?: MigrateExistingUsersToSalesforce$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return migrateExistingUsersToSalesforce(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `migrateExistingUsersToSalesforce$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateExistingUsersToSalesforce(params?: MigrateExistingUsersToSalesforce$Params, context?: HttpContext): Observable<void> {
    return this.migrateExistingUsersToSalesforce$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `migrateExistingCustomersToSalesforce()` */
  static readonly MigrateExistingCustomersToSalesforcePath = '/migration/customers/{page}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `migrateExistingCustomersToSalesforce()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateExistingCustomersToSalesforce$Response(params: MigrateExistingCustomersToSalesforce$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return migrateExistingCustomersToSalesforce(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `migrateExistingCustomersToSalesforce$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateExistingCustomersToSalesforce(params: MigrateExistingCustomersToSalesforce$Params, context?: HttpContext): Observable<void> {
    return this.migrateExistingCustomersToSalesforce$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `migrateExistingCustomersToSalesforce1()` */
  static readonly MigrateExistingCustomersToSalesforce1Path = '/migration/customers';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `migrateExistingCustomersToSalesforce1()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateExistingCustomersToSalesforce1$Response(params?: MigrateExistingCustomersToSalesforce1$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return migrateExistingCustomersToSalesforce1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `migrateExistingCustomersToSalesforce1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateExistingCustomersToSalesforce1(params?: MigrateExistingCustomersToSalesforce1$Params, context?: HttpContext): Observable<void> {
    return this.migrateExistingCustomersToSalesforce1$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getCustomerPages()` */
  static readonly GetCustomerPagesPath = '/migration/customer-pages';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getCustomerPages()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCustomerPages$Response(params?: GetCustomerPages$Params, context?: HttpContext): Observable<StrictHttpResponse<number>> {
    return getCustomerPages(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getCustomerPages$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCustomerPages(params?: GetCustomerPages$Params, context?: HttpContext): Observable<number> {
    return this.getCustomerPages$Response(params, context).pipe(
      map((r: StrictHttpResponse<number>): number => r.body)
    );
  }

  /** Path part for operation `migrateExistingBusinessCasesToSalesforce()` */
  static readonly MigrateExistingBusinessCasesToSalesforcePath = '/migration/business-cases';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `migrateExistingBusinessCasesToSalesforce()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateExistingBusinessCasesToSalesforce$Response(params?: MigrateExistingBusinessCasesToSalesforce$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return migrateExistingBusinessCasesToSalesforce(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `migrateExistingBusinessCasesToSalesforce$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateExistingBusinessCasesToSalesforce(params?: MigrateExistingBusinessCasesToSalesforce$Params, context?: HttpContext): Observable<void> {
    return this.migrateExistingBusinessCasesToSalesforce$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
