/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { generateCsv } from '../fn/core-banking-system-integration-public-controller/generate-csv';
import { GenerateCsv$Params } from '../fn/core-banking-system-integration-public-controller/generate-csv';
import { listCasesAndRespectiveCompanies } from '../fn/core-banking-system-integration-public-controller/list-cases-and-respective-companies';
import { ListCasesAndRespectiveCompanies$Params } from '../fn/core-banking-system-integration-public-controller/list-cases-and-respective-companies';
import { RegisteredEntryResponse } from '../models/registered-entry-response';
import { validateToken } from '../fn/core-banking-system-integration-public-controller/validate-token';
import { ValidateToken$Params } from '../fn/core-banking-system-integration-public-controller/validate-token';

@Injectable({ providedIn: 'root' })
export class CoreBankingSystemIntegrationPublicControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `validateToken()` */
  static readonly ValidateTokenPath = '/public/core-banking-integration/token/validate';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `validateToken()` instead.
   *
   * This method doesn't expect any request body.
   */
  validateToken$Response(params: ValidateToken$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return validateToken(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `validateToken$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  validateToken(params: ValidateToken$Params, context?: HttpContext): Observable<void> {
    return this.validateToken$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `listCasesAndRespectiveCompanies()` */
  static readonly ListCasesAndRespectiveCompaniesPath = '/public/core-banking-integration/list-cases';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `listCasesAndRespectiveCompanies()` instead.
   *
   * This method doesn't expect any request body.
   */
  listCasesAndRespectiveCompanies$Response(params: ListCasesAndRespectiveCompanies$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<RegisteredEntryResponse>>> {
    return listCasesAndRespectiveCompanies(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `listCasesAndRespectiveCompanies$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  listCasesAndRespectiveCompanies(params: ListCasesAndRespectiveCompanies$Params, context?: HttpContext): Observable<Array<RegisteredEntryResponse>> {
    return this.listCasesAndRespectiveCompanies$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<RegisteredEntryResponse>>): Array<RegisteredEntryResponse> => r.body)
    );
  }

  /** Path part for operation `generateCsv()` */
  static readonly GenerateCsvPath = '/public/core-banking-integration/csv-generation';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `generateCsv()` instead.
   *
   * This method doesn't expect any request body.
   */
  generateCsv$Response(params: GenerateCsv$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return generateCsv(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `generateCsv$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  generateCsv(params: GenerateCsv$Params, context?: HttpContext): Observable<void> {
    return this.generateCsv$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
