/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { businessCase } from '../fn/sales-force-force-sync-controller/business-case';
import { BusinessCase$Params } from '../fn/sales-force-force-sync-controller/business-case';
import { customer } from '../fn/sales-force-force-sync-controller/customer';
import { Customer$Params } from '../fn/sales-force-force-sync-controller/customer';
import { user } from '../fn/sales-force-force-sync-controller/user';
import { User$Params } from '../fn/sales-force-force-sync-controller/user';

@Injectable({ providedIn: 'root' })
export class SalesForceForceSyncControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `user()` */
  static readonly UserPath = '/force-sync/user/{id}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `user()` instead.
   *
   * This method doesn't expect any request body.
   */
  user$Response(params: User$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return user(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `user$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  user(params: User$Params, context?: HttpContext): Observable<void> {
    return this.user$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `customer()` */
  static readonly CustomerPath = '/force-sync/customer/{customerKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `customer()` instead.
   *
   * This method doesn't expect any request body.
   */
  customer$Response(params: Customer$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return customer(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `customer$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  customer(params: Customer$Params, context?: HttpContext): Observable<void> {
    return this.customer$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `businessCase()` */
  static readonly BusinessCasePath = '/force-sync/business-case/{id}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `businessCase()` instead.
   *
   * This method doesn't expect any request body.
   */
  businessCase$Response(params: BusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return businessCase(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `businessCase$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  businessCase(params: BusinessCase$Params, context?: HttpContext): Observable<void> {
    return this.businessCase$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
