/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { CoreBankingSynchronizationRegistrationDto } from '../models/core-banking-synchronization-registration-dto';
import { downloadAccessKey1$Any } from '../fn/core-banking-system-integration-controller/download-access-key-1-any';
import { DownloadAccessKey1$Any$Params } from '../fn/core-banking-system-integration-controller/download-access-key-1-any';
import { downloadAccessKey1$Json } from '../fn/core-banking-system-integration-controller/download-access-key-1-json';
import { DownloadAccessKey1$Json$Params } from '../fn/core-banking-system-integration-controller/download-access-key-1-json';
import { getRegistration1 } from '../fn/core-banking-system-integration-controller/get-registration-1';
import { GetRegistration1$Params } from '../fn/core-banking-system-integration-controller/get-registration-1';
import { performPreconditionCheck } from '../fn/core-banking-system-integration-controller/perform-precondition-check';
import { PerformPreconditionCheck$Params } from '../fn/core-banking-system-integration-controller/perform-precondition-check';
import { refreshCustomerRegistration } from '../fn/core-banking-system-integration-controller/refresh-customer-registration';
import { RefreshCustomerRegistration$Params } from '../fn/core-banking-system-integration-controller/refresh-customer-registration';
import { registerCaseForIntegration } from '../fn/core-banking-system-integration-controller/register-case-for-integration';
import { RegisterCaseForIntegration$Params } from '../fn/core-banking-system-integration-controller/register-case-for-integration';
import { registerCustomerForIntegration } from '../fn/core-banking-system-integration-controller/register-customer-for-integration';
import { RegisterCustomerForIntegration$Params } from '../fn/core-banking-system-integration-controller/register-customer-for-integration';

@Injectable({ providedIn: 'root' })
export class CoreBankingSystemIntegrationControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `refreshCustomerRegistration()` */
  static readonly RefreshCustomerRegistrationPath = '/core-banking/refresh-customer-registration';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `refreshCustomerRegistration()` instead.
   *
   * This method doesn't expect any request body.
   */
  refreshCustomerRegistration$Response(params?: RefreshCustomerRegistration$Params, context?: HttpContext): Observable<StrictHttpResponse<CoreBankingSynchronizationRegistrationDto>> {
    return refreshCustomerRegistration(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `refreshCustomerRegistration$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  refreshCustomerRegistration(params?: RefreshCustomerRegistration$Params, context?: HttpContext): Observable<CoreBankingSynchronizationRegistrationDto> {
    return this.refreshCustomerRegistration$Response(params, context).pipe(
      map((r: StrictHttpResponse<CoreBankingSynchronizationRegistrationDto>): CoreBankingSynchronizationRegistrationDto => r.body)
    );
  }

  /** Path part for operation `registerCustomerForIntegration()` */
  static readonly RegisterCustomerForIntegrationPath = '/core-banking/register-customer-for-integration';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `registerCustomerForIntegration()` instead.
   *
   * This method doesn't expect any request body.
   */
  registerCustomerForIntegration$Response(params?: RegisterCustomerForIntegration$Params, context?: HttpContext): Observable<StrictHttpResponse<CoreBankingSynchronizationRegistrationDto>> {
    return registerCustomerForIntegration(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `registerCustomerForIntegration$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  registerCustomerForIntegration(params?: RegisterCustomerForIntegration$Params, context?: HttpContext): Observable<CoreBankingSynchronizationRegistrationDto> {
    return this.registerCustomerForIntegration$Response(params, context).pipe(
      map((r: StrictHttpResponse<CoreBankingSynchronizationRegistrationDto>): CoreBankingSynchronizationRegistrationDto => r.body)
    );
  }

  /** Path part for operation `registerCaseForIntegration()` */
  static readonly RegisterCaseForIntegrationPath = '/core-banking/register-case-for-integration';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `registerCaseForIntegration()` instead.
   *
   * This method doesn't expect any request body.
   */
  registerCaseForIntegration$Response(params: RegisterCaseForIntegration$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return registerCaseForIntegration(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `registerCaseForIntegration$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  registerCaseForIntegration(params: RegisterCaseForIntegration$Params, context?: HttpContext): Observable<void> {
    return this.registerCaseForIntegration$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getRegistration1()` */
  static readonly GetRegistration1Path = '/core-banking/get-registration';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getRegistration1()` instead.
   *
   * This method doesn't expect any request body.
   */
  getRegistration1$Response(params?: GetRegistration1$Params, context?: HttpContext): Observable<StrictHttpResponse<CoreBankingSynchronizationRegistrationDto>> {
    return getRegistration1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getRegistration1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getRegistration1(params?: GetRegistration1$Params, context?: HttpContext): Observable<CoreBankingSynchronizationRegistrationDto> {
    return this.getRegistration1$Response(params, context).pipe(
      map((r: StrictHttpResponse<CoreBankingSynchronizationRegistrationDto>): CoreBankingSynchronizationRegistrationDto => r.body)
    );
  }

  /** Path part for operation `downloadAccessKey1()` */
  static readonly DownloadAccessKey1Path = '/core-banking/download-access-key';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `downloadAccessKey1$Any()` instead.
   *
   * This method doesn't expect any request body.
   */
  downloadAccessKey1$Any$Response(params?: DownloadAccessKey1$Any$Params, context?: HttpContext): Observable<StrictHttpResponse<string>> {
    return downloadAccessKey1$Any(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `downloadAccessKey1$Any$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  downloadAccessKey1$Any(params?: DownloadAccessKey1$Any$Params, context?: HttpContext): Observable<string> {
    return this.downloadAccessKey1$Any$Response(params, context).pipe(
      map((r: StrictHttpResponse<string>): string => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `downloadAccessKey1$Json()` instead.
   *
   * This method doesn't expect any request body.
   */
  downloadAccessKey1$Json$Response(params?: DownloadAccessKey1$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<string>> {
    return downloadAccessKey1$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `downloadAccessKey1$Json$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  downloadAccessKey1$Json(params?: DownloadAccessKey1$Json$Params, context?: HttpContext): Observable<string> {
    return this.downloadAccessKey1$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<string>): string => r.body)
    );
  }

  /** Path part for operation `performPreconditionCheck()` */
  static readonly PerformPreconditionCheckPath = '/core-banking/case-precondition-check';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `performPreconditionCheck()` instead.
   *
   * This method doesn't expect any request body.
   */
  performPreconditionCheck$Response(params: PerformPreconditionCheck$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return performPreconditionCheck(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `performPreconditionCheck$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  performPreconditionCheck(params: PerformPreconditionCheck$Params, context?: HttpContext): Observable<void> {
    return this.performPreconditionCheck$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
