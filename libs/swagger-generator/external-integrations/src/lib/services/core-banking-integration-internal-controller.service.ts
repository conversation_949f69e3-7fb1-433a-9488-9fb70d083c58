/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { CoreBankingSynchronizationRegistration } from '../models/core-banking-synchronization-registration';
import { CoreBankingSynchronizationRegistrationDto } from '../models/core-banking-synchronization-registration-dto';
import { deregisterCase } from '../fn/core-banking-integration-internal-controller/deregister-case';
import { DeregisterCase$Params } from '../fn/core-banking-integration-internal-controller/deregister-case';
import { downloadAccessKey$Any } from '../fn/core-banking-integration-internal-controller/download-access-key-any';
import { DownloadAccessKey$Any$Params } from '../fn/core-banking-integration-internal-controller/download-access-key-any';
import { downloadAccessKey$Json } from '../fn/core-banking-integration-internal-controller/download-access-key-json';
import { DownloadAccessKey$Json$Params } from '../fn/core-banking-integration-internal-controller/download-access-key-json';
import { getRegistration } from '../fn/core-banking-integration-internal-controller/get-registration';
import { GetRegistration$Params } from '../fn/core-banking-integration-internal-controller/get-registration';
import { performPreconditionCheckInternal } from '../fn/core-banking-integration-internal-controller/perform-precondition-check-internal';
import { PerformPreconditionCheckInternal$Params } from '../fn/core-banking-integration-internal-controller/perform-precondition-check-internal';
import { refreshCustomerRegistrationInternal } from '../fn/core-banking-integration-internal-controller/refresh-customer-registration-internal';
import { RefreshCustomerRegistrationInternal$Params } from '../fn/core-banking-integration-internal-controller/refresh-customer-registration-internal';
import { registerCaseForIntegrationInternal } from '../fn/core-banking-integration-internal-controller/register-case-for-integration-internal';
import { RegisterCaseForIntegrationInternal$Params } from '../fn/core-banking-integration-internal-controller/register-case-for-integration-internal';
import { registerCustomerForIntegrationInternal } from '../fn/core-banking-integration-internal-controller/register-customer-for-integration-internal';
import { RegisterCustomerForIntegrationInternal$Params } from '../fn/core-banking-integration-internal-controller/register-customer-for-integration-internal';
import { validateToken1 } from '../fn/core-banking-integration-internal-controller/validate-token-1';
import { ValidateToken1$Params } from '../fn/core-banking-integration-internal-controller/validate-token-1';

@Injectable({ providedIn: 'root' })
export class CoreBankingIntegrationInternalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `refreshCustomerRegistrationInternal()` */
  static readonly RefreshCustomerRegistrationInternalPath = '/internal/core-banking/refresh-customer-registration';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `refreshCustomerRegistrationInternal()` instead.
   *
   * This method doesn't expect any request body.
   */
  refreshCustomerRegistrationInternal$Response(params: RefreshCustomerRegistrationInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<CoreBankingSynchronizationRegistrationDto>> {
    return refreshCustomerRegistrationInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `refreshCustomerRegistrationInternal$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  refreshCustomerRegistrationInternal(params: RefreshCustomerRegistrationInternal$Params, context?: HttpContext): Observable<CoreBankingSynchronizationRegistrationDto> {
    return this.refreshCustomerRegistrationInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<CoreBankingSynchronizationRegistrationDto>): CoreBankingSynchronizationRegistrationDto => r.body)
    );
  }

  /** Path part for operation `validateToken1()` */
  static readonly ValidateToken1Path = '/internal/core-banking/token/validate';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `validateToken1()` instead.
   *
   * This method doesn't expect any request body.
   */
  validateToken1$Response(params: ValidateToken1$Params, context?: HttpContext): Observable<StrictHttpResponse<CoreBankingSynchronizationRegistration>> {
    return validateToken1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `validateToken1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  validateToken1(params: ValidateToken1$Params, context?: HttpContext): Observable<CoreBankingSynchronizationRegistration> {
    return this.validateToken1$Response(params, context).pipe(
      map((r: StrictHttpResponse<CoreBankingSynchronizationRegistration>): CoreBankingSynchronizationRegistration => r.body)
    );
  }

  /** Path part for operation `registerCustomerForIntegrationInternal()` */
  static readonly RegisterCustomerForIntegrationInternalPath = '/internal/core-banking/register-customer-for-integration';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `registerCustomerForIntegrationInternal()` instead.
   *
   * This method doesn't expect any request body.
   */
  registerCustomerForIntegrationInternal$Response(params: RegisterCustomerForIntegrationInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<CoreBankingSynchronizationRegistrationDto>> {
    return registerCustomerForIntegrationInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `registerCustomerForIntegrationInternal$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  registerCustomerForIntegrationInternal(params: RegisterCustomerForIntegrationInternal$Params, context?: HttpContext): Observable<CoreBankingSynchronizationRegistrationDto> {
    return this.registerCustomerForIntegrationInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<CoreBankingSynchronizationRegistrationDto>): CoreBankingSynchronizationRegistrationDto => r.body)
    );
  }

  /** Path part for operation `registerCaseForIntegrationInternal()` */
  static readonly RegisterCaseForIntegrationInternalPath = '/internal/core-banking/register-case-for-integration';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `registerCaseForIntegrationInternal()` instead.
   *
   * This method doesn't expect any request body.
   */
  registerCaseForIntegrationInternal$Response(params: RegisterCaseForIntegrationInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return registerCaseForIntegrationInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `registerCaseForIntegrationInternal$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  registerCaseForIntegrationInternal(params: RegisterCaseForIntegrationInternal$Params, context?: HttpContext): Observable<void> {
    return this.registerCaseForIntegrationInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getRegistration()` */
  static readonly GetRegistrationPath = '/internal/core-banking/get-registration';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getRegistration()` instead.
   *
   * This method doesn't expect any request body.
   */
  getRegistration$Response(params: GetRegistration$Params, context?: HttpContext): Observable<StrictHttpResponse<CoreBankingSynchronizationRegistrationDto>> {
    return getRegistration(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getRegistration$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getRegistration(params: GetRegistration$Params, context?: HttpContext): Observable<CoreBankingSynchronizationRegistrationDto> {
    return this.getRegistration$Response(params, context).pipe(
      map((r: StrictHttpResponse<CoreBankingSynchronizationRegistrationDto>): CoreBankingSynchronizationRegistrationDto => r.body)
    );
  }

  /** Path part for operation `downloadAccessKey()` */
  static readonly DownloadAccessKeyPath = '/internal/core-banking/download-access-key';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `downloadAccessKey$Any()` instead.
   *
   * This method doesn't expect any request body.
   */
  downloadAccessKey$Any$Response(params: DownloadAccessKey$Any$Params, context?: HttpContext): Observable<StrictHttpResponse<string>> {
    return downloadAccessKey$Any(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `downloadAccessKey$Any$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  downloadAccessKey$Any(params: DownloadAccessKey$Any$Params, context?: HttpContext): Observable<string> {
    return this.downloadAccessKey$Any$Response(params, context).pipe(
      map((r: StrictHttpResponse<string>): string => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `downloadAccessKey$Json()` instead.
   *
   * This method doesn't expect any request body.
   */
  downloadAccessKey$Json$Response(params: DownloadAccessKey$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<string>> {
    return downloadAccessKey$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `downloadAccessKey$Json$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  downloadAccessKey$Json(params: DownloadAccessKey$Json$Params, context?: HttpContext): Observable<string> {
    return this.downloadAccessKey$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<string>): string => r.body)
    );
  }

  /** Path part for operation `performPreconditionCheckInternal()` */
  static readonly PerformPreconditionCheckInternalPath = '/internal/core-banking/case-precondition-check';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `performPreconditionCheckInternal()` instead.
   *
   * This method doesn't expect any request body.
   */
  performPreconditionCheckInternal$Response(params: PerformPreconditionCheckInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return performPreconditionCheckInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `performPreconditionCheckInternal$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  performPreconditionCheckInternal(params: PerformPreconditionCheckInternal$Params, context?: HttpContext): Observable<void> {
    return this.performPreconditionCheckInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `deregisterCase()` */
  static readonly DeregisterCasePath = '/internal/core-banking';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deregisterCase()` instead.
   *
   * This method doesn't expect any request body.
   */
  deregisterCase$Response(params: DeregisterCase$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return deregisterCase(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deregisterCase$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deregisterCase(params: DeregisterCase$Params, context?: HttpContext): Observable<void> {
    return this.deregisterCase$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
