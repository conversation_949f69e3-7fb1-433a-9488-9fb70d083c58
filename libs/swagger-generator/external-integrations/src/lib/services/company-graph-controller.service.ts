/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { CompanyNetworkDto } from '../models/company-network-dto';
import { getCompanyGraph } from '../fn/company-graph-controller/get-company-graph';
import { GetCompanyGraph$Params } from '../fn/company-graph-controller/get-company-graph';

@Injectable({ providedIn: 'root' })
export class CompanyGraphControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getCompanyGraph()` */
  static readonly GetCompanyGraphPath = '/internal/company-graph';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getCompanyGraph()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCompanyGraph$Response(params: GetCompanyGraph$Params, context?: HttpContext): Observable<StrictHttpResponse<CompanyNetworkDto>> {
    return getCompanyGraph(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getCompanyGraph$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCompanyGraph(params: GetCompanyGraph$Params, context?: HttpContext): Observable<CompanyNetworkDto> {
    return this.getCompanyGraph$Response(params, context).pipe(
      map((r: StrictHttpResponse<CompanyNetworkDto>): CompanyNetworkDto => r.body)
    );
  }

}
