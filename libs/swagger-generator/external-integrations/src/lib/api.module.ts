/* tslint:disable */
/* eslint-disable */
import { NgM<PERSON><PERSON>, ModuleWithProviders, SkipSelf, Optional } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiConfiguration, ApiConfigurationParams } from './api-configuration';

import { CoreBankingIntegrationInternalControllerService } from './services/core-banking-integration-internal-controller.service';
import { CoreBankingSystemIntegrationControllerService } from './services/core-banking-system-integration-controller.service';
import { CoreBankingSystemIntegrationPublicControllerService } from './services/core-banking-system-integration-public-controller.service';
import { SalesforceMigrationControllerService } from './services/salesforce-migration-controller.service';
import { CompanyGraphControllerService } from './services/company-graph-controller.service';
import { SalesForceForceSyncControllerService } from './services/sales-force-force-sync-controller.service';

/**
 * Module that provides all services and configuration.
 */
@NgModule({
  imports: [],
  exports: [],
  declarations: [],
  providers: [
    CoreBankingIntegrationInternalControllerService,
    CoreBankingSystemIntegrationControllerService,
    CoreBankingSystemIntegrationPublicControllerService,
    SalesforceMigrationControllerService,
    CompanyGraphControllerService,
    SalesForceForceSyncControllerService,
    ApiConfiguration
  ],
})
export class ApiModule {
  static forRoot(params: ApiConfigurationParams): ModuleWithProviders<ApiModule> {
    return {
      ngModule: ApiModule,
      providers: [
        {
          provide: ApiConfiguration,
          useValue: params
        }
      ]
    }
  }

  constructor( 
    @Optional() @SkipSelf() parentModule: ApiModule,
    @Optional() http: HttpClient
  ) {
    if (parentModule) {
      throw new Error('ApiModule is already loaded. Import in your base AppModule only.');
    }
    if (!http) {
      throw new Error('You need to import the HttpClientModule in your AppModule! \n' +
      'See also https://github.com/angular/angular/issues/20575');
    }
  }
}
