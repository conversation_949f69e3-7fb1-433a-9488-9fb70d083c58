/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { getChangelogInternalChangelogGet } from '../fn/info/get-changelog-internal-changelog-get';
import { GetChangelogInternalChangelogGet$Params } from '../fn/info/get-changelog-internal-changelog-get';
import { getVersionInternalVersionGet } from '../fn/info/get-version-internal-version-get';
import { GetVersionInternalVersionGet$Params } from '../fn/info/get-version-internal-version-get';

@Injectable({ providedIn: 'root' })
export class InfoService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getVersionInternalVersionGet()` */
  static readonly GetVersionInternalVersionGetPath = '/internal/version';

  /**
   * Get Version.
   *
   * Return the version of the service.
   *
   * Notes
   * -----
   * For now we only return all OPENAI endpoint variables as well as infos from the pyproject.toml
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getVersionInternalVersionGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  getVersionInternalVersionGet$Response(params?: GetVersionInternalVersionGet$Params, context?: HttpContext): Observable<StrictHttpResponse<{
}>> {
    return getVersionInternalVersionGet(this.http, this.rootUrl, params, context);
  }

  /**
   * Get Version.
   *
   * Return the version of the service.
   *
   * Notes
   * -----
   * For now we only return all OPENAI endpoint variables as well as infos from the pyproject.toml
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getVersionInternalVersionGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getVersionInternalVersionGet(params?: GetVersionInternalVersionGet$Params, context?: HttpContext): Observable<{
}> {
    return this.getVersionInternalVersionGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<{
}>): {
} => r.body)
    );
  }

  /** Path part for operation `getChangelogInternalChangelogGet()` */
  static readonly GetChangelogInternalChangelogGetPath = '/internal/changelog';

  /**
   * Get Changelog.
   *
   * Return the changelog in HTML.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getChangelogInternalChangelogGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  getChangelogInternalChangelogGet$Response(params?: GetChangelogInternalChangelogGet$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return getChangelogInternalChangelogGet(this.http, this.rootUrl, params, context);
  }

  /**
   * Get Changelog.
   *
   * Return the changelog in HTML.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getChangelogInternalChangelogGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getChangelogInternalChangelogGet(params?: GetChangelogInternalChangelogGet$Params, context?: HttpContext): Observable<any> {
    return this.getChangelogInternalChangelogGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

}
