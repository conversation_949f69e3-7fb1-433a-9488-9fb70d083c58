/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { ChatMessage } from '../models/chat-message';
import { commentCommentPost } from '../fn/feedback/comment-comment-post';
import { CommentCommentPost$Params } from '../fn/feedback/comment-comment-post';
import { deleteFeedbackCommentCommentDelete } from '../fn/feedback/delete-feedback-comment-comment-delete';
import { DeleteFeedbackCommentCommentDelete$Params } from '../fn/feedback/delete-feedback-comment-comment-delete';
import { deleteFeedbackFeedbackDelete } from '../fn/feedback/delete-feedback-feedback-delete';
import { DeleteFeedbackFeedbackDelete$Params } from '../fn/feedback/delete-feedback-feedback-delete';
import { deleteFeedbackRateRateDelete } from '../fn/feedback/delete-feedback-rate-rate-delete';
import { DeleteFeedbackRateRateDelete$Params } from '../fn/feedback/delete-feedback-rate-rate-delete';
import { FeedbackComment } from '../models/feedback-comment';
import { FeedbackEntry } from '../models/feedback-entry';
import { FeedbackRate } from '../models/feedback-rate';
import { getFeedbackCommentCommentGet } from '../fn/feedback/get-feedback-comment-comment-get';
import { GetFeedbackCommentCommentGet$Params } from '../fn/feedback/get-feedback-comment-comment-get';
import { getFeedbackFeedbackGet } from '../fn/feedback/get-feedback-feedback-get';
import { GetFeedbackFeedbackGet$Params } from '../fn/feedback/get-feedback-feedback-get';
import { getFeedbackRateRateGet } from '../fn/feedback/get-feedback-rate-rate-get';
import { GetFeedbackRateRateGet$Params } from '../fn/feedback/get-feedback-rate-rate-get';
import { rateMessageRatePost } from '../fn/feedback/rate-message-rate-post';
import { RateMessageRatePost$Params } from '../fn/feedback/rate-message-rate-post';
import { thumbUpTheJamThumbupthejamPost } from '../fn/feedback/thumb-up-the-jam-thumbupthejam-post';
import { ThumbUpTheJamThumbupthejamPost$Params } from '../fn/feedback/thumb-up-the-jam-thumbupthejam-post';

@Injectable({ providedIn: 'root' })
export class FeedbackService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getFeedbackRateRateGet()` */
  static readonly GetFeedbackRateRateGetPath = '/rate';

  /**
   * Get Feedback Rate.
   *
   * Fetch the sentiments of a post.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFeedbackRateRateGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFeedbackRateRateGet$Response(params: GetFeedbackRateRateGet$Params, context?: HttpContext): Observable<StrictHttpResponse<FeedbackRate>> {
    return getFeedbackRateRateGet(this.http, this.rootUrl, params, context);
  }

  /**
   * Get Feedback Rate.
   *
   * Fetch the sentiments of a post.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFeedbackRateRateGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFeedbackRateRateGet(params: GetFeedbackRateRateGet$Params, context?: HttpContext): Observable<FeedbackRate> {
    return this.getFeedbackRateRateGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<FeedbackRate>): FeedbackRate => r.body)
    );
  }

  /** Path part for operation `rateMessageRatePost()` */
  static readonly RateMessageRatePostPath = '/rate';

  /**
   * Rate Message.
   *
   * Endpoint to show sentiments for a certain message.
   *
   * Parameters
   * ----------
   * sentiment : Sentiment
   *     The sentiment to the message, e.g. `positive`, `negative`, or `neutral`.
   * standard_context: StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * message_index : int
   *     The index of the message to be rated.
   * feedback_controller : FeedbackController
   *     The feedback controller to handle the rating logic.
   *
   * Returns
   * -------
   * List[ChatMessage]
   *     The updated chat history for the specified session ID.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `rateMessageRatePost()` instead.
   *
   * This method doesn't expect any request body.
   */
  rateMessageRatePost$Response(params: RateMessageRatePost$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<ChatMessage>>> {
    return rateMessageRatePost(this.http, this.rootUrl, params, context);
  }

  /**
   * Rate Message.
   *
   * Endpoint to show sentiments for a certain message.
   *
   * Parameters
   * ----------
   * sentiment : Sentiment
   *     The sentiment to the message, e.g. `positive`, `negative`, or `neutral`.
   * standard_context: StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * message_index : int
   *     The index of the message to be rated.
   * feedback_controller : FeedbackController
   *     The feedback controller to handle the rating logic.
   *
   * Returns
   * -------
   * List[ChatMessage]
   *     The updated chat history for the specified session ID.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `rateMessageRatePost$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  rateMessageRatePost(params: RateMessageRatePost$Params, context?: HttpContext): Observable<Array<ChatMessage>> {
    return this.rateMessageRatePost$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<ChatMessage>>): Array<ChatMessage> => r.body)
    );
  }

  /** Path part for operation `deleteFeedbackRateRateDelete()` */
  static readonly DeleteFeedbackRateRateDeletePath = '/rate';

  /**
   * Delete Feedback Rate.
   *
   * Delete the sentiments of a post.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteFeedbackRateRateDelete()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteFeedbackRateRateDelete$Response(params: DeleteFeedbackRateRateDelete$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return deleteFeedbackRateRateDelete(this.http, this.rootUrl, params, context);
  }

  /**
   * Delete Feedback Rate.
   *
   * Delete the sentiments of a post.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteFeedbackRateRateDelete$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteFeedbackRateRateDelete(params: DeleteFeedbackRateRateDelete$Params, context?: HttpContext): Observable<any> {
    return this.deleteFeedbackRateRateDelete$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /** Path part for operation `getFeedbackFeedbackGet()` */
  static readonly GetFeedbackFeedbackGetPath = '/feedback';

  /**
   * Get Feedback.
   *
   * Fetch the sentiments of a post.
   *
   * Parameters
   * ----------
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * message_index : int
   *     The message id of the respective message
   *
   * Returns
   * -------
   * FeedbackEntry
   *     The feedback of a certain question.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFeedbackFeedbackGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFeedbackFeedbackGet$Response(params: GetFeedbackFeedbackGet$Params, context?: HttpContext): Observable<StrictHttpResponse<FeedbackEntry>> {
    return getFeedbackFeedbackGet(this.http, this.rootUrl, params, context);
  }

  /**
   * Get Feedback.
   *
   * Fetch the sentiments of a post.
   *
   * Parameters
   * ----------
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * message_index : int
   *     The message id of the respective message
   *
   * Returns
   * -------
   * FeedbackEntry
   *     The feedback of a certain question.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFeedbackFeedbackGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFeedbackFeedbackGet(params: GetFeedbackFeedbackGet$Params, context?: HttpContext): Observable<FeedbackEntry> {
    return this.getFeedbackFeedbackGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<FeedbackEntry>): FeedbackEntry => r.body)
    );
  }

  /** Path part for operation `deleteFeedbackFeedbackDelete()` */
  static readonly DeleteFeedbackFeedbackDeletePath = '/feedback';

  /**
   * Delete Feedback.
   *
   * Delete the comments and rate of a post.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteFeedbackFeedbackDelete()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteFeedbackFeedbackDelete$Response(params: DeleteFeedbackFeedbackDelete$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return deleteFeedbackFeedbackDelete(this.http, this.rootUrl, params, context);
  }

  /**
   * Delete Feedback.
   *
   * Delete the comments and rate of a post.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteFeedbackFeedbackDelete$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteFeedbackFeedbackDelete(params: DeleteFeedbackFeedbackDelete$Params, context?: HttpContext): Observable<any> {
    return this.deleteFeedbackFeedbackDelete$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /** Path part for operation `getFeedbackCommentCommentGet()` */
  static readonly GetFeedbackCommentCommentGetPath = '/comment';

  /**
   * Get Feedback Comment.
   *
   * Fetch the comments of a post.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFeedbackCommentCommentGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFeedbackCommentCommentGet$Response(params: GetFeedbackCommentCommentGet$Params, context?: HttpContext): Observable<StrictHttpResponse<FeedbackComment>> {
    return getFeedbackCommentCommentGet(this.http, this.rootUrl, params, context);
  }

  /**
   * Get Feedback Comment.
   *
   * Fetch the comments of a post.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFeedbackCommentCommentGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFeedbackCommentCommentGet(params: GetFeedbackCommentCommentGet$Params, context?: HttpContext): Observable<FeedbackComment> {
    return this.getFeedbackCommentCommentGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<FeedbackComment>): FeedbackComment => r.body)
    );
  }

  /** Path part for operation `commentCommentPost()` */
  static readonly CommentCommentPostPath = '/comment';

  /**
   * Comment.
   *
   * Endpoint to add a feedback comment to a message based on the session id.
   *
   * Parameters
   * ----------
   * body : FeedbackComment
   *     The comment to be added to the message.
   * standard_context: StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * message_index : int
   *     The message id of the respective message
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `commentCommentPost()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  commentCommentPost$Response(params: CommentCommentPost$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return commentCommentPost(this.http, this.rootUrl, params, context);
  }

  /**
   * Comment.
   *
   * Endpoint to add a feedback comment to a message based on the session id.
   *
   * Parameters
   * ----------
   * body : FeedbackComment
   *     The comment to be added to the message.
   * standard_context: StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * message_index : int
   *     The message id of the respective message
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `commentCommentPost$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  commentCommentPost(params: CommentCommentPost$Params, context?: HttpContext): Observable<any> {
    return this.commentCommentPost$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /** Path part for operation `deleteFeedbackCommentCommentDelete()` */
  static readonly DeleteFeedbackCommentCommentDeletePath = '/comment';

  /**
   * Delete Feedback Comment.
   *
   * Fetch the comments of a post.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteFeedbackCommentCommentDelete()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteFeedbackCommentCommentDelete$Response(params: DeleteFeedbackCommentCommentDelete$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return deleteFeedbackCommentCommentDelete(this.http, this.rootUrl, params, context);
  }

  /**
   * Delete Feedback Comment.
   *
   * Fetch the comments of a post.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteFeedbackCommentCommentDelete$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteFeedbackCommentCommentDelete(params: DeleteFeedbackCommentCommentDelete$Params, context?: HttpContext): Observable<any> {
    return this.deleteFeedbackCommentCommentDelete$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /** Path part for operation `thumbUpTheJamThumbupthejamPost()` */
  static readonly ThumbUpTheJamThumbupthejamPostPath = '/thumbupthejam';

  /**
   * Thumb Up The Jam.
   *
   * Shortcut for sending thumbs up to a message.
   *
   * Thumb up the jam, let your feedback be heard \n
   * Help the bot improve with your every word \n
   * If it's spot-on or a little spam \n
   * Don't hesitate, just thumb up the jam \n
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `thumbUpTheJamThumbupthejamPost()` instead.
   *
   * This method doesn't expect any request body.
   */
  thumbUpTheJamThumbupthejamPost$Response(params: ThumbUpTheJamThumbupthejamPost$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<ChatMessage>>> {
    return thumbUpTheJamThumbupthejamPost(this.http, this.rootUrl, params, context);
  }

  /**
   * Thumb Up The Jam.
   *
   * Shortcut for sending thumbs up to a message.
   *
   * Thumb up the jam, let your feedback be heard \n
   * Help the bot improve with your every word \n
   * If it's spot-on or a little spam \n
   * Don't hesitate, just thumb up the jam \n
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `thumbUpTheJamThumbupthejamPost$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  thumbUpTheJamThumbupthejamPost(params: ThumbUpTheJamThumbupthejamPost$Params, context?: HttpContext): Observable<Array<ChatMessage>> {
    return this.thumbUpTheJamThumbupthejamPost$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<ChatMessage>>): Array<ChatMessage> => r.body)
    );
  }

}
