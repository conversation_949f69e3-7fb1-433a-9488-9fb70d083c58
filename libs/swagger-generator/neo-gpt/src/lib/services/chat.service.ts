/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { ChatMessage } from '../models/chat-message';
import { deleteChatHistoryChatHistoryDelete } from '../fn/chat/delete-chat-history-chat-history-delete';
import { DeleteChatHistoryChatHistoryDelete$Params } from '../fn/chat/delete-chat-history-chat-history-delete';
import { fetchHistoryChatHistoryGet } from '../fn/chat/fetch-history-chat-history-get';
import { FetchHistoryChatHistoryGet$Params } from '../fn/chat/fetch-history-chat-history-get';
import { getUserChatPreferenceChatUserSettingsGetGet } from '../fn/chat/get-user-chat-preference-chat-user-settings-get-get';
import { GetUserChatPreferenceChatUserSettingsGetGet$Params } from '../fn/chat/get-user-chat-preference-chat-user-settings-get-get';
import { resetHistoryPreferenceChatUserSettingsResetPost } from '../fn/chat/reset-history-preference-chat-user-settings-reset-post';
import { ResetHistoryPreferenceChatUserSettingsResetPost$Params } from '../fn/chat/reset-history-preference-chat-user-settings-reset-post';
import { setLastSeenMessageChatSetLastSeenMessagePost } from '../fn/chat/set-last-seen-message-chat-set-last-seen-message-post';
import { SetLastSeenMessageChatSetLastSeenMessagePost$Params } from '../fn/chat/set-last-seen-message-chat-set-last-seen-message-post';
import { updateUserChatPreferencesChatUserSettingsUpdatePatch } from '../fn/chat/update-user-chat-preferences-chat-user-settings-update-patch';
import { UpdateUserChatPreferencesChatUserSettingsUpdatePatch$Params } from '../fn/chat/update-user-chat-preferences-chat-user-settings-update-patch';

@Injectable({ providedIn: 'root' })
export class ChatService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `fetchHistoryChatHistoryGet()` */
  static readonly FetchHistoryChatHistoryGetPath = '/chat/history';

  /**
   * Fetch History.
   *
   * Fetch the chat history for a given session ID.
   *
   * Parameters
   * ----------
   * start : int
   *     Start chat history index
   * stop : int
   *     End chat history index
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   *     Defaults to the result of invoking Controller.get_controller.
   *
   * Returns
   * -------
   * List[ChatMessage]
   *     The chat history for the specified session ID.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `fetchHistoryChatHistoryGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  fetchHistoryChatHistoryGet$Response(params: FetchHistoryChatHistoryGet$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<ChatMessage>>> {
    return fetchHistoryChatHistoryGet(this.http, this.rootUrl, params, context);
  }

  /**
   * Fetch History.
   *
   * Fetch the chat history for a given session ID.
   *
   * Parameters
   * ----------
   * start : int
   *     Start chat history index
   * stop : int
   *     End chat history index
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   *     Defaults to the result of invoking Controller.get_controller.
   *
   * Returns
   * -------
   * List[ChatMessage]
   *     The chat history for the specified session ID.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `fetchHistoryChatHistoryGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  fetchHistoryChatHistoryGet(params: FetchHistoryChatHistoryGet$Params, context?: HttpContext): Observable<Array<ChatMessage>> {
    return this.fetchHistoryChatHistoryGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<ChatMessage>>): Array<ChatMessage> => r.body)
    );
  }

  /** Path part for operation `deleteChatHistoryChatHistoryDelete()` */
  static readonly DeleteChatHistoryChatHistoryDeletePath = '/chat/history';

  /**
   * Delete Chat History.
   *
   * Delete the history for the provided user session.
   *
   * Parameters
   * ----------
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteChatHistoryChatHistoryDelete()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteChatHistoryChatHistoryDelete$Response(params: DeleteChatHistoryChatHistoryDelete$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return deleteChatHistoryChatHistoryDelete(this.http, this.rootUrl, params, context);
  }

  /**
   * Delete Chat History.
   *
   * Delete the history for the provided user session.
   *
   * Parameters
   * ----------
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteChatHistoryChatHistoryDelete$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteChatHistoryChatHistoryDelete(params: DeleteChatHistoryChatHistoryDelete$Params, context?: HttpContext): Observable<any> {
    return this.deleteChatHistoryChatHistoryDelete$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /** Path part for operation `setLastSeenMessageChatSetLastSeenMessagePost()` */
  static readonly SetLastSeenMessageChatSetLastSeenMessagePostPath = '/chat/set_last_seen_message';

  /**
   * Set Last Seen Message.
   *
   * Set the is_seen status of all current chat messages.
   *
   * Parameters
   * ----------
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   *
   * Returns
   * -------
   * List[ChatMessage]
   *     The updated chat history for the specified session ID.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `setLastSeenMessageChatSetLastSeenMessagePost()` instead.
   *
   * This method doesn't expect any request body.
   */
  setLastSeenMessageChatSetLastSeenMessagePost$Response(params: SetLastSeenMessageChatSetLastSeenMessagePost$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<ChatMessage>>> {
    return setLastSeenMessageChatSetLastSeenMessagePost(this.http, this.rootUrl, params, context);
  }

  /**
   * Set Last Seen Message.
   *
   * Set the is_seen status of all current chat messages.
   *
   * Parameters
   * ----------
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   *
   * Returns
   * -------
   * List[ChatMessage]
   *     The updated chat history for the specified session ID.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `setLastSeenMessageChatSetLastSeenMessagePost$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  setLastSeenMessageChatSetLastSeenMessagePost(params: SetLastSeenMessageChatSetLastSeenMessagePost$Params, context?: HttpContext): Observable<Array<ChatMessage>> {
    return this.setLastSeenMessageChatSetLastSeenMessagePost$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<ChatMessage>>): Array<ChatMessage> => r.body)
    );
  }

  /** Path part for operation `resetHistoryPreferenceChatUserSettingsResetPost()` */
  static readonly ResetHistoryPreferenceChatUserSettingsResetPostPath = '/chat/user-settings/reset';

  /**
   * Reset History Preference.
   *
   * Reset the user preferences to default.
   *
   * This effectively deletes the custom settings for the user.
   *
   * Parameters
   * ----------
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `resetHistoryPreferenceChatUserSettingsResetPost()` instead.
   *
   * This method doesn't expect any request body.
   */
  resetHistoryPreferenceChatUserSettingsResetPost$Response(params: ResetHistoryPreferenceChatUserSettingsResetPost$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return resetHistoryPreferenceChatUserSettingsResetPost(this.http, this.rootUrl, params, context);
  }

  /**
   * Reset History Preference.
   *
   * Reset the user preferences to default.
   *
   * This effectively deletes the custom settings for the user.
   *
   * Parameters
   * ----------
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `resetHistoryPreferenceChatUserSettingsResetPost$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  resetHistoryPreferenceChatUserSettingsResetPost(params: ResetHistoryPreferenceChatUserSettingsResetPost$Params, context?: HttpContext): Observable<any> {
    return this.resetHistoryPreferenceChatUserSettingsResetPost$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /** Path part for operation `updateUserChatPreferencesChatUserSettingsUpdatePatch()` */
  static readonly UpdateUserChatPreferencesChatUserSettingsUpdatePatchPath = '/chat/user-settings/update';

  /**
   * Update User Chat Preferences.
   *
   * Set the user preferences.
   *
   * Parameters
   * ----------
   * preferences_to_set: UserChatPreferences
   *     The user settings that are desired to be changed
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `updateUserChatPreferencesChatUserSettingsUpdatePatch()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  updateUserChatPreferencesChatUserSettingsUpdatePatch$Response(params: UpdateUserChatPreferencesChatUserSettingsUpdatePatch$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return updateUserChatPreferencesChatUserSettingsUpdatePatch(this.http, this.rootUrl, params, context);
  }

  /**
   * Update User Chat Preferences.
   *
   * Set the user preferences.
   *
   * Parameters
   * ----------
   * preferences_to_set: UserChatPreferences
   *     The user settings that are desired to be changed
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `updateUserChatPreferencesChatUserSettingsUpdatePatch$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  updateUserChatPreferencesChatUserSettingsUpdatePatch(params: UpdateUserChatPreferencesChatUserSettingsUpdatePatch$Params, context?: HttpContext): Observable<any> {
    return this.updateUserChatPreferencesChatUserSettingsUpdatePatch$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /** Path part for operation `getUserChatPreferenceChatUserSettingsGetGet()` */
  static readonly GetUserChatPreferenceChatUserSettingsGetGetPath = '/chat/user-settings/get';

  /**
   * Get User Chat Preference.
   *
   * Get the user preferences.
   *
   * Parameters
   * ----------
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getUserChatPreferenceChatUserSettingsGetGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserChatPreferenceChatUserSettingsGetGet$Response(params: GetUserChatPreferenceChatUserSettingsGetGet$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return getUserChatPreferenceChatUserSettingsGetGet(this.http, this.rootUrl, params, context);
  }

  /**
   * Get User Chat Preference.
   *
   * Get the user preferences.
   *
   * Parameters
   * ----------
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getUserChatPreferenceChatUserSettingsGetGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserChatPreferenceChatUserSettingsGetGet(params: GetUserChatPreferenceChatUserSettingsGetGet$Params, context?: HttpContext): Observable<any> {
    return this.getUserChatPreferenceChatUserSettingsGetGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

}
