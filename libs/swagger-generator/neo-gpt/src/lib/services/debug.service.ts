/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { getCurrentlyLoadedHardcodingsInternalGetCurrentlyLoadedHardcodingsGet } from '../fn/debug/get-currently-loaded-hardcodings-internal-get-currently-loaded-hardcodings-get';
import { GetCurrentlyLoadedHardcodingsInternalGetCurrentlyLoadedHardcodingsGet$Params } from '../fn/debug/get-currently-loaded-hardcodings-internal-get-currently-loaded-hardcodings-get';
import { gpuSupportInternalGpuSupportGet } from '../fn/debug/gpu-support-internal-gpu-support-get';
import { GpuSupportInternalGpuSupportGet$Params } from '../fn/debug/gpu-support-internal-gpu-support-get';
import { healthCheckInternalHealthGet } from '../fn/debug/health-check-internal-health-get';
import { HealthCheckInternalHealthGet$Params } from '../fn/debug/health-check-internal-health-get';
import { nFailedJobsInternalFailedJobsCountPost } from '../fn/debug/n-failed-jobs-internal-failed-jobs-count-post';
import { NFailedJobsInternalFailedJobsCountPost$Params } from '../fn/debug/n-failed-jobs-internal-failed-jobs-count-post';
import { queueLengthInternalQueueLengthPost } from '../fn/debug/queue-length-internal-queue-length-post';
import { QueueLengthInternalQueueLengthPost$Params } from '../fn/debug/queue-length-internal-queue-length-post';
import { redisPingInternalPingRedisGet } from '../fn/debug/redis-ping-internal-ping-redis-get';
import { RedisPingInternalPingRedisGet$Params } from '../fn/debug/redis-ping-internal-ping-redis-get';
import { reloadHardcodingsInternalReloadHardcodingsPut } from '../fn/debug/reload-hardcodings-internal-reload-hardcodings-put';
import { ReloadHardcodingsInternalReloadHardcodingsPut$Params } from '../fn/debug/reload-hardcodings-internal-reload-hardcodings-put';
import { retryJobsInternalRetryFailedJobsPost } from '../fn/debug/retry-jobs-internal-retry-failed-jobs-post';
import { RetryJobsInternalRetryFailedJobsPost$Params } from '../fn/debug/retry-jobs-internal-retry-failed-jobs-post';
import { testDemoHardcodingsInternalTestDemoHardcodingsPost } from '../fn/debug/test-demo-hardcodings-internal-test-demo-hardcodings-post';
import { TestDemoHardcodingsInternalTestDemoHardcodingsPost$Params } from '../fn/debug/test-demo-hardcodings-internal-test-demo-hardcodings-post';

@Injectable({ providedIn: 'root' })
export class DebugService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `gpuSupportInternalGpuSupportGet()` */
  static readonly GpuSupportInternalGpuSupportGetPath = '/internal/gpu-support';

  /**
   * Gpu Support.
   *
   * Endpoint to check if the GPU is working.
   *
   * Currently, this endpoint is deprecated and will return always False.
   * This is expected as the backend, does not need access to the GPU.
   * It is only the OCR worker that does need it.
   *
   * This deprecation is necessary, because we had to maintain a significantly
   * larger image of the backend service only to support this endpoint.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `gpuSupportInternalGpuSupportGet()` instead.
   *
   * This method doesn't expect any request body.
   *
   * @deprecated
   */
  gpuSupportInternalGpuSupportGet$Response(params?: GpuSupportInternalGpuSupportGet$Params, context?: HttpContext): Observable<StrictHttpResponse<boolean>> {
    return gpuSupportInternalGpuSupportGet(this.http, this.rootUrl, params, context);
  }

  /**
   * Gpu Support.
   *
   * Endpoint to check if the GPU is working.
   *
   * Currently, this endpoint is deprecated and will return always False.
   * This is expected as the backend, does not need access to the GPU.
   * It is only the OCR worker that does need it.
   *
   * This deprecation is necessary, because we had to maintain a significantly
   * larger image of the backend service only to support this endpoint.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `gpuSupportInternalGpuSupportGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   *
   * @deprecated
   */
  gpuSupportInternalGpuSupportGet(params?: GpuSupportInternalGpuSupportGet$Params, context?: HttpContext): Observable<boolean> {
    return this.gpuSupportInternalGpuSupportGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<boolean>): boolean => r.body)
    );
  }

  /** Path part for operation `redisPingInternalPingRedisGet()` */
  static readonly RedisPingInternalPingRedisGetPath = '/internal/ping-redis';

  /**
   * Redis Ping.
   *
   * Check the redis DB connection.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `redisPingInternalPingRedisGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  redisPingInternalPingRedisGet$Response(params?: RedisPingInternalPingRedisGet$Params, context?: HttpContext): Observable<StrictHttpResponse<boolean>> {
    return redisPingInternalPingRedisGet(this.http, this.rootUrl, params, context);
  }

  /**
   * Redis Ping.
   *
   * Check the redis DB connection.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `redisPingInternalPingRedisGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  redisPingInternalPingRedisGet(params?: RedisPingInternalPingRedisGet$Params, context?: HttpContext): Observable<boolean> {
    return this.redisPingInternalPingRedisGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<boolean>): boolean => r.body)
    );
  }

  /** Path part for operation `healthCheckInternalHealthGet()` */
  static readonly HealthCheckInternalHealthGetPath = '/internal/health';

  /**
   * Health Check.
   *
   * Check the health of the app.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `healthCheckInternalHealthGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  healthCheckInternalHealthGet$Response(params?: HealthCheckInternalHealthGet$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return healthCheckInternalHealthGet(this.http, this.rootUrl, params, context);
  }

  /**
   * Health Check.
   *
   * Check the health of the app.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `healthCheckInternalHealthGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  healthCheckInternalHealthGet(params?: HealthCheckInternalHealthGet$Params, context?: HttpContext): Observable<any> {
    return this.healthCheckInternalHealthGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /** Path part for operation `queueLengthInternalQueueLengthPost()` */
  static readonly QueueLengthInternalQueueLengthPostPath = '/internal/queue_length';

  /**
   * Queue Length.
   *
   * Return the length of the queues.
   *
   * Returns
   * -------
   * lengths: QueueLengths
   *     The lengths of the queues
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `queueLengthInternalQueueLengthPost()` instead.
   *
   * This method doesn't expect any request body.
   */
  queueLengthInternalQueueLengthPost$Response(params?: QueueLengthInternalQueueLengthPost$Params, context?: HttpContext): Observable<StrictHttpResponse<{
}>> {
    return queueLengthInternalQueueLengthPost(this.http, this.rootUrl, params, context);
  }

  /**
   * Queue Length.
   *
   * Return the length of the queues.
   *
   * Returns
   * -------
   * lengths: QueueLengths
   *     The lengths of the queues
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `queueLengthInternalQueueLengthPost$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  queueLengthInternalQueueLengthPost(params?: QueueLengthInternalQueueLengthPost$Params, context?: HttpContext): Observable<{
}> {
    return this.queueLengthInternalQueueLengthPost$Response(params, context).pipe(
      map((r: StrictHttpResponse<{
}>): {
} => r.body)
    );
  }

  /** Path part for operation `nFailedJobsInternalFailedJobsCountPost()` */
  static readonly NFailedJobsInternalFailedJobsCountPostPath = '/internal/failed_jobs_count';

  /**
   * N Failed Jobs.
   *
   * Return the number of failed jobs for each queue.
   *
   * Returns
   * -------
   * lengths: QueueLengths
   *     The lengths of the queues
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `nFailedJobsInternalFailedJobsCountPost()` instead.
   *
   * This method doesn't expect any request body.
   */
  nFailedJobsInternalFailedJobsCountPost$Response(params?: NFailedJobsInternalFailedJobsCountPost$Params, context?: HttpContext): Observable<StrictHttpResponse<{
}>> {
    return nFailedJobsInternalFailedJobsCountPost(this.http, this.rootUrl, params, context);
  }

  /**
   * N Failed Jobs.
   *
   * Return the number of failed jobs for each queue.
   *
   * Returns
   * -------
   * lengths: QueueLengths
   *     The lengths of the queues
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `nFailedJobsInternalFailedJobsCountPost$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  nFailedJobsInternalFailedJobsCountPost(params?: NFailedJobsInternalFailedJobsCountPost$Params, context?: HttpContext): Observable<{
}> {
    return this.nFailedJobsInternalFailedJobsCountPost$Response(params, context).pipe(
      map((r: StrictHttpResponse<{
}>): {
} => r.body)
    );
  }

  /** Path part for operation `retryJobsInternalRetryFailedJobsPost()` */
  static readonly RetryJobsInternalRetryFailedJobsPostPath = '/internal/retry-failed-jobs';

  /**
   * Retry Jobs.
   *
   * Retry failed jobs from a specified queue with optional filtering.
   *
   * Notes
   * -----
   * For more details, refer to the 'RedisQAO.requeue_failed_jobs' method documentation.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `retryJobsInternalRetryFailedJobsPost()` instead.
   *
   * This method doesn't expect any request body.
   */
  retryJobsInternalRetryFailedJobsPost$Response(params: RetryJobsInternalRetryFailedJobsPost$Params, context?: HttpContext): Observable<StrictHttpResponse<{
[key: string]: string;
}>> {
    return retryJobsInternalRetryFailedJobsPost(this.http, this.rootUrl, params, context);
  }

  /**
   * Retry Jobs.
   *
   * Retry failed jobs from a specified queue with optional filtering.
   *
   * Notes
   * -----
   * For more details, refer to the 'RedisQAO.requeue_failed_jobs' method documentation.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `retryJobsInternalRetryFailedJobsPost$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  retryJobsInternalRetryFailedJobsPost(params: RetryJobsInternalRetryFailedJobsPost$Params, context?: HttpContext): Observable<{
[key: string]: string;
}> {
    return this.retryJobsInternalRetryFailedJobsPost$Response(params, context).pipe(
      map((r: StrictHttpResponse<{
[key: string]: string;
}>): {
[key: string]: string;
} => r.body)
    );
  }

  /** Path part for operation `reloadHardcodingsInternalReloadHardcodingsPut()` */
  static readonly ReloadHardcodingsInternalReloadHardcodingsPutPath = '/internal/reload_hardcodings';

  /**
   * Reload Hardcodings.
   *
   * Reload the json containing hardcoded questions and answers.
   *
   * Parameters
   * ----------
   * gitlab_access_token : str
   *     If provided, use that to access the hardcodings.
   * controller : Controller
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `reloadHardcodingsInternalReloadHardcodingsPut()` instead.
   *
   * This method doesn't expect any request body.
   */
  reloadHardcodingsInternalReloadHardcodingsPut$Response(params?: ReloadHardcodingsInternalReloadHardcodingsPut$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return reloadHardcodingsInternalReloadHardcodingsPut(this.http, this.rootUrl, params, context);
  }

  /**
   * Reload Hardcodings.
   *
   * Reload the json containing hardcoded questions and answers.
   *
   * Parameters
   * ----------
   * gitlab_access_token : str
   *     If provided, use that to access the hardcodings.
   * controller : Controller
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `reloadHardcodingsInternalReloadHardcodingsPut$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  reloadHardcodingsInternalReloadHardcodingsPut(params?: ReloadHardcodingsInternalReloadHardcodingsPut$Params, context?: HttpContext): Observable<void> {
    return this.reloadHardcodingsInternalReloadHardcodingsPut$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getCurrentlyLoadedHardcodingsInternalGetCurrentlyLoadedHardcodingsGet()` */
  static readonly GetCurrentlyLoadedHardcodingsInternalGetCurrentlyLoadedHardcodingsGetPath = '/internal/get_currently_loaded_hardcodings';

  /**
   * Get Currently Loaded Hardcodings.
   *
   * Get the currently loaded hardcoding questions.
   *
   * If hardcoder is present, get the hardcoded questions. If not present, raise 404.
   *
   * Parameters
   * ----------
   * controller : Controller
   * controller.hardcoder.reload_hardcoded_answers(gitlab_access_token)
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getCurrentlyLoadedHardcodingsInternalGetCurrentlyLoadedHardcodingsGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCurrentlyLoadedHardcodingsInternalGetCurrentlyLoadedHardcodingsGet$Response(params?: GetCurrentlyLoadedHardcodingsInternalGetCurrentlyLoadedHardcodingsGet$Params, context?: HttpContext): Observable<StrictHttpResponse<({
} | null)>> {
    return getCurrentlyLoadedHardcodingsInternalGetCurrentlyLoadedHardcodingsGet(this.http, this.rootUrl, params, context);
  }

  /**
   * Get Currently Loaded Hardcodings.
   *
   * Get the currently loaded hardcoding questions.
   *
   * If hardcoder is present, get the hardcoded questions. If not present, raise 404.
   *
   * Parameters
   * ----------
   * controller : Controller
   * controller.hardcoder.reload_hardcoded_answers(gitlab_access_token)
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getCurrentlyLoadedHardcodingsInternalGetCurrentlyLoadedHardcodingsGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCurrentlyLoadedHardcodingsInternalGetCurrentlyLoadedHardcodingsGet(params?: GetCurrentlyLoadedHardcodingsInternalGetCurrentlyLoadedHardcodingsGet$Params, context?: HttpContext): Observable<({
} | null)> {
    return this.getCurrentlyLoadedHardcodingsInternalGetCurrentlyLoadedHardcodingsGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<({
} | null)>): ({
} | null) => r.body)
    );
  }

  /** Path part for operation `testDemoHardcodingsInternalTestDemoHardcodingsPost()` */
  static readonly TestDemoHardcodingsInternalTestDemoHardcodingsPostPath = '/internal/test_demo_hardcodings';

  /**
   * Test Demo Hardcodings.
   *
   * Reload the json containing hardcoded questions and answers
   *
   * Parameters
   * ----------
   * request_body : TestDemoHardcodingsBody
   *     Request body containing a token, the business case ids to be tested and
   *     the origin against which to send requests to the BE.
   * controller : Controller
   * permissions_manager: PermissionsManager
   *
   * Returns
   * -------
   * dict[str, Any]
   *     The test report, which has several summary metrics and a list of the hardcodings
   *     tested.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `testDemoHardcodingsInternalTestDemoHardcodingsPost()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  testDemoHardcodingsInternalTestDemoHardcodingsPost$Response(params: TestDemoHardcodingsInternalTestDemoHardcodingsPost$Params, context?: HttpContext): Observable<StrictHttpResponse<{
}>> {
    return testDemoHardcodingsInternalTestDemoHardcodingsPost(this.http, this.rootUrl, params, context);
  }

  /**
   * Test Demo Hardcodings.
   *
   * Reload the json containing hardcoded questions and answers
   *
   * Parameters
   * ----------
   * request_body : TestDemoHardcodingsBody
   *     Request body containing a token, the business case ids to be tested and
   *     the origin against which to send requests to the BE.
   * controller : Controller
   * permissions_manager: PermissionsManager
   *
   * Returns
   * -------
   * dict[str, Any]
   *     The test report, which has several summary metrics and a list of the hardcodings
   *     tested.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `testDemoHardcodingsInternalTestDemoHardcodingsPost$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  testDemoHardcodingsInternalTestDemoHardcodingsPost(params: TestDemoHardcodingsInternalTestDemoHardcodingsPost$Params, context?: HttpContext): Observable<{
}> {
    return this.testDemoHardcodingsInternalTestDemoHardcodingsPost$Response(params, context).pipe(
      map((r: StrictHttpResponse<{
}>): {
} => r.body)
    );
  }

}
