/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { ChatMessage } from '../models/chat-message';
import { thumbUpTheJamThumbupthejamPost } from '../fn/feedback/thumb-up-the-jam-thumbupthejam-post';
import { ThumbUpTheJamThumbupthejamPost$Params } from '../fn/feedback/thumb-up-the-jam-thumbupthejam-post';

@Injectable({ providedIn: 'root' })
export class FunService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `thumbUpTheJamThumbupthejamPost()` */
  static readonly ThumbUpTheJamThumbupthejamPostPath = '/thumbupthejam';

  /**
   * Thumb Up The Jam.
   *
   * Shortcut for sending thumbs up to a message.
   *
   * Thumb up the jam, let your feedback be heard \n
   * Help the bot improve with your every word \n
   * If it's spot-on or a little spam \n
   * Don't hesitate, just thumb up the jam \n
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `thumbUpTheJamThumbupthejamPost()` instead.
   *
   * This method doesn't expect any request body.
   */
  thumbUpTheJamThumbupthejamPost$Response(params: ThumbUpTheJamThumbupthejamPost$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<ChatMessage>>> {
    return thumbUpTheJamThumbupthejamPost(this.http, this.rootUrl, params, context);
  }

  /**
   * Thumb Up The Jam.
   *
   * Shortcut for sending thumbs up to a message.
   *
   * Thumb up the jam, let your feedback be heard \n
   * Help the bot improve with your every word \n
   * If it's spot-on or a little spam \n
   * Don't hesitate, just thumb up the jam \n
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `thumbUpTheJamThumbupthejamPost$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  thumbUpTheJamThumbupthejamPost(params: ThumbUpTheJamThumbupthejamPost$Params, context?: HttpContext): Observable<Array<ChatMessage>> {
    return this.thumbUpTheJamThumbupthejamPost$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<ChatMessage>>): Array<ChatMessage> => r.body)
    );
  }

}
