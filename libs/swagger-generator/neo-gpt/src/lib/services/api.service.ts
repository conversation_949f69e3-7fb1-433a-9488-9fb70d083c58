/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { forwardingGet } from '../fn/operations/forwarding-get';
import { ForwardingGet$Params } from '../fn/operations/forwarding-get';

@Injectable({ providedIn: 'root' })
export class ApiService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `forwardingGet()` */
  static readonly ForwardingGetPath = '/';

  /**
   * Forwarding.
   *
   * Provide a page to forward to the docs.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `forwardingGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  forwardingGet$Response(params?: ForwardingGet$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return forwardingGet(this.http, this.rootUrl, params, context);
  }

  /**
   * Forwarding.
   *
   * Provide a page to forward to the docs.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `forwardingGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  forwardingGet(params?: ForwardingGet$Params, context?: HttpContext): Observable<any> {
    return this.forwardingGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

}
