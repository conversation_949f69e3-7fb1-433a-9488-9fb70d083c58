/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { getDocumentStatusDocumentStatusGet } from '../fn/ingestion/get-document-status-document-status-get';
import { GetDocumentStatusDocumentStatusGet$Params } from '../fn/ingestion/get-document-status-document-status-get';
import { ingestFieldInternalIngestFieldPost } from '../fn/ingestion/ingest-field-internal-ingest-field-post';
import { IngestFieldInternalIngestFieldPost$Params } from '../fn/ingestion/ingest-field-internal-ingest-field-post';
import { ingestFinStructureInternalIngestFinStructurePost } from '../fn/ingestion/ingest-fin-structure-internal-ingest-fin-structure-post';
import { IngestFinStructureInternalIngestFinStructurePost$Params } from '../fn/ingestion/ingest-fin-structure-internal-ingest-fin-structure-post';
import { ingestInternalIngestDocumentIdPost } from '../fn/ingestion/ingest-internal-ingest-document-id-post';
import { IngestInternalIngestDocumentIdPost$Params } from '../fn/ingestion/ingest-internal-ingest-document-id-post';

@Injectable({ providedIn: 'root' })
export class IngestionService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `ingestInternalIngestDocumentIdPost()` */
  static readonly IngestInternalIngestDocumentIdPostPath = '/internal/ingest/{document_id}';

  /**
   * Ingest.
   *
   * Ingest a document for question answering and summarization.
   *
   * This endpoint processes a document for question answering and summarization across multiple languages.
   * The document can be provided directly or referenced via an S3 bucket and key. The processing involves
   * chunking the document, embedding the chunks using vector stores, and summarizing them using map-reduce-based
   * chains. The summarized data is then stored in Redis for efficient retrieval.
   *
   * Temporarily, we start and stop the Kafka Producer in this function if the `use_document_harvester` flag is True.
   * The most efficient way would be to add
   *
   * ```
   * api.state.producer = Producer(**PRODUCER_SETTINGS.model_dump())
   * await api.state.producer.start()
   * yield
   * await api.state.producer.stop()
   * ```
   *
   * to `interface.main.get_api` and to add the request argument to this endpoint.
   * We could then call `requests.app.state.producer.send_and_wait()` in this ingest endpoint.
   *
   * However, temporarily we would need to make this dependent on an environmental variable
   * as flag in `get_api` which is less obvious than having this temporary solution live only at one place here.
   *
   * Also note that the `api.state` attribute would solve the previous mypy issue with `api.producer = ...`
   * where we set a non-existing FastAPI attribute.
   *
   * Parameters
   * ----------
   * request
   *     The Starlette Request object that contains metadata about the HTTP request.
   * document
   *     The document file to be processed. Direct file upload is deprecated; use S3 bucket and key instead.
   * document_id
   *     The unique identifier for the document used by the service.
   * bucket_name
   *     The name of the S3 bucket where the document is stored.
   * document_key
   *     The key/path of the document within the S3 bucket.
   * business_case_id
   *     The identifier for the business case to which the document is assigned.
   * use_document_harvester
   *     Flag indicating whether to use the Document Harvester for processing, storing, and embedding documents.
   * low_priority
   *     Flag to set the processing priority of the document. If True, the document is sent to lower priority queues.
   * filename
   *     The filename of the document.
   * controller
   *     The controller instance responsible for handling document ingestion logic.
   *
   * Returns
   * -------
   * None
   *     This endpoint does not return any content. It initiates the document processing workflow.
   *
   * Raises
   * ------
   * HTTPException
   *     Raised if required parameters are missing or if there are issues accessing the document in S3.
   *
   * Notes
   * -----
   * - When 'use_document_harvester' is True, the Document Harvester service handles processing.
   * - If 'document' is provided directly, it's processed immediately, but this approach is deprecated.
   * - Proper error handling ensures that issues with S3 access or missing parameters are reported appropriately.
   *
   * See Also
   * --------
   * Controller.ingest_document : Method to ingest documents using the Document Harvester.
   * s3_object_exists : Utility function to check if an object exists in S3.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `ingestInternalIngestDocumentIdPost()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  ingestInternalIngestDocumentIdPost$Response(params: IngestInternalIngestDocumentIdPost$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return ingestInternalIngestDocumentIdPost(this.http, this.rootUrl, params, context);
  }

  /**
   * Ingest.
   *
   * Ingest a document for question answering and summarization.
   *
   * This endpoint processes a document for question answering and summarization across multiple languages.
   * The document can be provided directly or referenced via an S3 bucket and key. The processing involves
   * chunking the document, embedding the chunks using vector stores, and summarizing them using map-reduce-based
   * chains. The summarized data is then stored in Redis for efficient retrieval.
   *
   * Temporarily, we start and stop the Kafka Producer in this function if the `use_document_harvester` flag is True.
   * The most efficient way would be to add
   *
   * ```
   * api.state.producer = Producer(**PRODUCER_SETTINGS.model_dump())
   * await api.state.producer.start()
   * yield
   * await api.state.producer.stop()
   * ```
   *
   * to `interface.main.get_api` and to add the request argument to this endpoint.
   * We could then call `requests.app.state.producer.send_and_wait()` in this ingest endpoint.
   *
   * However, temporarily we would need to make this dependent on an environmental variable
   * as flag in `get_api` which is less obvious than having this temporary solution live only at one place here.
   *
   * Also note that the `api.state` attribute would solve the previous mypy issue with `api.producer = ...`
   * where we set a non-existing FastAPI attribute.
   *
   * Parameters
   * ----------
   * request
   *     The Starlette Request object that contains metadata about the HTTP request.
   * document
   *     The document file to be processed. Direct file upload is deprecated; use S3 bucket and key instead.
   * document_id
   *     The unique identifier for the document used by the service.
   * bucket_name
   *     The name of the S3 bucket where the document is stored.
   * document_key
   *     The key/path of the document within the S3 bucket.
   * business_case_id
   *     The identifier for the business case to which the document is assigned.
   * use_document_harvester
   *     Flag indicating whether to use the Document Harvester for processing, storing, and embedding documents.
   * low_priority
   *     Flag to set the processing priority of the document. If True, the document is sent to lower priority queues.
   * filename
   *     The filename of the document.
   * controller
   *     The controller instance responsible for handling document ingestion logic.
   *
   * Returns
   * -------
   * None
   *     This endpoint does not return any content. It initiates the document processing workflow.
   *
   * Raises
   * ------
   * HTTPException
   *     Raised if required parameters are missing or if there are issues accessing the document in S3.
   *
   * Notes
   * -----
   * - When 'use_document_harvester' is True, the Document Harvester service handles processing.
   * - If 'document' is provided directly, it's processed immediately, but this approach is deprecated.
   * - Proper error handling ensures that issues with S3 access or missing parameters are reported appropriately.
   *
   * See Also
   * --------
   * Controller.ingest_document : Method to ingest documents using the Document Harvester.
   * s3_object_exists : Utility function to check if an object exists in S3.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `ingestInternalIngestDocumentIdPost$Response()` instead.
   *
   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.
   */
  ingestInternalIngestDocumentIdPost(params: IngestInternalIngestDocumentIdPost$Params, context?: HttpContext): Observable<any> {
    return this.ingestInternalIngestDocumentIdPost$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /** Path part for operation `getDocumentStatusDocumentStatusGet()` */
  static readonly GetDocumentStatusDocumentStatusGetPath = '/document_status';

  /**
   * Get Document Status.
   *
   * Get the status of a document.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getDocumentStatusDocumentStatusGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  getDocumentStatusDocumentStatusGet$Response(params: GetDocumentStatusDocumentStatusGet$Params, context?: HttpContext): Observable<StrictHttpResponse<{
}>> {
    return getDocumentStatusDocumentStatusGet(this.http, this.rootUrl, params, context);
  }

  /**
   * Get Document Status.
   *
   * Get the status of a document.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getDocumentStatusDocumentStatusGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getDocumentStatusDocumentStatusGet(params: GetDocumentStatusDocumentStatusGet$Params, context?: HttpContext): Observable<{
}> {
    return this.getDocumentStatusDocumentStatusGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<{
}>): {
} => r.body)
    );
  }

  /** Path part for operation `ingestFieldInternalIngestFieldPost()` */
  static readonly IngestFieldInternalIngestFieldPostPath = '/internal/ingest_field';

  /**
   * Ingest Field.
   *
   * Ingest a data room field for question answering and summarization.
   *
   * This endpoint allows for ingesting a data room field that will be processed
   * for question answering and summarization in multiple languages.
   *
   * Parameters
   * ----------
   * request : Request
   *     The starlette request object.
   * background_tasks : BackgroundTasks
   *     Background tasks to be executed after the request is completed.
   * data_room_field : DataRoomField
   *     The data room field as a request body sent from srv-business-case-manager.
   * use_document_harvester
   *     Flag indicating whether to use the Document Harvester for processing, storing, and embedding documents.
   * controller
   *     The application controller.
   *
   * Returns
   * -------
   * None
   *     The function does not return anything.
   *
   * Note ---- The processing involves chunking the document, embedding the chunks
   * using vector stores, and summarizing them using map-reduce-based chains for
   * multiple languages.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `ingestFieldInternalIngestFieldPost()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  ingestFieldInternalIngestFieldPost$Response(params: IngestFieldInternalIngestFieldPost$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return ingestFieldInternalIngestFieldPost(this.http, this.rootUrl, params, context);
  }

  /**
   * Ingest Field.
   *
   * Ingest a data room field for question answering and summarization.
   *
   * This endpoint allows for ingesting a data room field that will be processed
   * for question answering and summarization in multiple languages.
   *
   * Parameters
   * ----------
   * request : Request
   *     The starlette request object.
   * background_tasks : BackgroundTasks
   *     Background tasks to be executed after the request is completed.
   * data_room_field : DataRoomField
   *     The data room field as a request body sent from srv-business-case-manager.
   * use_document_harvester
   *     Flag indicating whether to use the Document Harvester for processing, storing, and embedding documents.
   * controller
   *     The application controller.
   *
   * Returns
   * -------
   * None
   *     The function does not return anything.
   *
   * Note ---- The processing involves chunking the document, embedding the chunks
   * using vector stores, and summarizing them using map-reduce-based chains for
   * multiple languages.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `ingestFieldInternalIngestFieldPost$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  ingestFieldInternalIngestFieldPost(params: IngestFieldInternalIngestFieldPost$Params, context?: HttpContext): Observable<any> {
    return this.ingestFieldInternalIngestFieldPost$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /** Path part for operation `ingestFinStructureInternalIngestFinStructurePost()` */
  static readonly IngestFinStructureInternalIngestFinStructurePostPath = '/internal/ingest_fin_structure';

  /**
   * Ingest Fin Structure.
   *
   * Ingest a data room field for question answering and summarization.
   *
   * This endpoint allows for ingesting a data room field that will be processed
   * for question answering and summarization in multiple languages.
   *
   * Parameters
   * ----------
   * financing_structure_data : FinancingStructureUpdate
   *     A pydantic class containing the full financing structure and a list of updated field ids.
   * background_tasks : BackgroundTasks
   *     Background tasks to be executed after the request is completed.
   *     This can be used to offload long-running tasks.
   * use_document_harvester
   *     Flag indicating whether to use the Document Harvester for processing, storing, and embedding documents.
   * controller
   *     The application controller.
   *
   * Returns
   * -------
   * None
   *     The function does not return anything.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `ingestFinStructureInternalIngestFinStructurePost()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  ingestFinStructureInternalIngestFinStructurePost$Response(params: IngestFinStructureInternalIngestFinStructurePost$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return ingestFinStructureInternalIngestFinStructurePost(this.http, this.rootUrl, params, context);
  }

  /**
   * Ingest Fin Structure.
   *
   * Ingest a data room field for question answering and summarization.
   *
   * This endpoint allows for ingesting a data room field that will be processed
   * for question answering and summarization in multiple languages.
   *
   * Parameters
   * ----------
   * financing_structure_data : FinancingStructureUpdate
   *     A pydantic class containing the full financing structure and a list of updated field ids.
   * background_tasks : BackgroundTasks
   *     Background tasks to be executed after the request is completed.
   *     This can be used to offload long-running tasks.
   * use_document_harvester
   *     Flag indicating whether to use the Document Harvester for processing, storing, and embedding documents.
   * controller
   *     The application controller.
   *
   * Returns
   * -------
   * None
   *     The function does not return anything.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `ingestFinStructureInternalIngestFinStructurePost$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  ingestFinStructureInternalIngestFinStructurePost(params: IngestFinStructureInternalIngestFinStructurePost$Params, context?: HttpContext): Observable<any> {
    return this.ingestFinStructureInternalIngestFinStructurePost$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

}
