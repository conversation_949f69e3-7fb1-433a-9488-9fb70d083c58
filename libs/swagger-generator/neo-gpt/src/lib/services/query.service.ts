/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { Answer } from '../models/answer';
import { ChatInitData } from '../models/chat-init-data';
import { loadBusinessCaseLoadBusinessCasePost } from '../fn/query/load-business-case-load-business-case-post';
import { LoadBusinessCaseLoadBusinessCasePost$Params } from '../fn/query/load-business-case-load-business-case-post';
import { queryBusinessCaseV2BusinessCasesQueryPost } from '../fn/query/query-business-case-v-2-business-cases-query-post';
import { QueryBusinessCaseV2BusinessCasesQueryPost$Params } from '../fn/query/query-business-case-v-2-business-cases-query-post';
import { queryDocumentsDocumentsQueryPost } from '../fn/query/query-documents-documents-query-post';
import { QueryDocumentsDocumentsQueryPost$Params } from '../fn/query/query-documents-documents-query-post';
import { startChatStartPost } from '../fn/query/start-chat-start-post';
import { StartChatStartPost$Params } from '../fn/query/start-chat-start-post';

@Injectable({ providedIn: 'root' })
export class QueryService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `loadBusinessCaseLoadBusinessCasePost()` */
  static readonly LoadBusinessCaseLoadBusinessCasePostPath = '/load_business_case';

  /**
   * Load Business Case.
   *
   * Load the Milvus partition of the provided business case id.
   *
   * This endpoint can be used by the FE when the chatbot is opened,
   * so that the business case partition is pre-loaded while the chat is
   * opening in the platform UI.
   *
   * Parameters
   * ----------
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the business case id, and other information.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `loadBusinessCaseLoadBusinessCasePost()` instead.
   *
   * This method doesn't expect any request body.
   */
  loadBusinessCaseLoadBusinessCasePost$Response(params: LoadBusinessCaseLoadBusinessCasePost$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return loadBusinessCaseLoadBusinessCasePost(this.http, this.rootUrl, params, context);
  }

  /**
   * Load Business Case.
   *
   * Load the Milvus partition of the provided business case id.
   *
   * This endpoint can be used by the FE when the chatbot is opened,
   * so that the business case partition is pre-loaded while the chat is
   * opening in the platform UI.
   *
   * Parameters
   * ----------
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the business case id, and other information.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `loadBusinessCaseLoadBusinessCasePost$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  loadBusinessCaseLoadBusinessCasePost(params: LoadBusinessCaseLoadBusinessCasePost$Params, context?: HttpContext): Observable<any> {
    return this.loadBusinessCaseLoadBusinessCasePost$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

  /** Path part for operation `startChatStartPost()` */
  static readonly StartChatStartPostPath = '/start';

  /**
   * Start Chat.
   *
   * Fetch all the information needed for the chatbot to start a conversation.
   *
   * Parameters
   * ----------
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   * force_generate : bool
   *     If True, forces the regeneration of the questions and summary for the document.
   * lang: ModelLanguages
   *     an Enum with the languages supported by the app
   *
   * Returns
   * -------
   * ChatInitData
   *     The data needed for the start of a conversation by the chatbot
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `startChatStartPost()` instead.
   *
   * This method doesn't expect any request body.
   */
  startChatStartPost$Response(params: StartChatStartPost$Params, context?: HttpContext): Observable<StrictHttpResponse<ChatInitData>> {
    return startChatStartPost(this.http, this.rootUrl, params, context);
  }

  /**
   * Start Chat.
   *
   * Fetch all the information needed for the chatbot to start a conversation.
   *
   * Parameters
   * ----------
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id, and other information.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   * force_generate : bool
   *     If True, forces the regeneration of the questions and summary for the document.
   * lang: ModelLanguages
   *     an Enum with the languages supported by the app
   *
   * Returns
   * -------
   * ChatInitData
   *     The data needed for the start of a conversation by the chatbot
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `startChatStartPost$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  startChatStartPost(params: StartChatStartPost$Params, context?: HttpContext): Observable<ChatInitData> {
    return this.startChatStartPost$Response(params, context).pipe(
      map((r: StrictHttpResponse<ChatInitData>): ChatInitData => r.body)
    );
  }

  /** Path part for operation `queryDocumentsDocumentsQueryPost()` */
  static readonly QueryDocumentsDocumentsQueryPostPath = '/documents/query';

  /**
   * Query Documents.
   *
   * Answer the user's text query to a document.
   *
   * This endpoint processes the user's question and generates an answer using a
   * ChatGPT-like language model and a vector store. It interacts with a Redis-based
   * chat message history to maintain conversation context.
   *
   * Parameters
   * ----------
   * controller
   * language
   * request : Request
   *     The Starlette request.
   * document_query : str
   *     Plaintext question asked by the user.
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id,
   *     and other information.
   *
   * Returns
   * -------
   * Answer
   *     An answer containing the actual text answer and additional metadata for
   *     the sources used.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `queryDocumentsDocumentsQueryPost()` instead.
   *
   * This method doesn't expect any request body.
   */
  queryDocumentsDocumentsQueryPost$Response(params: QueryDocumentsDocumentsQueryPost$Params, context?: HttpContext): Observable<StrictHttpResponse<Answer>> {
    return queryDocumentsDocumentsQueryPost(this.http, this.rootUrl, params, context);
  }

  /**
   * Query Documents.
   *
   * Answer the user's text query to a document.
   *
   * This endpoint processes the user's question and generates an answer using a
   * ChatGPT-like language model and a vector store. It interacts with a Redis-based
   * chat message history to maintain conversation context.
   *
   * Parameters
   * ----------
   * controller
   * language
   * request : Request
   *     The Starlette request.
   * document_query : str
   *     Plaintext question asked by the user.
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id,
   *     and other information.
   *
   * Returns
   * -------
   * Answer
   *     An answer containing the actual text answer and additional metadata for
   *     the sources used.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `queryDocumentsDocumentsQueryPost$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  queryDocumentsDocumentsQueryPost(params: QueryDocumentsDocumentsQueryPost$Params, context?: HttpContext): Observable<Answer> {
    return this.queryDocumentsDocumentsQueryPost$Response(params, context).pipe(
      map((r: StrictHttpResponse<Answer>): Answer => r.body)
    );
  }

  /** Path part for operation `queryBusinessCaseV2BusinessCasesQueryPost()` */
  static readonly QueryBusinessCaseV2BusinessCasesQueryPostPath = '/v2/business-cases/query';

  /**
   * Query Business Case.
   *
   * Answer the user's text query to a business case including the financing structure.
   *
   * Version 2 of the similar endpoint which queried only the data room.
   *
   * This endpoint processes the user's question and generates an answer using a
   * ChatGPT-like language model and a vector store. It interacts with a Redis-based
   * chat message history to maintain conversation context.
   *
   * Parameters
   * ----------
   * request : Request
   *     The Starlette request.
   * document_query : str
   *     Plaintext question asked by the user.
   * language: ModelLanguages
   *     The model language used for the query.
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id,
   *     and other information.
   * context
   *     The context from which the endpoint is called. Used to order the sources.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   *
   * Returns
   * -------
   * Answer
   *     An answer containing the actual text answer and additional metadata for
   *     the sources used.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `queryBusinessCaseV2BusinessCasesQueryPost()` instead.
   *
   * This method doesn't expect any request body.
   */
  queryBusinessCaseV2BusinessCasesQueryPost$Response(params: QueryBusinessCaseV2BusinessCasesQueryPost$Params, context?: HttpContext): Observable<StrictHttpResponse<any>> {
    return queryBusinessCaseV2BusinessCasesQueryPost(this.http, this.rootUrl, params, context);
  }

  /**
   * Query Business Case.
   *
   * Answer the user's text query to a business case including the financing structure.
   *
   * Version 2 of the similar endpoint which queried only the data room.
   *
   * This endpoint processes the user's question and generates an answer using a
   * ChatGPT-like language model and a vector store. It interacts with a Redis-based
   * chat message history to maintain conversation context.
   *
   * Parameters
   * ----------
   * request : Request
   *     The Starlette request.
   * document_query : str
   *     Plaintext question asked by the user.
   * language: ModelLanguages
   *     The model language used for the query.
   * standard_context : StandardEndpointContext
   *     The context of the endpoint, which contains the user id, business case id,
   *     and other information.
   * context
   *     The context from which the endpoint is called. Used to order the sources.
   * controller : Controller
   *     An instance of the Controller class obtained from the dependency injection.
   *
   * Returns
   * -------
   * Answer
   *     An answer containing the actual text answer and additional metadata for
   *     the sources used.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `queryBusinessCaseV2BusinessCasesQueryPost$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  queryBusinessCaseV2BusinessCasesQueryPost(params: QueryBusinessCaseV2BusinessCasesQueryPost$Params, context?: HttpContext): Observable<any> {
    return this.queryBusinessCaseV2BusinessCasesQueryPost$Response(params, context).pipe(
      map((r: StrictHttpResponse<any>): any => r.body)
    );
  }

}
