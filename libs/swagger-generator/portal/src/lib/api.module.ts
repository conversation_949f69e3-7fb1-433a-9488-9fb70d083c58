/* tslint:disable */
/* eslint-disable */
import { <PERSON><PERSON><PERSON><PERSON>, ModuleWithProviders, <PERSON><PERSON><PERSON>elf, Optional } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiConfiguration, ApiConfigurationParams } from './api-configuration';

import { UserCaseControllerService } from './services/user-case-controller.service';
import { UserCaseInternalControllerService } from './services/user-case-internal-controller.service';
import { PortalBusinessCaseControllerService } from './services/portal-business-case-controller.service';
import { CaseFieldInputRequestInternalControllerService } from './services/case-field-input-request-internal-controller.service';
import { CaseFieldAccessInternalControllerService } from './services/case-field-access-internal-controller.service';
import { CaseFieldInputRequestControllerService } from './services/case-field-input-request-controller.service';
import { CaseFieldAccessControllerService } from './services/case-field-access-controller.service';
import { SingleClusterDemoControllerService } from './services/single-cluster-demo-controller.service';
import { MigrationForDeletionOfCaseFieldRequestTypeCompanyService } from './services/migration-for-deletion-of-case-field-request-type-company.service';
import { BusinessCaseRemovalInternalControllerService } from './services/business-case-removal-internal-controller.service';

/**
 * Module that provides all services and configuration.
 */
@NgModule({
  imports: [],
  exports: [],
  declarations: [],
  providers: [
    UserCaseControllerService,
    UserCaseInternalControllerService,
    PortalBusinessCaseControllerService,
    CaseFieldInputRequestInternalControllerService,
    CaseFieldAccessInternalControllerService,
    CaseFieldInputRequestControllerService,
    CaseFieldAccessControllerService,
    SingleClusterDemoControllerService,
    MigrationForDeletionOfCaseFieldRequestTypeCompanyService,
    BusinessCaseRemovalInternalControllerService,
    ApiConfiguration
  ],
})
export class ApiModule {
  static forRoot(params: ApiConfigurationParams): ModuleWithProviders<ApiModule> {
    return {
      ngModule: ApiModule,
      providers: [
        {
          provide: ApiConfiguration,
          useValue: params
        }
      ]
    }
  }

  constructor( 
    @Optional() @SkipSelf() parentModule: ApiModule,
    @Optional() http: HttpClient
  ) {
    if (parentModule) {
      throw new Error('ApiModule is already loaded. Import in your base AppModule only.');
    }
    if (!http) {
      throw new Error('You need to import the HttpClientModule in your AppModule! \n' +
      'See also https://github.com/angular/angular/issues/20575');
    }
  }
}
