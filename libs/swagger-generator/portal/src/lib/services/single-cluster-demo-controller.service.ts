/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { getUserCaseAllByCaseIdDemo } from '../fn/single-cluster-demo-controller/get-user-case-all-by-case-id-demo';
import { GetUserCaseAllByCaseIdDemo$Params } from '../fn/single-cluster-demo-controller/get-user-case-all-by-case-id-demo';
import { UserCase } from '../models/user-case';

@Injectable({ providedIn: 'root' })
export class SingleClusterDemoControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getUserCaseAllByCaseIdDemo()` */
  static readonly GetUserCaseAllByCaseIdDemoPath = '/api/demo/all-by-case-id';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getUserCaseAllByCaseIdDemo()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserCaseAllByCaseIdDemo$Response(params: GetUserCaseAllByCaseIdDemo$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<UserCase>>> {
    return getUserCaseAllByCaseIdDemo(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getUserCaseAllByCaseIdDemo$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserCaseAllByCaseIdDemo(params: GetUserCaseAllByCaseIdDemo$Params, context?: HttpContext): Observable<Array<UserCase>> {
    return this.getUserCaseAllByCaseIdDemo$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<UserCase>>): Array<UserCase> => r.body)
    );
  }

}
