/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { CaseFieldAccess } from '../models/case-field-access';
import { getAllByCaseId2 } from '../fn/case-field-access-controller/get-all-by-case-id-2';
import { GetAllByCaseId2$Params } from '../fn/case-field-access-controller/get-all-by-case-id-2';
import { getAllByCaseIdAndRequestType1 } from '../fn/case-field-access-controller/get-all-by-case-id-and-request-type-1';
import { GetAllByCaseIdAndRequestType1$Params } from '../fn/case-field-access-controller/get-all-by-case-id-and-request-type-1';

@Injectable({ providedIn: 'root' })
export class CaseFieldAccessControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getAllByCaseId2()` */
  static readonly GetAllByCaseId2Path = '/case-field-access/all-by-case-id';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllByCaseId2()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllByCaseId2$Response(params: GetAllByCaseId2$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<CaseFieldAccess>>> {
    return getAllByCaseId2(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllByCaseId2$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllByCaseId2(params: GetAllByCaseId2$Params, context?: HttpContext): Observable<Array<CaseFieldAccess>> {
    return this.getAllByCaseId2$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<CaseFieldAccess>>): Array<CaseFieldAccess> => r.body)
    );
  }

  /** Path part for operation `getAllByCaseIdAndRequestType1()` */
  static readonly GetAllByCaseIdAndRequestType1Path = '/case-field-access/all-by-case-id-and-request-type';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllByCaseIdAndRequestType1()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllByCaseIdAndRequestType1$Response(params: GetAllByCaseIdAndRequestType1$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<CaseFieldAccess>>> {
    return getAllByCaseIdAndRequestType1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllByCaseIdAndRequestType1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllByCaseIdAndRequestType1(params: GetAllByCaseIdAndRequestType1$Params, context?: HttpContext): Observable<Array<CaseFieldAccess>> {
    return this.getAllByCaseIdAndRequestType1$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<CaseFieldAccess>>): Array<CaseFieldAccess> => r.body)
    );
  }

}
