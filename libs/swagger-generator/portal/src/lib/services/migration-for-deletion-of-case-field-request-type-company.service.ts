/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { deleteCaseFieldAccessTypeCompany } from '../fn/migration-for-deletion-of-case-field-request-type-company/delete-case-field-access-type-company';
import { DeleteCaseFieldAccessTypeCompany$Params } from '../fn/migration-for-deletion-of-case-field-request-type-company/delete-case-field-access-type-company';
import { deleteCaseFieldInputRequestTypeCompany } from '../fn/migration-for-deletion-of-case-field-request-type-company/delete-case-field-input-request-type-company';
import { DeleteCaseFieldInputRequestTypeCompany$Params } from '../fn/migration-for-deletion-of-case-field-request-type-company/delete-case-field-input-request-type-company';

@Injectable({ providedIn: 'root' })
export class MigrationForDeletionOfCaseFieldRequestTypeCompanyService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `deleteCaseFieldInputRequestTypeCompany()` */
  static readonly DeleteCaseFieldInputRequestTypeCompanyPath = '/migration/delete-caseFieldInputRequest';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteCaseFieldInputRequestTypeCompany()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteCaseFieldInputRequestTypeCompany$Response(params: DeleteCaseFieldInputRequestTypeCompany$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return deleteCaseFieldInputRequestTypeCompany(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteCaseFieldInputRequestTypeCompany$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteCaseFieldInputRequestTypeCompany(params: DeleteCaseFieldInputRequestTypeCompany$Params, context?: HttpContext): Observable<Array<string>> {
    return this.deleteCaseFieldInputRequestTypeCompany$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

  /** Path part for operation `deleteCaseFieldAccessTypeCompany()` */
  static readonly DeleteCaseFieldAccessTypeCompanyPath = '/migration/delete-caseFieldAccess';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteCaseFieldAccessTypeCompany()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteCaseFieldAccessTypeCompany$Response(params: DeleteCaseFieldAccessTypeCompany$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return deleteCaseFieldAccessTypeCompany(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteCaseFieldAccessTypeCompany$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteCaseFieldAccessTypeCompany(params: DeleteCaseFieldAccessTypeCompany$Params, context?: HttpContext): Observable<Array<string>> {
    return this.deleteCaseFieldAccessTypeCompany$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

}
