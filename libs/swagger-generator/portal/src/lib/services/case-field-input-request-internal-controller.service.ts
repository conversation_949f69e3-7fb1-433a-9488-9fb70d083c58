/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { CaseFieldInputRequest } from '../models/case-field-input-request';
import { getFieldInputRequestByCaseAndInformationId } from '../fn/case-field-input-request-internal-controller/get-field-input-request-by-case-and-information-id';
import { GetFieldInputRequestByCaseAndInformationId$Params } from '../fn/case-field-input-request-internal-controller/get-field-input-request-by-case-and-information-id';

@Injectable({ providedIn: 'root' })
export class CaseFieldInputRequestInternalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getFieldInputRequestByCaseAndInformationId()` */
  static readonly GetFieldInputRequestByCaseAndInformationIdPath = '/internal/case-field-input-request/get-by-case-and-info-id';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFieldInputRequestByCaseAndInformationId()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFieldInputRequestByCaseAndInformationId$Response(params: GetFieldInputRequestByCaseAndInformationId$Params, context?: HttpContext): Observable<StrictHttpResponse<CaseFieldInputRequest>> {
    return getFieldInputRequestByCaseAndInformationId(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFieldInputRequestByCaseAndInformationId$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFieldInputRequestByCaseAndInformationId(params: GetFieldInputRequestByCaseAndInformationId$Params, context?: HttpContext): Observable<CaseFieldInputRequest> {
    return this.getFieldInputRequestByCaseAndInformationId$Response(params, context).pipe(
      map((r: StrictHttpResponse<CaseFieldInputRequest>): CaseFieldInputRequest => r.body)
    );
  }

}
