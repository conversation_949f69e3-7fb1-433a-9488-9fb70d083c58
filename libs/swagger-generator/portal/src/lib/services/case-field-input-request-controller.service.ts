/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { CaseFieldInputRequest } from '../models/case-field-input-request';
import { getAllByCaseId1 } from '../fn/case-field-input-request-controller/get-all-by-case-id-1';
import { GetAllByCaseId1$Params } from '../fn/case-field-input-request-controller/get-all-by-case-id-1';
import { getAllByCaseIdAndRequestType } from '../fn/case-field-input-request-controller/get-all-by-case-id-and-request-type';
import { GetAllByCaseIdAndRequestType$Params } from '../fn/case-field-input-request-controller/get-all-by-case-id-and-request-type';

@Injectable({ providedIn: 'root' })
export class CaseFieldInputRequestControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getAllByCaseId1()` */
  static readonly GetAllByCaseId1Path = '/case-field-input-request/all-by-case-id';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllByCaseId1()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllByCaseId1$Response(params: GetAllByCaseId1$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<CaseFieldInputRequest>>> {
    return getAllByCaseId1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllByCaseId1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllByCaseId1(params: GetAllByCaseId1$Params, context?: HttpContext): Observable<Array<CaseFieldInputRequest>> {
    return this.getAllByCaseId1$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<CaseFieldInputRequest>>): Array<CaseFieldInputRequest> => r.body)
    );
  }

  /** Path part for operation `getAllByCaseIdAndRequestType()` */
  static readonly GetAllByCaseIdAndRequestTypePath = '/case-field-input-request/all-by-case-id-and-request-type';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllByCaseIdAndRequestType()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllByCaseIdAndRequestType$Response(params: GetAllByCaseIdAndRequestType$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<CaseFieldInputRequest>>> {
    return getAllByCaseIdAndRequestType(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllByCaseIdAndRequestType$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllByCaseIdAndRequestType(params: GetAllByCaseIdAndRequestType$Params, context?: HttpContext): Observable<Array<CaseFieldInputRequest>> {
    return this.getAllByCaseIdAndRequestType$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<CaseFieldInputRequest>>): Array<CaseFieldInputRequest> => r.body)
    );
  }

}
