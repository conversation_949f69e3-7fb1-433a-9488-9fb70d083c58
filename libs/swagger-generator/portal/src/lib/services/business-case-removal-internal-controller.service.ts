/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { deleteAllByBusinessCaseIds } from '../fn/business-case-removal-internal-controller/delete-all-by-business-case-ids';
import { DeleteAllByBusinessCaseIds$Params } from '../fn/business-case-removal-internal-controller/delete-all-by-business-case-ids';

@Injectable({ providedIn: 'root' })
export class BusinessCaseRemovalInternalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `deleteAllByBusinessCaseIds()` */
  static readonly DeleteAllByBusinessCaseIdsPath = '/internal/business-case-removal/by-business-case-ids';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteAllByBusinessCaseIds()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  deleteAllByBusinessCaseIds$Response(params: DeleteAllByBusinessCaseIds$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return deleteAllByBusinessCaseIds(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteAllByBusinessCaseIds$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  deleteAllByBusinessCaseIds(params: DeleteAllByBusinessCaseIds$Params, context?: HttpContext): Observable<Array<string>> {
    return this.deleteAllByBusinessCaseIds$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

}
