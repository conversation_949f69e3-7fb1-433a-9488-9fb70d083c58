/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { BusinessCase } from '../models/business-case';
import { FinStructureField } from '../models/fin-structure-field';
import { getAllInformation } from '../fn/portal-business-case-controller/get-all-information';
import { GetAllInformation$Params } from '../fn/portal-business-case-controller/get-all-information';
import { getBusinessCaseById } from '../fn/portal-business-case-controller/get-business-case-by-id';
import { GetBusinessCaseById$Params } from '../fn/portal-business-case-controller/get-business-case-by-id';
import { getFinancingStructureCommonFieldsByBusinessCaseId } from '../fn/portal-business-case-controller/get-financing-structure-common-fields-by-business-case-id';
import { GetFinancingStructureCommonFieldsByBusinessCaseId$Params } from '../fn/portal-business-case-controller/get-financing-structure-common-fields-by-business-case-id';
import { getInformationIdsByFields } from '../fn/portal-business-case-controller/get-information-ids-by-fields';
import { GetInformationIdsByFields$Params } from '../fn/portal-business-case-controller/get-information-ids-by-fields';
import { Information } from '../models/information';

@Injectable({ providedIn: 'root' })
export class PortalBusinessCaseControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getBusinessCaseById()` */
  static readonly GetBusinessCaseByIdPath = '/business-case/{businessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getBusinessCaseById()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  getBusinessCaseById$Response(params: GetBusinessCaseById$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
    return getBusinessCaseById(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getBusinessCaseById$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  getBusinessCaseById(params: GetBusinessCaseById$Params, context?: HttpContext): Observable<BusinessCase> {
    return this.getBusinessCaseById$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body)
    );
  }

  /** Path part for operation `getAllInformation()` */
  static readonly GetAllInformationPath = '/business-case/information';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllInformation()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  getAllInformation$Response(params: GetAllInformation$Params, context?: HttpContext): Observable<StrictHttpResponse<{
[key: string]: Information;
}>> {
    return getAllInformation(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllInformation$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  getAllInformation(params: GetAllInformation$Params, context?: HttpContext): Observable<{
[key: string]: Information;
}> {
    return this.getAllInformation$Response(params, context).pipe(
      map((r: StrictHttpResponse<{
[key: string]: Information;
}>): {
[key: string]: Information;
} => r.body)
    );
  }

  /** Path part for operation `getInformationIdsByFields()` */
  static readonly GetInformationIdsByFieldsPath = '/business-case/information/by-fields';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getInformationIdsByFields()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  getInformationIdsByFields$Response(params: GetInformationIdsByFields$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return getInformationIdsByFields(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getInformationIdsByFields$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  getInformationIdsByFields(params: GetInformationIdsByFields$Params, context?: HttpContext): Observable<Array<string>> {
    return this.getInformationIdsByFields$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

  /** Path part for operation `getFinancingStructureCommonFieldsByBusinessCaseId()` */
  static readonly GetFinancingStructureCommonFieldsByBusinessCaseIdPath = '/business-case/fin-structure/common-field';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFinancingStructureCommonFieldsByBusinessCaseId()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinancingStructureCommonFieldsByBusinessCaseId$Response(params: GetFinancingStructureCommonFieldsByBusinessCaseId$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<FinStructureField>>> {
    return getFinancingStructureCommonFieldsByBusinessCaseId(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFinancingStructureCommonFieldsByBusinessCaseId$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinancingStructureCommonFieldsByBusinessCaseId(params: GetFinancingStructureCommonFieldsByBusinessCaseId$Params, context?: HttpContext): Observable<Array<FinStructureField>> {
    return this.getFinancingStructureCommonFieldsByBusinessCaseId$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<FinStructureField>>): Array<FinStructureField> => r.body)
    );
  }

}
