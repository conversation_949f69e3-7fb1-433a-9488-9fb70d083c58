/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { CaseFieldAccess } from '../models/case-field-access';
import { getCaseFieldAccessAllByCaseIdInternal } from '../fn/case-field-access-internal-controller/get-case-field-access-all-by-case-id-internal';
import { GetCaseFieldAccessAllByCaseIdInternal$Params } from '../fn/case-field-access-internal-controller/get-case-field-access-all-by-case-id-internal';

@Injectable({ providedIn: 'root' })
export class CaseFieldAccessInternalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getCaseFieldAccessAllByCaseIdInternal()` */
  static readonly GetCaseFieldAccessAllByCaseIdInternalPath = '/internal/case-field-access/all-by-case-id';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getCaseFieldAccessAllByCaseIdInternal()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCaseFieldAccessAllByCaseIdInternal$Response(params: GetCaseFieldAccessAllByCaseIdInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<CaseFieldAccess>>> {
    return getCaseFieldAccessAllByCaseIdInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getCaseFieldAccessAllByCaseIdInternal$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCaseFieldAccessAllByCaseIdInternal(params: GetCaseFieldAccessAllByCaseIdInternal$Params, context?: HttpContext): Observable<Array<CaseFieldAccess>> {
    return this.getCaseFieldAccessAllByCaseIdInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<CaseFieldAccess>>): Array<CaseFieldAccess> => r.body)
    );
  }

}
