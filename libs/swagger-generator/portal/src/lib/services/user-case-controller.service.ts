/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { createAssignment } from '../fn/user-case-controller/create-assignment';
import { CreateAssignment$Params } from '../fn/user-case-controller/create-assignment';
import { getAllByCaseId } from '../fn/user-case-controller/get-all-by-case-id';
import { GetAllByCaseId$Params } from '../fn/user-case-controller/get-all-by-case-id';
import { getAllByUserId } from '../fn/user-case-controller/get-all-by-user-id';
import { GetAllByUserId$Params } from '../fn/user-case-controller/get-all-by-user-id';
import { removeAssignment } from '../fn/user-case-controller/remove-assignment';
import { RemoveAssignment$Params } from '../fn/user-case-controller/remove-assignment';
import { removeAssignmentsByUserId } from '../fn/user-case-controller/remove-assignments-by-user-id';
import { RemoveAssignmentsByUserId$Params } from '../fn/user-case-controller/remove-assignments-by-user-id';
import { UserCase } from '../models/user-case';

@Injectable({ providedIn: 'root' })
export class UserCaseControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `createAssignment()` */
  static readonly CreateAssignmentPath = '/user-case';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `createAssignment()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  createAssignment$Response(params: CreateAssignment$Params, context?: HttpContext): Observable<StrictHttpResponse<UserCase>> {
    return createAssignment(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `createAssignment$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  createAssignment(params: CreateAssignment$Params, context?: HttpContext): Observable<UserCase> {
    return this.createAssignment$Response(params, context).pipe(
      map((r: StrictHttpResponse<UserCase>): UserCase => r.body)
    );
  }

  /** Path part for operation `removeAssignment()` */
  static readonly RemoveAssignmentPath = '/user-case';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `removeAssignment()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  removeAssignment$Response(params: RemoveAssignment$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return removeAssignment(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `removeAssignment$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  removeAssignment(params: RemoveAssignment$Params, context?: HttpContext): Observable<void> {
    return this.removeAssignment$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getAllByUserId()` */
  static readonly GetAllByUserIdPath = '/user-case/all-by-user-id';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllByUserId()` instead.
   *
   * This method doesn't expect any request body.
   *
   * @deprecated
   */
  getAllByUserId$Response(params: GetAllByUserId$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<UserCase>>> {
    return getAllByUserId(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllByUserId$Response()` instead.
   *
   * This method doesn't expect any request body.
   *
   * @deprecated
   */
  getAllByUserId(params: GetAllByUserId$Params, context?: HttpContext): Observable<Array<UserCase>> {
    return this.getAllByUserId$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<UserCase>>): Array<UserCase> => r.body)
    );
  }

  /** Path part for operation `removeAssignmentsByUserId()` */
  static readonly RemoveAssignmentsByUserIdPath = '/user-case/all-by-user-id';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `removeAssignmentsByUserId()` instead.
   *
   * This method doesn't expect any request body.
   */
  removeAssignmentsByUserId$Response(params: RemoveAssignmentsByUserId$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return removeAssignmentsByUserId(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `removeAssignmentsByUserId$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  removeAssignmentsByUserId(params: RemoveAssignmentsByUserId$Params, context?: HttpContext): Observable<void> {
    return this.removeAssignmentsByUserId$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getAllByCaseId()` */
  static readonly GetAllByCaseIdPath = '/user-case/all-by-case-id';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllByCaseId()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllByCaseId$Response(params: GetAllByCaseId$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<UserCase>>> {
    return getAllByCaseId(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllByCaseId$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllByCaseId(params: GetAllByCaseId$Params, context?: HttpContext): Observable<Array<UserCase>> {
    return this.getAllByCaseId$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<UserCase>>): Array<UserCase> => r.body)
    );
  }

}
