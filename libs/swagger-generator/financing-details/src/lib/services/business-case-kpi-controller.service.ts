/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { BusinessCaseKpi } from '../models/business-case-kpi';
import { enableOrDisableKpiForCustomer1 } from '../fn/business-case-kpi-controller/enable-or-disable-kpi-for-customer-1';
import { EnableOrDisableKpiForCustomer1$Params } from '../fn/business-case-kpi-controller/enable-or-disable-kpi-for-customer-1';
import { FinStructureFieldAdditionalInfo } from '../models/fin-structure-field-additional-info';
import { getAllKpisForBusinessCase1 } from '../fn/business-case-kpi-controller/get-all-kpis-for-business-case-1';
import { GetAllKpisForBusinessCase1$Params } from '../fn/business-case-kpi-controller/get-all-kpis-for-business-case-1';
import { getBusinessCaseKpiClusters } from '../fn/business-case-kpi-controller/get-business-case-kpi-clusters';
import { GetBusinessCaseKpiClusters$Params } from '../fn/business-case-kpi-controller/get-business-case-kpi-clusters';
import { getKpiAdditionalInformation } from '../fn/business-case-kpi-controller/get-kpi-additional-information';
import { GetKpiAdditionalInformation$Params } from '../fn/business-case-kpi-controller/get-kpi-additional-information';
import { getKpiForCustomer2 } from '../fn/business-case-kpi-controller/get-kpi-for-customer-2';
import { GetKpiForCustomer2$Params } from '../fn/business-case-kpi-controller/get-kpi-for-customer-2';
import { KpiClusterContainerBusinessCaseKpi } from '../models/kpi-cluster-container-business-case-kpi';

@Injectable({ providedIn: 'root' })
export class BusinessCaseKpiControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `enableOrDisableKpiForCustomer1()` */
  static readonly EnableOrDisableKpiForCustomer1Path = '/business-case/kpis/{kpiKey}/enable-disable-kpi';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `enableOrDisableKpiForCustomer1()` instead.
   *
   * This method doesn't expect any request body.
   */
  enableOrDisableKpiForCustomer1$Response(params: EnableOrDisableKpiForCustomer1$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return enableOrDisableKpiForCustomer1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `enableOrDisableKpiForCustomer1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  enableOrDisableKpiForCustomer1(params: EnableOrDisableKpiForCustomer1$Params, context?: HttpContext): Observable<void> {
    return this.enableOrDisableKpiForCustomer1$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getAllKpisForBusinessCase1()` */
  static readonly GetAllKpisForBusinessCase1Path = '/business-case/kpis';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllKpisForBusinessCase1()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllKpisForBusinessCase1$Response(params: GetAllKpisForBusinessCase1$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<BusinessCaseKpi>>> {
    return getAllKpisForBusinessCase1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllKpisForBusinessCase1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllKpisForBusinessCase1(params: GetAllKpisForBusinessCase1$Params, context?: HttpContext): Observable<Array<BusinessCaseKpi>> {
    return this.getAllKpisForBusinessCase1$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<BusinessCaseKpi>>): Array<BusinessCaseKpi> => r.body)
    );
  }

  /** Path part for operation `getKpiForCustomer2()` */
  static readonly GetKpiForCustomer2Path = '/business-case/kpis/{kpiKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getKpiForCustomer2()` instead.
   *
   * This method doesn't expect any request body.
   */
  getKpiForCustomer2$Response(params: GetKpiForCustomer2$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCaseKpi>> {
    return getKpiForCustomer2(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getKpiForCustomer2$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getKpiForCustomer2(params: GetKpiForCustomer2$Params, context?: HttpContext): Observable<BusinessCaseKpi> {
    return this.getKpiForCustomer2$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCaseKpi>): BusinessCaseKpi => r.body)
    );
  }

  /** Path part for operation `getKpiAdditionalInformation()` */
  static readonly GetKpiAdditionalInformationPath = '/business-case/kpis/{kpiKey}/additional-information';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getKpiAdditionalInformation()` instead.
   *
   * This method doesn't expect any request body.
   */
  getKpiAdditionalInformation$Response(params: GetKpiAdditionalInformation$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<FinStructureFieldAdditionalInfo>>> {
    return getKpiAdditionalInformation(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getKpiAdditionalInformation$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getKpiAdditionalInformation(params: GetKpiAdditionalInformation$Params, context?: HttpContext): Observable<Array<FinStructureFieldAdditionalInfo>> {
    return this.getKpiAdditionalInformation$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<FinStructureFieldAdditionalInfo>>): Array<FinStructureFieldAdditionalInfo> => r.body)
    );
  }

  /** Path part for operation `getBusinessCaseKpiClusters()` */
  static readonly GetBusinessCaseKpiClustersPath = '/business-case/kpis/clusters';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getBusinessCaseKpiClusters()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCaseKpiClusters$Response(params: GetBusinessCaseKpiClusters$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<KpiClusterContainerBusinessCaseKpi>>> {
    return getBusinessCaseKpiClusters(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getBusinessCaseKpiClusters$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCaseKpiClusters(params: GetBusinessCaseKpiClusters$Params, context?: HttpContext): Observable<Array<KpiClusterContainerBusinessCaseKpi>> {
    return this.getBusinessCaseKpiClusters$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<KpiClusterContainerBusinessCaseKpi>>): Array<KpiClusterContainerBusinessCaseKpi> => r.body)
    );
  }

}
