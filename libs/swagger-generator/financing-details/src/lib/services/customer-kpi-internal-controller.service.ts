/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { CustomerKpi } from '../models/customer-kpi';
import { getAllKpisForCustomer } from '../fn/customer-kpi-internal-controller/get-all-kpis-for-customer';
import { GetAllKpisForCustomer$Params } from '../fn/customer-kpi-internal-controller/get-all-kpis-for-customer';
import { getKpiForCustomer } from '../fn/customer-kpi-internal-controller/get-kpi-for-customer';
import { GetKpiForCustomer$Params } from '../fn/customer-kpi-internal-controller/get-kpi-for-customer';

@Injectable({ providedIn: 'root' })
export class CustomerKpiInternalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getAllKpisForCustomer()` */
  static readonly GetAllKpisForCustomerPath = '/internal/customers/{customerKey}/kpis';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllKpisForCustomer()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllKpisForCustomer$Response(params: GetAllKpisForCustomer$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<CustomerKpi>>> {
    return getAllKpisForCustomer(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllKpisForCustomer$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllKpisForCustomer(params: GetAllKpisForCustomer$Params, context?: HttpContext): Observable<Array<CustomerKpi>> {
    return this.getAllKpisForCustomer$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<CustomerKpi>>): Array<CustomerKpi> => r.body)
    );
  }

  /** Path part for operation `getKpiForCustomer()` */
  static readonly GetKpiForCustomerPath = '/internal/customers/{customerKey}/kpis/{kpiKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getKpiForCustomer()` instead.
   *
   * This method doesn't expect any request body.
   */
  getKpiForCustomer$Response(params: GetKpiForCustomer$Params, context?: HttpContext): Observable<StrictHttpResponse<CustomerKpi>> {
    return getKpiForCustomer(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getKpiForCustomer$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getKpiForCustomer(params: GetKpiForCustomer$Params, context?: HttpContext): Observable<CustomerKpi> {
    return this.getKpiForCustomer$Response(params, context).pipe(
      map((r: StrictHttpResponse<CustomerKpi>): CustomerKpi => r.body)
    );
  }

}
