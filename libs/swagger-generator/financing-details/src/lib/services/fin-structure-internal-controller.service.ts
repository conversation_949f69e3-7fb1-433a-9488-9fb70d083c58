/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { addValueToFinStructureField } from '../fn/fin-structure-internal-controller/add-value-to-fin-structure-field';
import { AddValueToFinStructureField$Params } from '../fn/fin-structure-internal-controller/add-value-to-fin-structure-field';
import { deleteAllFinStructuresByBusinessCaseId } from '../fn/fin-structure-internal-controller/delete-all-fin-structures-by-business-case-id';
import { DeleteAllFinStructuresByBusinessCaseId$Params } from '../fn/fin-structure-internal-controller/delete-all-fin-structures-by-business-case-id';
import { deleteAllSharedFinStructuresByBusinessCaseId } from '../fn/fin-structure-internal-controller/delete-all-shared-fin-structures-by-business-case-id';
import { DeleteAllSharedFinStructuresByBusinessCaseId$Params } from '../fn/fin-structure-internal-controller/delete-all-shared-fin-structures-by-business-case-id';
import { FinancingStructure } from '../models/financing-structure';
import { findAllFinStructuresByBusinessCaseId } from '../fn/fin-structure-internal-controller/find-all-fin-structures-by-business-case-id';
import { FindAllFinStructuresByBusinessCaseId$Params } from '../fn/fin-structure-internal-controller/find-all-fin-structures-by-business-case-id';
import { findAllSharedFinStructuresByBusinessCaseId } from '../fn/fin-structure-internal-controller/find-all-shared-fin-structures-by-business-case-id';
import { FindAllSharedFinStructuresByBusinessCaseId$Params } from '../fn/fin-structure-internal-controller/find-all-shared-fin-structures-by-business-case-id';
import { FinStructureField } from '../models/fin-structure-field';
import { FinStructureGroup } from '../models/fin-structure-group';
import { FinStructureSharingEntity } from '../models/fin-structure-sharing-entity';
import { generateFinStructureForCustomer } from '../fn/fin-structure-internal-controller/generate-fin-structure-for-customer';
import { GenerateFinStructureForCustomer$Params } from '../fn/fin-structure-internal-controller/generate-fin-structure-for-customer';
import { getFinancingStructureByBusinessCaseIdAndCustomerKey } from '../fn/fin-structure-internal-controller/get-financing-structure-by-business-case-id-and-customer-key';
import { GetFinancingStructureByBusinessCaseIdAndCustomerKey$Params } from '../fn/fin-structure-internal-controller/get-financing-structure-by-business-case-id-and-customer-key';
import { getFinancingStructureById } from '../fn/fin-structure-internal-controller/get-financing-structure-by-id';
import { GetFinancingStructureById$Params } from '../fn/fin-structure-internal-controller/get-financing-structure-by-id';
import { getFinancingStructureDynamicGroups } from '../fn/fin-structure-internal-controller/get-financing-structure-dynamic-groups';
import { GetFinancingStructureDynamicGroups$Params } from '../fn/fin-structure-internal-controller/get-financing-structure-dynamic-groups';
import { getFinancingStructures } from '../fn/fin-structure-internal-controller/get-financing-structures';
import { GetFinancingStructures$Params } from '../fn/fin-structure-internal-controller/get-financing-structures';
import { getFinStructureStaticFieldByBusinessCaseIdAndCustomerKeyAndFieldKey } from '../fn/fin-structure-internal-controller/get-fin-structure-static-field-by-business-case-id-and-customer-key-and-field-key';
import { GetFinStructureStaticFieldByBusinessCaseIdAndCustomerKeyAndFieldKey$Params } from '../fn/fin-structure-internal-controller/get-fin-structure-static-field-by-business-case-id-and-customer-key-and-field-key';
import { getLeadFinancingStructureCommonFields } from '../fn/fin-structure-internal-controller/get-lead-financing-structure-common-fields';
import { GetLeadFinancingStructureCommonFields$Params } from '../fn/fin-structure-internal-controller/get-lead-financing-structure-common-fields';
import { initializeRequiredFields } from '../fn/fin-structure-internal-controller/initialize-required-fields';
import { InitializeRequiredFields$Params } from '../fn/fin-structure-internal-controller/initialize-required-fields';
import { PageFinancingStructure } from '../models/page-financing-structure';
import { persistAllFinancingStructures } from '../fn/fin-structure-internal-controller/persist-all-financing-structures';
import { PersistAllFinancingStructures$Params } from '../fn/fin-structure-internal-controller/persist-all-financing-structures';
import { persistAllSharedFinStructures } from '../fn/fin-structure-internal-controller/persist-all-shared-fin-structures';
import { PersistAllSharedFinStructures$Params } from '../fn/fin-structure-internal-controller/persist-all-shared-fin-structures';
import { sendCommonFieldsUpdateNotification } from '../fn/fin-structure-internal-controller/send-common-fields-update-notification';
import { SendCommonFieldsUpdateNotification$Params } from '../fn/fin-structure-internal-controller/send-common-fields-update-notification';
import { sendCreateFinancingStructuresNotification } from '../fn/fin-structure-internal-controller/send-create-financing-structures-notification';
import { SendCreateFinancingStructuresNotification$Params } from '../fn/fin-structure-internal-controller/send-create-financing-structures-notification';
import { sendUpdateFinancingStructureNotification } from '../fn/fin-structure-internal-controller/send-update-financing-structure-notification';
import { SendUpdateFinancingStructureNotification$Params } from '../fn/fin-structure-internal-controller/send-update-financing-structure-notification';
import { transferFieldsDataBetweenFinStructures } from '../fn/fin-structure-internal-controller/transfer-fields-data-between-fin-structures';
import { TransferFieldsDataBetweenFinStructures$Params } from '../fn/fin-structure-internal-controller/transfer-fields-data-between-fin-structures';
import { transferFinancingStructureData } from '../fn/fin-structure-internal-controller/transfer-financing-structure-data';
import { TransferFinancingStructureData$Params } from '../fn/fin-structure-internal-controller/transfer-financing-structure-data';

@Injectable({ providedIn: 'root' })
export class FinStructureInternalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `sendUpdateFinancingStructureNotification()` */
  static readonly SendUpdateFinancingStructureNotificationPath = '/internal/fin-structure/{financingStructureId}/send-fin-structure-update-notification';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `sendUpdateFinancingStructureNotification()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  sendUpdateFinancingStructureNotification$Response(params: SendUpdateFinancingStructureNotification$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return sendUpdateFinancingStructureNotification(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `sendUpdateFinancingStructureNotification$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  sendUpdateFinancingStructureNotification(params: SendUpdateFinancingStructureNotification$Params, context?: HttpContext): Observable<void> {
    return this.sendUpdateFinancingStructureNotification$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `initializeRequiredFields()` */
  static readonly InitializeRequiredFieldsPath = '/internal/fin-structure/{financingStructureId}/initialize-required-fields';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `initializeRequiredFields()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  initializeRequiredFields$Response(params: InitializeRequiredFields$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return initializeRequiredFields(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `initializeRequiredFields$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  initializeRequiredFields(params: InitializeRequiredFields$Params, context?: HttpContext): Observable<void> {
    return this.initializeRequiredFields$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `transferFinancingStructureData()` */
  static readonly TransferFinancingStructureDataPath = '/internal/fin-structure/{businessCaseId}/transfer-fin-structure-data';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `transferFinancingStructureData()` instead.
   *
   * This method doesn't expect any request body.
   */
  transferFinancingStructureData$Response(params: TransferFinancingStructureData$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return transferFinancingStructureData(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `transferFinancingStructureData$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  transferFinancingStructureData(params: TransferFinancingStructureData$Params, context?: HttpContext): Observable<void> {
    return this.transferFinancingStructureData$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `generateFinStructureForCustomer()` */
  static readonly GenerateFinStructureForCustomerPath = '/internal/fin-structure/{businessCaseId}/generate-fin-structure-copy/{customerKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `generateFinStructureForCustomer()` instead.
   *
   * This method doesn't expect any request body.
   */
  generateFinStructureForCustomer$Response(params: GenerateFinStructureForCustomer$Params, context?: HttpContext): Observable<StrictHttpResponse<FinancingStructure>> {
    return generateFinStructureForCustomer(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `generateFinStructureForCustomer$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  generateFinStructureForCustomer(params: GenerateFinStructureForCustomer$Params, context?: HttpContext): Observable<FinancingStructure> {
    return this.generateFinStructureForCustomer$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinancingStructure>): FinancingStructure => r.body)
    );
  }

  /** Path part for operation `sendCommonFieldsUpdateNotification()` */
  static readonly SendCommonFieldsUpdateNotificationPath = '/internal/fin-structure/{businessCaseId}/common-fields-update-notification';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `sendCommonFieldsUpdateNotification()` instead.
   *
   * This method doesn't expect any request body.
   */
  sendCommonFieldsUpdateNotification$Response(params: SendCommonFieldsUpdateNotification$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return sendCommonFieldsUpdateNotification(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `sendCommonFieldsUpdateNotification$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  sendCommonFieldsUpdateNotification(params: SendCommonFieldsUpdateNotification$Params, context?: HttpContext): Observable<void> {
    return this.sendCommonFieldsUpdateNotification$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `transferFieldsDataBetweenFinStructures()` */
  static readonly TransferFieldsDataBetweenFinStructuresPath = '/internal/fin-structure/transfer-fields-data';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `transferFieldsDataBetweenFinStructures()` instead.
   *
   * This method doesn't expect any request body.
   */
  transferFieldsDataBetweenFinStructures$Response(params: TransferFieldsDataBetweenFinStructures$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return transferFieldsDataBetweenFinStructures(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `transferFieldsDataBetweenFinStructures$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  transferFieldsDataBetweenFinStructures(params: TransferFieldsDataBetweenFinStructures$Params, context?: HttpContext): Observable<void> {
    return this.transferFieldsDataBetweenFinStructures$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `sendCreateFinancingStructuresNotification()` */
  static readonly SendCreateFinancingStructuresNotificationPath = '/internal/fin-structure/send-fin-structure-create-notification';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `sendCreateFinancingStructuresNotification()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  sendCreateFinancingStructuresNotification$Response(params: SendCreateFinancingStructuresNotification$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return sendCreateFinancingStructuresNotification(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `sendCreateFinancingStructuresNotification$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  sendCreateFinancingStructuresNotification(params: SendCreateFinancingStructuresNotification$Params, context?: HttpContext): Observable<void> {
    return this.sendCreateFinancingStructuresNotification$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `persistAllSharedFinStructures()` */
  static readonly PersistAllSharedFinStructuresPath = '/internal/fin-structure/persist-shared-fin-structures';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `persistAllSharedFinStructures()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  persistAllSharedFinStructures$Response(params: PersistAllSharedFinStructures$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return persistAllSharedFinStructures(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `persistAllSharedFinStructures$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  persistAllSharedFinStructures(params: PersistAllSharedFinStructures$Params, context?: HttpContext): Observable<void> {
    return this.persistAllSharedFinStructures$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `persistAllFinancingStructures()` */
  static readonly PersistAllFinancingStructuresPath = '/internal/fin-structure/persist-fin-structures';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `persistAllFinancingStructures()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  persistAllFinancingStructures$Response(params: PersistAllFinancingStructures$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return persistAllFinancingStructures(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `persistAllFinancingStructures$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  persistAllFinancingStructures(params: PersistAllFinancingStructures$Params, context?: HttpContext): Observable<void> {
    return this.persistAllFinancingStructures$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `addValueToFinStructureField()` */
  static readonly AddValueToFinStructureFieldPath = '/internal/fin-structure/business-case/{businessCaseId}/add-value-to-field';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `addValueToFinStructureField()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addValueToFinStructureField$Response(params: AddValueToFinStructureField$Params, context?: HttpContext): Observable<StrictHttpResponse<FinancingStructure>> {
    return addValueToFinStructureField(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `addValueToFinStructureField$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addValueToFinStructureField(params: AddValueToFinStructureField$Params, context?: HttpContext): Observable<FinancingStructure> {
    return this.addValueToFinStructureField$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinancingStructure>): FinancingStructure => r.body)
    );
  }

  /** Path part for operation `getFinancingStructures()` */
  static readonly GetFinancingStructuresPath = '/internal/fin-structure';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFinancingStructures()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinancingStructures$Response(params: GetFinancingStructures$Params, context?: HttpContext): Observable<StrictHttpResponse<PageFinancingStructure>> {
    return getFinancingStructures(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFinancingStructures$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinancingStructures(params: GetFinancingStructures$Params, context?: HttpContext): Observable<PageFinancingStructure> {
    return this.getFinancingStructures$Response(params, context).pipe(
      map((r: StrictHttpResponse<PageFinancingStructure>): PageFinancingStructure => r.body)
    );
  }

  /** Path part for operation `getFinancingStructureById()` */
  static readonly GetFinancingStructureByIdPath = '/internal/fin-structure/{financingStructureId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFinancingStructureById()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinancingStructureById$Response(params: GetFinancingStructureById$Params, context?: HttpContext): Observable<StrictHttpResponse<FinancingStructure>> {
    return getFinancingStructureById(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFinancingStructureById$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinancingStructureById(params: GetFinancingStructureById$Params, context?: HttpContext): Observable<FinancingStructure> {
    return this.getFinancingStructureById$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinancingStructure>): FinancingStructure => r.body)
    );
  }

  /** Path part for operation `getLeadFinancingStructureCommonFields()` */
  static readonly GetLeadFinancingStructureCommonFieldsPath = '/internal/fin-structure/{businessCaseId}/get-lead-financing-structure-common-fields';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getLeadFinancingStructureCommonFields()` instead.
   *
   * This method doesn't expect any request body.
   */
  getLeadFinancingStructureCommonFields$Response(params: GetLeadFinancingStructureCommonFields$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<FinStructureField>>> {
    return getLeadFinancingStructureCommonFields(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getLeadFinancingStructureCommonFields$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getLeadFinancingStructureCommonFields(params: GetLeadFinancingStructureCommonFields$Params, context?: HttpContext): Observable<Array<FinStructureField>> {
    return this.getLeadFinancingStructureCommonFields$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<FinStructureField>>): Array<FinStructureField> => r.body)
    );
  }

  /** Path part for operation `getFinStructureStaticFieldByBusinessCaseIdAndCustomerKeyAndFieldKey()` */
  static readonly GetFinStructureStaticFieldByBusinessCaseIdAndCustomerKeyAndFieldKeyPath = '/internal/fin-structure/{businessCaseId}/field';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFinStructureStaticFieldByBusinessCaseIdAndCustomerKeyAndFieldKey()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinStructureStaticFieldByBusinessCaseIdAndCustomerKeyAndFieldKey$Response(params: GetFinStructureStaticFieldByBusinessCaseIdAndCustomerKeyAndFieldKey$Params, context?: HttpContext): Observable<StrictHttpResponse<FinStructureField>> {
    return getFinStructureStaticFieldByBusinessCaseIdAndCustomerKeyAndFieldKey(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFinStructureStaticFieldByBusinessCaseIdAndCustomerKeyAndFieldKey$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinStructureStaticFieldByBusinessCaseIdAndCustomerKeyAndFieldKey(params: GetFinStructureStaticFieldByBusinessCaseIdAndCustomerKeyAndFieldKey$Params, context?: HttpContext): Observable<FinStructureField> {
    return this.getFinStructureStaticFieldByBusinessCaseIdAndCustomerKeyAndFieldKey$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinStructureField>): FinStructureField => r.body)
    );
  }

  /** Path part for operation `getFinancingStructureDynamicGroups()` */
  static readonly GetFinancingStructureDynamicGroupsPath = '/internal/fin-structure/{businessCaseId}/dynamic-groups';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFinancingStructureDynamicGroups()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinancingStructureDynamicGroups$Response(params: GetFinancingStructureDynamicGroups$Params, context?: HttpContext): Observable<StrictHttpResponse<{
[key: string]: Array<FinStructureGroup>;
}>> {
    return getFinancingStructureDynamicGroups(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFinancingStructureDynamicGroups$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinancingStructureDynamicGroups(params: GetFinancingStructureDynamicGroups$Params, context?: HttpContext): Observable<{
[key: string]: Array<FinStructureGroup>;
}> {
    return this.getFinancingStructureDynamicGroups$Response(params, context).pipe(
      map((r: StrictHttpResponse<{
[key: string]: Array<FinStructureGroup>;
}>): {
[key: string]: Array<FinStructureGroup>;
} => r.body)
    );
  }

  /** Path part for operation `getFinancingStructureByBusinessCaseIdAndCustomerKey()` */
  static readonly GetFinancingStructureByBusinessCaseIdAndCustomerKeyPath = '/internal/fin-structure/{businessCaseId}/customer';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFinancingStructureByBusinessCaseIdAndCustomerKey()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinancingStructureByBusinessCaseIdAndCustomerKey$Response(params: GetFinancingStructureByBusinessCaseIdAndCustomerKey$Params, context?: HttpContext): Observable<StrictHttpResponse<FinancingStructure>> {
    return getFinancingStructureByBusinessCaseIdAndCustomerKey(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFinancingStructureByBusinessCaseIdAndCustomerKey$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinancingStructureByBusinessCaseIdAndCustomerKey(params: GetFinancingStructureByBusinessCaseIdAndCustomerKey$Params, context?: HttpContext): Observable<FinancingStructure> {
    return this.getFinancingStructureByBusinessCaseIdAndCustomerKey$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinancingStructure>): FinancingStructure => r.body)
    );
  }

  /** Path part for operation `findAllSharedFinStructuresByBusinessCaseId()` */
  static readonly FindAllSharedFinStructuresByBusinessCaseIdPath = '/internal/fin-structure/{businessCaseId}/all-shared-fin-structures';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `findAllSharedFinStructuresByBusinessCaseId()` instead.
   *
   * This method doesn't expect any request body.
   */
  findAllSharedFinStructuresByBusinessCaseId$Response(params: FindAllSharedFinStructuresByBusinessCaseId$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<FinStructureSharingEntity>>> {
    return findAllSharedFinStructuresByBusinessCaseId(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `findAllSharedFinStructuresByBusinessCaseId$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  findAllSharedFinStructuresByBusinessCaseId(params: FindAllSharedFinStructuresByBusinessCaseId$Params, context?: HttpContext): Observable<Array<FinStructureSharingEntity>> {
    return this.findAllSharedFinStructuresByBusinessCaseId$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<FinStructureSharingEntity>>): Array<FinStructureSharingEntity> => r.body)
    );
  }

  /** Path part for operation `deleteAllSharedFinStructuresByBusinessCaseId()` */
  static readonly DeleteAllSharedFinStructuresByBusinessCaseIdPath = '/internal/fin-structure/{businessCaseId}/all-shared-fin-structures';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteAllSharedFinStructuresByBusinessCaseId()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteAllSharedFinStructuresByBusinessCaseId$Response(params: DeleteAllSharedFinStructuresByBusinessCaseId$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return deleteAllSharedFinStructuresByBusinessCaseId(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteAllSharedFinStructuresByBusinessCaseId$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteAllSharedFinStructuresByBusinessCaseId(params: DeleteAllSharedFinStructuresByBusinessCaseId$Params, context?: HttpContext): Observable<void> {
    return this.deleteAllSharedFinStructuresByBusinessCaseId$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `findAllFinStructuresByBusinessCaseId()` */
  static readonly FindAllFinStructuresByBusinessCaseIdPath = '/internal/fin-structure/{businessCaseId}/all-fin-structures';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `findAllFinStructuresByBusinessCaseId()` instead.
   *
   * This method doesn't expect any request body.
   */
  findAllFinStructuresByBusinessCaseId$Response(params: FindAllFinStructuresByBusinessCaseId$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<FinancingStructure>>> {
    return findAllFinStructuresByBusinessCaseId(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `findAllFinStructuresByBusinessCaseId$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  findAllFinStructuresByBusinessCaseId(params: FindAllFinStructuresByBusinessCaseId$Params, context?: HttpContext): Observable<Array<FinancingStructure>> {
    return this.findAllFinStructuresByBusinessCaseId$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<FinancingStructure>>): Array<FinancingStructure> => r.body)
    );
  }

  /** Path part for operation `deleteAllFinStructuresByBusinessCaseId()` */
  static readonly DeleteAllFinStructuresByBusinessCaseIdPath = '/internal/fin-structure/{businessCaseId}/all-fin-structures';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteAllFinStructuresByBusinessCaseId()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteAllFinStructuresByBusinessCaseId$Response(params: DeleteAllFinStructuresByBusinessCaseId$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return deleteAllFinStructuresByBusinessCaseId(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteAllFinStructuresByBusinessCaseId$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteAllFinStructuresByBusinessCaseId(params: DeleteAllFinStructuresByBusinessCaseId$Params, context?: HttpContext): Observable<void> {
    return this.deleteAllFinStructuresByBusinessCaseId$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
