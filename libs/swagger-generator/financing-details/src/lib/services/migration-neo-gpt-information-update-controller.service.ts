/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { sendUpdateToNeoGptForFinStructureFields } from '../fn/migration-neo-gpt-information-update-controller/send-update-to-neo-gpt-for-fin-structure-fields';
import { SendUpdateToNeoGptForFinStructureFields$Params } from '../fn/migration-neo-gpt-information-update-controller/send-update-to-neo-gpt-for-fin-structure-fields';

@Injectable({ providedIn: 'root' })
export class MigrationNeoGptInformationUpdateControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `sendUpdateToNeoGptForFinStructureFields()` */
  static readonly SendUpdateToNeoGptForFinStructureFieldsPath = '/migration/financing-structure/neo-gpt-information-update/fin-structures';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `sendUpdateToNeoGptForFinStructureFields()` instead.
   *
   * This method doesn't expect any request body.
   */
  sendUpdateToNeoGptForFinStructureFields$Response(params: SendUpdateToNeoGptForFinStructureFields$Params, context?: HttpContext): Observable<StrictHttpResponse<{
[key: string]: Array<string>;
}>> {
    return sendUpdateToNeoGptForFinStructureFields(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `sendUpdateToNeoGptForFinStructureFields$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  sendUpdateToNeoGptForFinStructureFields(params: SendUpdateToNeoGptForFinStructureFields$Params, context?: HttpContext): Observable<{
[key: string]: Array<string>;
}> {
    return this.sendUpdateToNeoGptForFinStructureFields$Response(params, context).pipe(
      map((r: StrictHttpResponse<{
[key: string]: Array<string>;
}>): {
[key: string]: Array<string>;
} => r.body)
    );
  }

}
