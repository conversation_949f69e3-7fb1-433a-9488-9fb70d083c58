/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { createKpiComment } from '../fn/kpi-comment-controller/create-kpi-comment';
import { CreateKpiComment$Params } from '../fn/kpi-comment-controller/create-kpi-comment';
import { getKpiCommentsForCustomerAndBusinessCase } from '../fn/kpi-comment-controller/get-kpi-comments-for-customer-and-business-case';
import { GetKpiCommentsForCustomerAndBusinessCase$Params } from '../fn/kpi-comment-controller/get-kpi-comments-for-customer-and-business-case';
import { KpiComment } from '../models/kpi-comment';

@Injectable({ providedIn: 'root' })
export class KpiCommentControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getKpiCommentsForCustomerAndBusinessCase()` */
  static readonly GetKpiCommentsForCustomerAndBusinessCasePath = '/business-case/kpi-comments';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getKpiCommentsForCustomerAndBusinessCase()` instead.
   *
   * This method doesn't expect any request body.
   */
  getKpiCommentsForCustomerAndBusinessCase$Response(params: GetKpiCommentsForCustomerAndBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<KpiComment>>> {
    return getKpiCommentsForCustomerAndBusinessCase(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getKpiCommentsForCustomerAndBusinessCase$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getKpiCommentsForCustomerAndBusinessCase(params: GetKpiCommentsForCustomerAndBusinessCase$Params, context?: HttpContext): Observable<Array<KpiComment>> {
    return this.getKpiCommentsForCustomerAndBusinessCase$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<KpiComment>>): Array<KpiComment> => r.body)
    );
  }

  /** Path part for operation `createKpiComment()` */
  static readonly CreateKpiCommentPath = '/business-case/kpi-comments';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `createKpiComment()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  createKpiComment$Response(params: CreateKpiComment$Params, context?: HttpContext): Observable<StrictHttpResponse<KpiComment>> {
    return createKpiComment(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `createKpiComment$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  createKpiComment(params: CreateKpiComment$Params, context?: HttpContext): Observable<KpiComment> {
    return this.createKpiComment$Response(params, context).pipe(
      map((r: StrictHttpResponse<KpiComment>): KpiComment => r.body)
    );
  }

}
