/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { FinStructureSharingEntity } from '../models/fin-structure-sharing-entity';
import { getFinStructureSharingEntitiesFromCustomer } from '../fn/fin-structure-sharing-controller/get-fin-structure-sharing-entities-from-customer';
import { GetFinStructureSharingEntitiesFromCustomer$Params } from '../fn/fin-structure-sharing-controller/get-fin-structure-sharing-entities-from-customer';
import { getFinStructureSharingEntitiesSharedWithMe } from '../fn/fin-structure-sharing-controller/get-fin-structure-sharing-entities-shared-with-me';
import { GetFinStructureSharingEntitiesSharedWithMe$Params } from '../fn/fin-structure-sharing-controller/get-fin-structure-sharing-entities-shared-with-me';
import { getMyFinStructureSharingEntities } from '../fn/fin-structure-sharing-controller/get-my-fin-structure-sharing-entities';
import { GetMyFinStructureSharingEntities$Params } from '../fn/fin-structure-sharing-controller/get-my-fin-structure-sharing-entities';
import { getMyLatestFinStructureSharingEntities } from '../fn/fin-structure-sharing-controller/get-my-latest-fin-structure-sharing-entities';
import { GetMyLatestFinStructureSharingEntities$Params } from '../fn/fin-structure-sharing-controller/get-my-latest-fin-structure-sharing-entities';
import { shareFinStructureWithParticipant } from '../fn/fin-structure-sharing-controller/share-fin-structure-with-participant';
import { ShareFinStructureWithParticipant$Params } from '../fn/fin-structure-sharing-controller/share-fin-structure-with-participant';

@Injectable({ providedIn: 'root' })
export class FinStructureSharingControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `shareFinStructureWithParticipant()` */
  static readonly ShareFinStructureWithParticipantPath = '/fin-structure-sharing/share-groups-with-participant';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `shareFinStructureWithParticipant()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  shareFinStructureWithParticipant$Response(params: ShareFinStructureWithParticipant$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return shareFinStructureWithParticipant(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `shareFinStructureWithParticipant$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  shareFinStructureWithParticipant(params: ShareFinStructureWithParticipant$Params, context?: HttpContext): Observable<void> {
    return this.shareFinStructureWithParticipant$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getFinStructureSharingEntitiesSharedWithMe()` */
  static readonly GetFinStructureSharingEntitiesSharedWithMePath = '/fin-structure-sharing';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFinStructureSharingEntitiesSharedWithMe()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinStructureSharingEntitiesSharedWithMe$Response(params: GetFinStructureSharingEntitiesSharedWithMe$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<FinStructureSharingEntity>>> {
    return getFinStructureSharingEntitiesSharedWithMe(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFinStructureSharingEntitiesSharedWithMe$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinStructureSharingEntitiesSharedWithMe(params: GetFinStructureSharingEntitiesSharedWithMe$Params, context?: HttpContext): Observable<Array<FinStructureSharingEntity>> {
    return this.getFinStructureSharingEntitiesSharedWithMe$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<FinStructureSharingEntity>>): Array<FinStructureSharingEntity> => r.body)
    );
  }

  /** Path part for operation `getFinStructureSharingEntitiesFromCustomer()` */
  static readonly GetFinStructureSharingEntitiesFromCustomerPath = '/fin-structure-sharing/sharing-entities-from-customer';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFinStructureSharingEntitiesFromCustomer()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinStructureSharingEntitiesFromCustomer$Response(params: GetFinStructureSharingEntitiesFromCustomer$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<FinStructureSharingEntity>>> {
    return getFinStructureSharingEntitiesFromCustomer(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFinStructureSharingEntitiesFromCustomer$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinStructureSharingEntitiesFromCustomer(params: GetFinStructureSharingEntitiesFromCustomer$Params, context?: HttpContext): Observable<Array<FinStructureSharingEntity>> {
    return this.getFinStructureSharingEntitiesFromCustomer$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<FinStructureSharingEntity>>): Array<FinStructureSharingEntity> => r.body)
    );
  }

  /** Path part for operation `getMyFinStructureSharingEntities()` */
  static readonly GetMyFinStructureSharingEntitiesPath = '/fin-structure-sharing/my-sharing-entities';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getMyFinStructureSharingEntities()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMyFinStructureSharingEntities$Response(params: GetMyFinStructureSharingEntities$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<FinStructureSharingEntity>>> {
    return getMyFinStructureSharingEntities(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getMyFinStructureSharingEntities$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMyFinStructureSharingEntities(params: GetMyFinStructureSharingEntities$Params, context?: HttpContext): Observable<Array<FinStructureSharingEntity>> {
    return this.getMyFinStructureSharingEntities$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<FinStructureSharingEntity>>): Array<FinStructureSharingEntity> => r.body)
    );
  }

  /** Path part for operation `getMyLatestFinStructureSharingEntities()` */
  static readonly GetMyLatestFinStructureSharingEntitiesPath = '/fin-structure-sharing/my-latest-sharing-entities';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getMyLatestFinStructureSharingEntities()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMyLatestFinStructureSharingEntities$Response(params: GetMyLatestFinStructureSharingEntities$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<FinStructureSharingEntity>>> {
    return getMyLatestFinStructureSharingEntities(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getMyLatestFinStructureSharingEntities$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMyLatestFinStructureSharingEntities(params: GetMyLatestFinStructureSharingEntities$Params, context?: HttpContext): Observable<Array<FinStructureSharingEntity>> {
    return this.getMyLatestFinStructureSharingEntities$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<FinStructureSharingEntity>>): Array<FinStructureSharingEntity> => r.body)
    );
  }

}
