/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { deleteAllBusinessCaseRelatedData } from '../fn/business-cases-physical-removal-controller/delete-all-business-case-related-data';
import { DeleteAllBusinessCaseRelatedData$Params } from '../fn/business-cases-physical-removal-controller/delete-all-business-case-related-data';

@Injectable({ providedIn: 'root' })
export class BusinessCasesPhysicalRemovalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `deleteAllBusinessCaseRelatedData()` */
  static readonly DeleteAllBusinessCaseRelatedDataPath = '/internal/all-business-cases-related-data';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteAllBusinessCaseRelatedData()` instead.
   *
   * This method doesn't expect any request body.
   *
   * @deprecated
   */
  deleteAllBusinessCaseRelatedData$Response(params: DeleteAllBusinessCaseRelatedData$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return deleteAllBusinessCaseRelatedData(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteAllBusinessCaseRelatedData$Response()` instead.
   *
   * This method doesn't expect any request body.
   *
   * @deprecated
   */
  deleteAllBusinessCaseRelatedData(params: DeleteAllBusinessCaseRelatedData$Params, context?: HttpContext): Observable<Array<string>> {
    return this.deleteAllBusinessCaseRelatedData$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

}
