/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { BulkOperationResultString } from '../models/bulk-operation-result-string';
import { migrateFinStructures } from '../fn/migration-financing-structures-controller/migrate-fin-structures';
import { MigrateFinStructures$Params } from '../fn/migration-financing-structures-controller/migrate-fin-structures';
import { reloadCatalogueFromConfiguration } from '../fn/migration-financing-structures-controller/reload-catalogue-from-configuration';
import { ReloadCatalogueFromConfiguration$Params } from '../fn/migration-financing-structures-controller/reload-catalogue-from-configuration';

@Injectable({ providedIn: 'root' })
export class MigrationFinancingStructuresControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `reloadCatalogueFromConfiguration()` */
  static readonly ReloadCatalogueFromConfigurationPath = '/migration/financing-structure/catalogue/reload';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `reloadCatalogueFromConfiguration()` instead.
   *
   * This method doesn't expect any request body.
   */
  reloadCatalogueFromConfiguration$Response(params: ReloadCatalogueFromConfiguration$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return reloadCatalogueFromConfiguration(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `reloadCatalogueFromConfiguration$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  reloadCatalogueFromConfiguration(params: ReloadCatalogueFromConfiguration$Params, context?: HttpContext): Observable<void> {
    return this.reloadCatalogueFromConfiguration$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `migrateFinStructures()` */
  static readonly MigrateFinStructuresPath = '/migration/financing-structure/add-financing-partners';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `migrateFinStructures()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateFinStructures$Response(params: MigrateFinStructures$Params, context?: HttpContext): Observable<StrictHttpResponse<BulkOperationResultString>> {
    return migrateFinStructures(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `migrateFinStructures$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateFinStructures(params: MigrateFinStructures$Params, context?: HttpContext): Observable<BulkOperationResultString> {
    return this.migrateFinStructures$Response(params, context).pipe(
      map((r: StrictHttpResponse<BulkOperationResultString>): BulkOperationResultString => r.body)
    );
  }

}
