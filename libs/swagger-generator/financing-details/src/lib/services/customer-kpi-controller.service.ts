/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { CustomerKpi } from '../models/customer-kpi';
import { enableOrDisableKpiForCustomer } from '../fn/customer-kpi-controller/enable-or-disable-kpi-for-customer';
import { EnableOrDisableKpiForCustomer$Params } from '../fn/customer-kpi-controller/enable-or-disable-kpi-for-customer';
import { getAllKpisForCustomer1 } from '../fn/customer-kpi-controller/get-all-kpis-for-customer-1';
import { GetAllKpisForCustomer1$Params } from '../fn/customer-kpi-controller/get-all-kpis-for-customer-1';
import { getKpiForCustomer1 } from '../fn/customer-kpi-controller/get-kpi-for-customer-1';
import { GetKpiForCustomer1$Params } from '../fn/customer-kpi-controller/get-kpi-for-customer-1';
import { setKpiRange } from '../fn/customer-kpi-controller/set-kpi-range';
import { SetKpiRange$Params } from '../fn/customer-kpi-controller/set-kpi-range';

@Injectable({ providedIn: 'root' })
export class CustomerKpiControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `setKpiRange()` */
  static readonly SetKpiRangePath = '/customers/kpis/{kpiKey}/set-ranges';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `setKpiRange()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  setKpiRange$Response(params: SetKpiRange$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return setKpiRange(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `setKpiRange$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  setKpiRange(params: SetKpiRange$Params, context?: HttpContext): Observable<void> {
    return this.setKpiRange$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `enableOrDisableKpiForCustomer()` */
  static readonly EnableOrDisableKpiForCustomerPath = '/customers/kpis/{kpiKey}/enable-disable-kpi';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `enableOrDisableKpiForCustomer()` instead.
   *
   * This method doesn't expect any request body.
   */
  enableOrDisableKpiForCustomer$Response(params: EnableOrDisableKpiForCustomer$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return enableOrDisableKpiForCustomer(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `enableOrDisableKpiForCustomer$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  enableOrDisableKpiForCustomer(params: EnableOrDisableKpiForCustomer$Params, context?: HttpContext): Observable<void> {
    return this.enableOrDisableKpiForCustomer$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getAllKpisForCustomer1()` */
  static readonly GetAllKpisForCustomer1Path = '/customers/kpis';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllKpisForCustomer1()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllKpisForCustomer1$Response(params?: GetAllKpisForCustomer1$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<CustomerKpi>>> {
    return getAllKpisForCustomer1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllKpisForCustomer1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllKpisForCustomer1(params?: GetAllKpisForCustomer1$Params, context?: HttpContext): Observable<Array<CustomerKpi>> {
    return this.getAllKpisForCustomer1$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<CustomerKpi>>): Array<CustomerKpi> => r.body)
    );
  }

  /** Path part for operation `getKpiForCustomer1()` */
  static readonly GetKpiForCustomer1Path = '/customers/kpis/{kpiKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getKpiForCustomer1()` instead.
   *
   * This method doesn't expect any request body.
   */
  getKpiForCustomer1$Response(params: GetKpiForCustomer1$Params, context?: HttpContext): Observable<StrictHttpResponse<CustomerKpi>> {
    return getKpiForCustomer1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getKpiForCustomer1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getKpiForCustomer1(params: GetKpiForCustomer1$Params, context?: HttpContext): Observable<CustomerKpi> {
    return this.getKpiForCustomer1$Response(params, context).pipe(
      map((r: StrictHttpResponse<CustomerKpi>): CustomerKpi => r.body)
    );
  }

}
