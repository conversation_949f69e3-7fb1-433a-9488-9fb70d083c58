/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { FinStructureField } from '../models/fin-structure-field';
import { forceMirrorFinStructures } from '../fn/fin-structure-mirroring-internal-controller/force-mirror-fin-structures';
import { ForceMirrorFinStructures$Params } from '../fn/fin-structure-mirroring-internal-controller/force-mirror-fin-structures';
import { getMirroringEligibleFieldsFromMainFinStructure } from '../fn/fin-structure-mirroring-internal-controller/get-mirroring-eligible-fields-from-main-fin-structure';
import { GetMirroringEligibleFieldsFromMainFinStructure$Params } from '../fn/fin-structure-mirroring-internal-controller/get-mirroring-eligible-fields-from-main-fin-structure';
import { getMirroringEligibleStaticFields } from '../fn/fin-structure-mirroring-internal-controller/get-mirroring-eligible-static-fields';
import { GetMirroringEligibleStaticFields$Params } from '../fn/fin-structure-mirroring-internal-controller/get-mirroring-eligible-static-fields';

@Injectable({ providedIn: 'root' })
export class FinStructureMirroringInternalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `forceMirrorFinStructures()` */
  static readonly ForceMirrorFinStructuresPath = '/internal/fin-structure-mirroring/force-mirror-fin-structures';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `forceMirrorFinStructures()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  forceMirrorFinStructures$Response(params: ForceMirrorFinStructures$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return forceMirrorFinStructures(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `forceMirrorFinStructures$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  forceMirrorFinStructures(params: ForceMirrorFinStructures$Params, context?: HttpContext): Observable<void> {
    return this.forceMirrorFinStructures$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getMirroringEligibleStaticFields()` */
  static readonly GetMirroringEligibleStaticFieldsPath = '/internal/fin-structure-mirroring/eligible-fields';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getMirroringEligibleStaticFields()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMirroringEligibleStaticFields$Response(params: GetMirroringEligibleStaticFields$Params, context?: HttpContext): Observable<StrictHttpResponse<{
[key: string]: FinStructureField;
}>> {
    return getMirroringEligibleStaticFields(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getMirroringEligibleStaticFields$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMirroringEligibleStaticFields(params: GetMirroringEligibleStaticFields$Params, context?: HttpContext): Observable<{
[key: string]: FinStructureField;
}> {
    return this.getMirroringEligibleStaticFields$Response(params, context).pipe(
      map((r: StrictHttpResponse<{
[key: string]: FinStructureField;
}>): {
[key: string]: FinStructureField;
} => r.body)
    );
  }

  /** Path part for operation `getMirroringEligibleFieldsFromMainFinStructure()` */
  static readonly GetMirroringEligibleFieldsFromMainFinStructurePath = '/internal/fin-structure-mirroring/business-case/{businessCaseId}/eligible-fields';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getMirroringEligibleFieldsFromMainFinStructure()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMirroringEligibleFieldsFromMainFinStructure$Response(params: GetMirroringEligibleFieldsFromMainFinStructure$Params, context?: HttpContext): Observable<StrictHttpResponse<{
[key: string]: FinStructureField;
}>> {
    return getMirroringEligibleFieldsFromMainFinStructure(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getMirroringEligibleFieldsFromMainFinStructure$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMirroringEligibleFieldsFromMainFinStructure(params: GetMirroringEligibleFieldsFromMainFinStructure$Params, context?: HttpContext): Observable<{
[key: string]: FinStructureField;
}> {
    return this.getMirroringEligibleFieldsFromMainFinStructure$Response(params, context).pipe(
      map((r: StrictHttpResponse<{
[key: string]: FinStructureField;
}>): {
[key: string]: FinStructureField;
} => r.body)
    );
  }

}
