/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { BusinessCaseKpiMigrationResponse } from '../models/business-case-kpi-migration-response';
import { CustomerKpiMigrationResponse } from '../models/customer-kpi-migration-response';
import { KpiTemplatesInitResult } from '../models/kpi-templates-init-result';
import { migrateCustomerKpis } from '../fn/kpi-migration-controller/migrate-customer-kpis';
import { MigrateCustomerKpis$Params } from '../fn/kpi-migration-controller/migrate-customer-kpis';
import { migrateKpisForAllBusinessCases } from '../fn/kpi-migration-controller/migrate-kpis-for-all-business-cases';
import { MigrateKpisForAllBusinessCases$Params } from '../fn/kpi-migration-controller/migrate-kpis-for-all-business-cases';
import { migrateKpisForAllCustomers } from '../fn/kpi-migration-controller/migrate-kpis-for-all-customers';
import { MigrateKpisForAllCustomers$Params } from '../fn/kpi-migration-controller/migrate-kpis-for-all-customers';
import { setupKpiTemplates } from '../fn/kpi-migration-controller/setup-kpi-templates';
import { SetupKpiTemplates$Params } from '../fn/kpi-migration-controller/setup-kpi-templates';

@Injectable({ providedIn: 'root' })
export class KpiMigrationControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `setupKpiTemplates()` */
  static readonly SetupKpiTemplatesPath = '/migration/kpi/kpi-templates';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `setupKpiTemplates()` instead.
   *
   * This method doesn't expect any request body.
   */
  setupKpiTemplates$Response(params: SetupKpiTemplates$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<KpiTemplatesInitResult>>> {
    return setupKpiTemplates(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `setupKpiTemplates$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  setupKpiTemplates(params: SetupKpiTemplates$Params, context?: HttpContext): Observable<Array<KpiTemplatesInitResult>> {
    return this.setupKpiTemplates$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<KpiTemplatesInitResult>>): Array<KpiTemplatesInitResult> => r.body)
    );
  }

  /** Path part for operation `migrateCustomerKpis()` */
  static readonly MigrateCustomerKpisPath = '/migration/kpi/customers/{customerKey}/customer-kpis';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `migrateCustomerKpis()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateCustomerKpis$Response(params: MigrateCustomerKpis$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return migrateCustomerKpis(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `migrateCustomerKpis$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateCustomerKpis(params: MigrateCustomerKpis$Params, context?: HttpContext): Observable<void> {
    return this.migrateCustomerKpis$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `migrateKpisForAllCustomers()` */
  static readonly MigrateKpisForAllCustomersPath = '/migration/kpi/customers/customer-kpis';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `migrateKpisForAllCustomers()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateKpisForAllCustomers$Response(params: MigrateKpisForAllCustomers$Params, context?: HttpContext): Observable<StrictHttpResponse<CustomerKpiMigrationResponse>> {
    return migrateKpisForAllCustomers(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `migrateKpisForAllCustomers$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateKpisForAllCustomers(params: MigrateKpisForAllCustomers$Params, context?: HttpContext): Observable<CustomerKpiMigrationResponse> {
    return this.migrateKpisForAllCustomers$Response(params, context).pipe(
      map((r: StrictHttpResponse<CustomerKpiMigrationResponse>): CustomerKpiMigrationResponse => r.body)
    );
  }

  /** Path part for operation `migrateKpisForAllBusinessCases()` */
  static readonly MigrateKpisForAllBusinessCasesPath = '/migration/kpi/business-case-kpis';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `migrateKpisForAllBusinessCases()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateKpisForAllBusinessCases$Response(params: MigrateKpisForAllBusinessCases$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCaseKpiMigrationResponse>> {
    return migrateKpisForAllBusinessCases(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `migrateKpisForAllBusinessCases$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateKpisForAllBusinessCases(params: MigrateKpisForAllBusinessCases$Params, context?: HttpContext): Observable<BusinessCaseKpiMigrationResponse> {
    return this.migrateKpisForAllBusinessCases$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCaseKpiMigrationResponse>): BusinessCaseKpiMigrationResponse => r.body)
    );
  }

}
