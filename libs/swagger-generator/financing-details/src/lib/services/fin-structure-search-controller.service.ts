/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { autocompleteFinStructure } from '../fn/fin-structure-search-controller/autocomplete-fin-structure';
import { AutocompleteFinStructure$Params } from '../fn/fin-structure-search-controller/autocomplete-fin-structure';
import { autocompleteSharingEntity } from '../fn/fin-structure-search-controller/autocomplete-sharing-entity';
import { AutocompleteSharingEntity$Params } from '../fn/fin-structure-search-controller/autocomplete-sharing-entity';
import { FinStructureSearchResponse } from '../models/fin-structure-search-response';
import { searchInFinStructure } from '../fn/fin-structure-search-controller/search-in-fin-structure';
import { SearchInFinStructure$Params } from '../fn/fin-structure-search-controller/search-in-fin-structure';
import { searchInSharingEntity } from '../fn/fin-structure-search-controller/search-in-sharing-entity';
import { SearchInSharingEntity$Params } from '../fn/fin-structure-search-controller/search-in-sharing-entity';

@Injectable({ providedIn: 'root' })
export class FinStructureSearchControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `searchInSharingEntity()` */
  static readonly SearchInSharingEntityPath = '/business-case/{businessCaseId}/sharing-entity/{sharingEntityId}/search';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `searchInSharingEntity()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  searchInSharingEntity$Response(params: SearchInSharingEntity$Params, context?: HttpContext): Observable<StrictHttpResponse<FinStructureSearchResponse>> {
    return searchInSharingEntity(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `searchInSharingEntity$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  searchInSharingEntity(params: SearchInSharingEntity$Params, context?: HttpContext): Observable<FinStructureSearchResponse> {
    return this.searchInSharingEntity$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinStructureSearchResponse>): FinStructureSearchResponse => r.body)
    );
  }

  /** Path part for operation `autocompleteSharingEntity()` */
  static readonly AutocompleteSharingEntityPath = '/business-case/{businessCaseId}/sharing-entity/{sharingEntityId}/autocomplete';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `autocompleteSharingEntity()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  autocompleteSharingEntity$Response(params: AutocompleteSharingEntity$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return autocompleteSharingEntity(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `autocompleteSharingEntity$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  autocompleteSharingEntity(params: AutocompleteSharingEntity$Params, context?: HttpContext): Observable<Array<string>> {
    return this.autocompleteSharingEntity$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

  /** Path part for operation `searchInFinStructure()` */
  static readonly SearchInFinStructurePath = '/business-case/{businessCaseId}/fin-structure/{finStructureId}/search';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `searchInFinStructure()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  searchInFinStructure$Response(params: SearchInFinStructure$Params, context?: HttpContext): Observable<StrictHttpResponse<FinStructureSearchResponse>> {
    return searchInFinStructure(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `searchInFinStructure$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  searchInFinStructure(params: SearchInFinStructure$Params, context?: HttpContext): Observable<FinStructureSearchResponse> {
    return this.searchInFinStructure$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinStructureSearchResponse>): FinStructureSearchResponse => r.body)
    );
  }

  /** Path part for operation `autocompleteFinStructure()` */
  static readonly AutocompleteFinStructurePath = '/business-case/{businessCaseId}/fin-structure/{finStructureId}/autocomplete';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `autocompleteFinStructure()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  autocompleteFinStructure$Response(params: AutocompleteFinStructure$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return autocompleteFinStructure(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `autocompleteFinStructure$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  autocompleteFinStructure(params: AutocompleteFinStructure$Params, context?: HttpContext): Observable<Array<string>> {
    return this.autocompleteFinStructure$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

}
