/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { eligibleFinancingFieldsCalculatableMirroring } from '../fn/fin-structure-mirroring-controller/eligible-financing-fields-calculatable-mirroring';
import { EligibleFinancingFieldsCalculatableMirroring$Params } from '../fn/fin-structure-mirroring-controller/eligible-financing-fields-calculatable-mirroring';
import { getMirroringEligibleStaticFieldKeys } from '../fn/fin-structure-mirroring-controller/get-mirroring-eligible-static-field-keys';
import { GetMirroringEligibleStaticFieldKeys$Params } from '../fn/fin-structure-mirroring-controller/get-mirroring-eligible-static-field-keys';

@Injectable({ providedIn: 'root' })
export class FinStructureMirroringControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getMirroringEligibleStaticFieldKeys()` */
  static readonly GetMirroringEligibleStaticFieldKeysPath = '/fin-structure-mirroring/eligible-field-keys';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getMirroringEligibleStaticFieldKeys()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMirroringEligibleStaticFieldKeys$Response(params: GetMirroringEligibleStaticFieldKeys$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return getMirroringEligibleStaticFieldKeys(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getMirroringEligibleStaticFieldKeys$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMirroringEligibleStaticFieldKeys(params: GetMirroringEligibleStaticFieldKeys$Params, context?: HttpContext): Observable<Array<string>> {
    return this.getMirroringEligibleStaticFieldKeys$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

  /** Path part for operation `eligibleFinancingFieldsCalculatableMirroring()` */
  static readonly EligibleFinancingFieldsCalculatableMirroringPath = '/fin-structure-mirroring/calculable-field-keys';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `eligibleFinancingFieldsCalculatableMirroring()` instead.
   *
   * This method doesn't expect any request body.
   */
  eligibleFinancingFieldsCalculatableMirroring$Response(params: EligibleFinancingFieldsCalculatableMirroring$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return eligibleFinancingFieldsCalculatableMirroring(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `eligibleFinancingFieldsCalculatableMirroring$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  eligibleFinancingFieldsCalculatableMirroring(params: EligibleFinancingFieldsCalculatableMirroring$Params, context?: HttpContext): Observable<Array<string>> {
    return this.eligibleFinancingFieldsCalculatableMirroring$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

}
