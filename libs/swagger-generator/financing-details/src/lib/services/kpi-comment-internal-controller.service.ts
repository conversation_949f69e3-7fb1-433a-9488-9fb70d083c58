/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { getBusinessCaseKpiComments } from '../fn/kpi-comment-internal-controller/get-business-case-kpi-comments';
import { GetBusinessCaseKpiComments$Params } from '../fn/kpi-comment-internal-controller/get-business-case-kpi-comments';
import { KpiComment } from '../models/kpi-comment';

@Injectable({ providedIn: 'root' })
export class KpiCommentInternalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getBusinessCaseKpiComments()` */
  static readonly GetBusinessCaseKpiCommentsPath = '/internal/business-case/{businessCaseId}/kpi-comments';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getBusinessCaseKpiComments()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCaseKpiComments$Response(params: GetBusinessCaseKpiComments$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<KpiComment>>> {
    return getBusinessCaseKpiComments(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getBusinessCaseKpiComments$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCaseKpiComments(params: GetBusinessCaseKpiComments$Params, context?: HttpContext): Observable<Array<KpiComment>> {
    return this.getBusinessCaseKpiComments$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<KpiComment>>): Array<KpiComment> => r.body)
    );
  }

}
