/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { FinPartnerOverviewResponse } from '../models/fin-partner-overview-response';
import { getFinancingPartnersData } from '../fn/fin-structure-fin-partners-controller/get-financing-partners-data';
import { GetFinancingPartnersData$Params } from '../fn/fin-structure-fin-partners-controller/get-financing-partners-data';

@Injectable({ providedIn: 'root' })
export class FinStructureFinPartnersControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getFinancingPartnersData()` */
  static readonly GetFinancingPartnersDataPath = '/fin-structure-fin-partners/{businessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFinancingPartnersData()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinancingPartnersData$Response(params: GetFinancingPartnersData$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<FinPartnerOverviewResponse>>> {
    return getFinancingPartnersData(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFinancingPartnersData$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinancingPartnersData(params: GetFinancingPartnersData$Params, context?: HttpContext): Observable<Array<FinPartnerOverviewResponse>> {
    return this.getFinancingPartnersData$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<FinPartnerOverviewResponse>>): Array<FinPartnerOverviewResponse> => r.body)
    );
  }

}
