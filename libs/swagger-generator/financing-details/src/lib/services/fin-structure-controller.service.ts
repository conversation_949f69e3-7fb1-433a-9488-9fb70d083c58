/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { addDescriptionToFinStructureField } from '../fn/fin-structure-controller/add-description-to-fin-structure-field';
import { AddDescriptionToFinStructureField$Params } from '../fn/fin-structure-controller/add-description-to-fin-structure-field';
import { addDynamicFieldsetToFinStructure } from '../fn/fin-structure-controller/add-dynamic-fieldset-to-fin-structure';
import { AddDynamicFieldsetToFinStructure$Params } from '../fn/fin-structure-controller/add-dynamic-fieldset-to-fin-structure';
import { addValueToFinStructureField1 } from '../fn/fin-structure-controller/add-value-to-fin-structure-field-1';
import { AddValueToFinStructureField1$Params } from '../fn/fin-structure-controller/add-value-to-fin-structure-field-1';
import { CatalogueData } from '../models/catalogue-data';
import { changeVisibilityOfFinStructureField } from '../fn/fin-structure-controller/change-visibility-of-fin-structure-field';
import { ChangeVisibilityOfFinStructureField$Params } from '../fn/fin-structure-controller/change-visibility-of-fin-structure-field';
import { deleteDynamicFieldsetFromFinStructure } from '../fn/fin-structure-controller/delete-dynamic-fieldset-from-fin-structure';
import { DeleteDynamicFieldsetFromFinStructure$Params } from '../fn/fin-structure-controller/delete-dynamic-fieldset-from-fin-structure';
import { enableOrDisableFinStructureGroup } from '../fn/fin-structure-controller/enable-or-disable-fin-structure-group';
import { EnableOrDisableFinStructureGroup$Params } from '../fn/fin-structure-controller/enable-or-disable-fin-structure-group';
import { FinancingStructure } from '../models/financing-structure';
import { FinStructureField } from '../models/fin-structure-field';
import { FinStructureRevision } from '../models/fin-structure-revision';
import { FinStructureTeaserResponse } from '../models/fin-structure-teaser-response';
import { getAllCatalogueData } from '../fn/fin-structure-controller/get-all-catalogue-data';
import { GetAllCatalogueData$Params } from '../fn/fin-structure-controller/get-all-catalogue-data';
import { getAllRevisionsForFieldInFinStructure } from '../fn/fin-structure-controller/get-all-revisions-for-field-in-fin-structure';
import { GetAllRevisionsForFieldInFinStructure$Params } from '../fn/fin-structure-controller/get-all-revisions-for-field-in-fin-structure';
import { getAllRevisionsForFinStructure } from '../fn/fin-structure-controller/get-all-revisions-for-fin-structure';
import { GetAllRevisionsForFinStructure$Params } from '../fn/fin-structure-controller/get-all-revisions-for-fin-structure';
import { getBusinessCaseMainFinStructure } from '../fn/fin-structure-controller/get-business-case-main-fin-structure';
import { GetBusinessCaseMainFinStructure$Params } from '../fn/fin-structure-controller/get-business-case-main-fin-structure';
import { getFinancingStructureMainGroups } from '../fn/fin-structure-controller/get-financing-structure-main-groups';
import { GetFinancingStructureMainGroups$Params } from '../fn/fin-structure-controller/get-financing-structure-main-groups';
import { getFinStructureBySharingEntityId } from '../fn/fin-structure-controller/get-fin-structure-by-sharing-entity-id';
import { GetFinStructureBySharingEntityId$Params } from '../fn/fin-structure-controller/get-fin-structure-by-sharing-entity-id';
import { getFinStructureCopy } from '../fn/fin-structure-controller/get-fin-structure-copy';
import { GetFinStructureCopy$Params } from '../fn/fin-structure-controller/get-fin-structure-copy';
import { getFinStructureSharingGroupsInformation } from '../fn/fin-structure-controller/get-fin-structure-sharing-groups-information';
import { GetFinStructureSharingGroupsInformation$Params } from '../fn/fin-structure-controller/get-fin-structure-sharing-groups-information';
import { getLeadFinancingStructureCommonFields1 } from '../fn/fin-structure-controller/get-lead-financing-structure-common-fields-1';
import { GetLeadFinancingStructureCommonFields1$Params } from '../fn/fin-structure-controller/get-lead-financing-structure-common-fields-1';
import { getMyFinStructureTeaser } from '../fn/fin-structure-controller/get-my-fin-structure-teaser';
import { GetMyFinStructureTeaser$Params } from '../fn/fin-structure-controller/get-my-fin-structure-teaser';
import { getSharedFinStructureTeaser } from '../fn/fin-structure-controller/get-shared-fin-structure-teaser';
import { GetSharedFinStructureTeaser$Params } from '../fn/fin-structure-controller/get-shared-fin-structure-teaser';
import { PlainGroup } from '../models/plain-group';
import { renameDynamicFieldsetInFinStructure } from '../fn/fin-structure-controller/rename-dynamic-fieldset-in-fin-structure';
import { RenameDynamicFieldsetInFinStructure$Params } from '../fn/fin-structure-controller/rename-dynamic-fieldset-in-fin-structure';
import { restoreRevisionInFinStructure } from '../fn/fin-structure-controller/restore-revision-in-fin-structure';
import { RestoreRevisionInFinStructure$Params } from '../fn/fin-structure-controller/restore-revision-in-fin-structure';

@Injectable({ providedIn: 'root' })
export class FinStructureControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `restoreRevisionInFinStructure()` */
  static readonly RestoreRevisionInFinStructurePath = '/fin-structure-management/{businessCaseId}/{finStructureId}/restore-revision';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `restoreRevisionInFinStructure()` instead.
   *
   * This method doesn't expect any request body.
   */
  restoreRevisionInFinStructure$Response(params: RestoreRevisionInFinStructure$Params, context?: HttpContext): Observable<StrictHttpResponse<FinancingStructure>> {
    return restoreRevisionInFinStructure(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `restoreRevisionInFinStructure$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  restoreRevisionInFinStructure(params: RestoreRevisionInFinStructure$Params, context?: HttpContext): Observable<FinancingStructure> {
    return this.restoreRevisionInFinStructure$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinancingStructure>): FinancingStructure => r.body)
    );
  }

  /** Path part for operation `renameDynamicFieldsetInFinStructure()` */
  static readonly RenameDynamicFieldsetInFinStructurePath = '/fin-structure-management/{businessCaseId}/rename-dynamic-fieldset/{fieldsetName}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `renameDynamicFieldsetInFinStructure()` instead.
   *
   * This method doesn't expect any request body.
   */
  renameDynamicFieldsetInFinStructure$Response(params: RenameDynamicFieldsetInFinStructure$Params, context?: HttpContext): Observable<StrictHttpResponse<FinancingStructure>> {
    return renameDynamicFieldsetInFinStructure(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `renameDynamicFieldsetInFinStructure$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  renameDynamicFieldsetInFinStructure(params: RenameDynamicFieldsetInFinStructure$Params, context?: HttpContext): Observable<FinancingStructure> {
    return this.renameDynamicFieldsetInFinStructure$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinancingStructure>): FinancingStructure => r.body)
    );
  }

  /** Path part for operation `enableOrDisableFinStructureGroup()` */
  static readonly EnableOrDisableFinStructureGroupPath = '/fin-structure-management/{businessCaseId}/enable-or-disable-group';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `enableOrDisableFinStructureGroup()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  enableOrDisableFinStructureGroup$Response(params: EnableOrDisableFinStructureGroup$Params, context?: HttpContext): Observable<StrictHttpResponse<FinancingStructure>> {
    return enableOrDisableFinStructureGroup(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `enableOrDisableFinStructureGroup$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  enableOrDisableFinStructureGroup(params: EnableOrDisableFinStructureGroup$Params, context?: HttpContext): Observable<FinancingStructure> {
    return this.enableOrDisableFinStructureGroup$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinancingStructure>): FinancingStructure => r.body)
    );
  }

  /** Path part for operation `changeVisibilityOfFinStructureField()` */
  static readonly ChangeVisibilityOfFinStructureFieldPath = '/fin-structure-management/{businessCaseId}/change-field-visibility';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `changeVisibilityOfFinStructureField()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  changeVisibilityOfFinStructureField$Response(params: ChangeVisibilityOfFinStructureField$Params, context?: HttpContext): Observable<StrictHttpResponse<FinancingStructure>> {
    return changeVisibilityOfFinStructureField(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `changeVisibilityOfFinStructureField$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  changeVisibilityOfFinStructureField(params: ChangeVisibilityOfFinStructureField$Params, context?: HttpContext): Observable<FinancingStructure> {
    return this.changeVisibilityOfFinStructureField$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinancingStructure>): FinancingStructure => r.body)
    );
  }

  /** Path part for operation `addValueToFinStructureField1()` */
  static readonly AddValueToFinStructureField1Path = '/fin-structure-management/{businessCaseId}/add-value-to-field';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `addValueToFinStructureField1()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addValueToFinStructureField1$Response(params: AddValueToFinStructureField1$Params, context?: HttpContext): Observable<StrictHttpResponse<FinancingStructure>> {
    return addValueToFinStructureField1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `addValueToFinStructureField1$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addValueToFinStructureField1(params: AddValueToFinStructureField1$Params, context?: HttpContext): Observable<FinancingStructure> {
    return this.addValueToFinStructureField1$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinancingStructure>): FinancingStructure => r.body)
    );
  }

  /** Path part for operation `addDynamicFieldsetToFinStructure()` */
  static readonly AddDynamicFieldsetToFinStructurePath = '/fin-structure-management/{businessCaseId}/add-dynamic-fieldset';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `addDynamicFieldsetToFinStructure()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addDynamicFieldsetToFinStructure$Response(params: AddDynamicFieldsetToFinStructure$Params, context?: HttpContext): Observable<StrictHttpResponse<FinancingStructure>> {
    return addDynamicFieldsetToFinStructure(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `addDynamicFieldsetToFinStructure$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addDynamicFieldsetToFinStructure(params: AddDynamicFieldsetToFinStructure$Params, context?: HttpContext): Observable<FinancingStructure> {
    return this.addDynamicFieldsetToFinStructure$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinancingStructure>): FinancingStructure => r.body)
    );
  }

  /** Path part for operation `addDescriptionToFinStructureField()` */
  static readonly AddDescriptionToFinStructureFieldPath = '/fin-structure-management/{businessCaseId}/add-description-to-field';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `addDescriptionToFinStructureField()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addDescriptionToFinStructureField$Response(params: AddDescriptionToFinStructureField$Params, context?: HttpContext): Observable<StrictHttpResponse<FinancingStructure>> {
    return addDescriptionToFinStructureField(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `addDescriptionToFinStructureField$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addDescriptionToFinStructureField(params: AddDescriptionToFinStructureField$Params, context?: HttpContext): Observable<FinancingStructure> {
    return this.addDescriptionToFinStructureField$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinancingStructure>): FinancingStructure => r.body)
    );
  }

  /** Path part for operation `getFinStructureSharingGroupsInformation()` */
  static readonly GetFinStructureSharingGroupsInformationPath = '/fin-structure-management/{businessCaseId}/{toCustomerKey}/fin-structure-sharing-groups';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFinStructureSharingGroupsInformation()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinStructureSharingGroupsInformation$Response(params: GetFinStructureSharingGroupsInformation$Params, context?: HttpContext): Observable<StrictHttpResponse<FinancingStructure>> {
    return getFinStructureSharingGroupsInformation(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFinStructureSharingGroupsInformation$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinStructureSharingGroupsInformation(params: GetFinStructureSharingGroupsInformation$Params, context?: HttpContext): Observable<FinancingStructure> {
    return this.getFinStructureSharingGroupsInformation$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinancingStructure>): FinancingStructure => r.body)
    );
  }

  /** Path part for operation `getFinStructureBySharingEntityId()` */
  static readonly GetFinStructureBySharingEntityIdPath = '/fin-structure-management/{businessCaseId}/{sharingEntityId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFinStructureBySharingEntityId()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinStructureBySharingEntityId$Response(params: GetFinStructureBySharingEntityId$Params, context?: HttpContext): Observable<StrictHttpResponse<FinancingStructure>> {
    return getFinStructureBySharingEntityId(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFinStructureBySharingEntityId$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinStructureBySharingEntityId(params: GetFinStructureBySharingEntityId$Params, context?: HttpContext): Observable<FinancingStructure> {
    return this.getFinStructureBySharingEntityId$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinancingStructure>): FinancingStructure => r.body)
    );
  }

  /** Path part for operation `getSharedFinStructureTeaser()` */
  static readonly GetSharedFinStructureTeaserPath = '/fin-structure-management/{businessCaseId}/{fromCustomerKey}/shared-fin-structure-teaser';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getSharedFinStructureTeaser()` instead.
   *
   * This method doesn't expect any request body.
   */
  getSharedFinStructureTeaser$Response(params: GetSharedFinStructureTeaser$Params, context?: HttpContext): Observable<StrictHttpResponse<FinStructureTeaserResponse>> {
    return getSharedFinStructureTeaser(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getSharedFinStructureTeaser$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getSharedFinStructureTeaser(params: GetSharedFinStructureTeaser$Params, context?: HttpContext): Observable<FinStructureTeaserResponse> {
    return this.getSharedFinStructureTeaser$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinStructureTeaserResponse>): FinStructureTeaserResponse => r.body)
    );
  }

  /** Path part for operation `getAllRevisionsForFieldInFinStructure()` */
  static readonly GetAllRevisionsForFieldInFinStructurePath = '/fin-structure-management/{businessCaseId}/{finStructureId}/{fieldId}/get-all-revisions';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllRevisionsForFieldInFinStructure()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllRevisionsForFieldInFinStructure$Response(params: GetAllRevisionsForFieldInFinStructure$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<FinStructureRevision>>> {
    return getAllRevisionsForFieldInFinStructure(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllRevisionsForFieldInFinStructure$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllRevisionsForFieldInFinStructure(params: GetAllRevisionsForFieldInFinStructure$Params, context?: HttpContext): Observable<Array<FinStructureRevision>> {
    return this.getAllRevisionsForFieldInFinStructure$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<FinStructureRevision>>): Array<FinStructureRevision> => r.body)
    );
  }

  /** Path part for operation `getAllRevisionsForFinStructure()` */
  static readonly GetAllRevisionsForFinStructurePath = '/fin-structure-management/{businessCaseId}/{finStructureId}/get-all-revisions';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllRevisionsForFinStructure()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllRevisionsForFinStructure$Response(params: GetAllRevisionsForFinStructure$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<FinStructureRevision>>> {
    return getAllRevisionsForFinStructure(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllRevisionsForFinStructure$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllRevisionsForFinStructure(params: GetAllRevisionsForFinStructure$Params, context?: HttpContext): Observable<Array<FinStructureRevision>> {
    return this.getAllRevisionsForFinStructure$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<FinStructureRevision>>): Array<FinStructureRevision> => r.body)
    );
  }

  /** Path part for operation `getMyFinStructureTeaser()` */
  static readonly GetMyFinStructureTeaserPath = '/fin-structure-management/{businessCaseId}/my-fin-structure-teaser';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getMyFinStructureTeaser()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMyFinStructureTeaser$Response(params: GetMyFinStructureTeaser$Params, context?: HttpContext): Observable<StrictHttpResponse<FinStructureTeaserResponse>> {
    return getMyFinStructureTeaser(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getMyFinStructureTeaser$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMyFinStructureTeaser(params: GetMyFinStructureTeaser$Params, context?: HttpContext): Observable<FinStructureTeaserResponse> {
    return this.getMyFinStructureTeaser$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinStructureTeaserResponse>): FinStructureTeaserResponse => r.body)
    );
  }

  /** Path part for operation `getBusinessCaseMainFinStructure()` */
  static readonly GetBusinessCaseMainFinStructurePath = '/fin-structure-management/{businessCaseId}/get-main-financing-structure';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getBusinessCaseMainFinStructure()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCaseMainFinStructure$Response(params: GetBusinessCaseMainFinStructure$Params, context?: HttpContext): Observable<StrictHttpResponse<FinancingStructure>> {
    return getBusinessCaseMainFinStructure(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getBusinessCaseMainFinStructure$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCaseMainFinStructure(params: GetBusinessCaseMainFinStructure$Params, context?: HttpContext): Observable<FinancingStructure> {
    return this.getBusinessCaseMainFinStructure$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinancingStructure>): FinancingStructure => r.body)
    );
  }

  /** Path part for operation `getLeadFinancingStructureCommonFields1()` */
  static readonly GetLeadFinancingStructureCommonFields1Path = '/fin-structure-management/{businessCaseId}/get-lead-financing-structure-common-fields';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getLeadFinancingStructureCommonFields1()` instead.
   *
   * This method doesn't expect any request body.
   */
  getLeadFinancingStructureCommonFields1$Response(params: GetLeadFinancingStructureCommonFields1$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<FinStructureField>>> {
    return getLeadFinancingStructureCommonFields1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getLeadFinancingStructureCommonFields1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getLeadFinancingStructureCommonFields1(params: GetLeadFinancingStructureCommonFields1$Params, context?: HttpContext): Observable<Array<FinStructureField>> {
    return this.getLeadFinancingStructureCommonFields1$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<FinStructureField>>): Array<FinStructureField> => r.body)
    );
  }

  /** Path part for operation `getFinStructureCopy()` */
  static readonly GetFinStructureCopyPath = '/fin-structure-management/{businessCaseId}/get-financing-structure-copy';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFinStructureCopy()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinStructureCopy$Response(params: GetFinStructureCopy$Params, context?: HttpContext): Observable<StrictHttpResponse<FinancingStructure>> {
    return getFinStructureCopy(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFinStructureCopy$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinStructureCopy(params: GetFinStructureCopy$Params, context?: HttpContext): Observable<FinancingStructure> {
    return this.getFinStructureCopy$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinancingStructure>): FinancingStructure => r.body)
    );
  }

  /** Path part for operation `getFinancingStructureMainGroups()` */
  static readonly GetFinancingStructureMainGroupsPath = '/fin-structure-management/{businessCaseId}/financing-structure-main-groups';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFinancingStructureMainGroups()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinancingStructureMainGroups$Response(params: GetFinancingStructureMainGroups$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<PlainGroup>>> {
    return getFinancingStructureMainGroups(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFinancingStructureMainGroups$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFinancingStructureMainGroups(params: GetFinancingStructureMainGroups$Params, context?: HttpContext): Observable<Array<PlainGroup>> {
    return this.getFinancingStructureMainGroups$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<PlainGroup>>): Array<PlainGroup> => r.body)
    );
  }

  /** Path part for operation `getAllCatalogueData()` */
  static readonly GetAllCatalogueDataPath = '/fin-structure-management/{businessCaseId}/catalogue-data';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllCatalogueData()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllCatalogueData$Response(params: GetAllCatalogueData$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<CatalogueData>>> {
    return getAllCatalogueData(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllCatalogueData$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllCatalogueData(params: GetAllCatalogueData$Params, context?: HttpContext): Observable<Array<CatalogueData>> {
    return this.getAllCatalogueData$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<CatalogueData>>): Array<CatalogueData> => r.body)
    );
  }

  /** Path part for operation `deleteDynamicFieldsetFromFinStructure()` */
  static readonly DeleteDynamicFieldsetFromFinStructurePath = '/fin-structure-management/{businessCaseId}/delete-dynamic-fieldset/{fieldsetName}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteDynamicFieldsetFromFinStructure()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteDynamicFieldsetFromFinStructure$Response(params: DeleteDynamicFieldsetFromFinStructure$Params, context?: HttpContext): Observable<StrictHttpResponse<FinancingStructure>> {
    return deleteDynamicFieldsetFromFinStructure(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteDynamicFieldsetFromFinStructure$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteDynamicFieldsetFromFinStructure(params: DeleteDynamicFieldsetFromFinStructure$Params, context?: HttpContext): Observable<FinancingStructure> {
    return this.deleteDynamicFieldsetFromFinStructure$Response(params, context).pipe(
      map((r: StrictHttpResponse<FinancingStructure>): FinancingStructure => r.body)
    );
  }

}
