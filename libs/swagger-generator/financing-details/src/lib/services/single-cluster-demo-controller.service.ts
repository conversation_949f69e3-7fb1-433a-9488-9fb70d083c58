/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { CustomerKpi } from '../models/customer-kpi';
import { deploy } from '../fn/single-cluster-demo-controller/deploy';
import { Deploy$Params } from '../fn/single-cluster-demo-controller/deploy';
import { getCustomerKpis } from '../fn/single-cluster-demo-controller/get-customer-kpis';
import { GetCustomerKpis$Params } from '../fn/single-cluster-demo-controller/get-customer-kpis';
import { recall } from '../fn/single-cluster-demo-controller/recall';
import { Recall$Params } from '../fn/single-cluster-demo-controller/recall';

@Injectable({ providedIn: 'root' })
export class SingleClusterDemoControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `recall()` */
  static readonly RecallPath = '/demo/recall';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `recall()` instead.
   *
   * This method doesn't expect any request body.
   */
  recall$Response(params: Recall$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return recall(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `recall$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  recall(params: Recall$Params, context?: HttpContext): Observable<void> {
    return this.recall$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `deploy()` */
  static readonly DeployPath = '/demo/deploy';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deploy()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  deploy$Response(params: Deploy$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return deploy(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deploy$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  deploy(params: Deploy$Params, context?: HttpContext): Observable<void> {
    return this.deploy$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getCustomerKpis()` */
  static readonly GetCustomerKpisPath = '/demo/customers/{customerKey}/kpis';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getCustomerKpis()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCustomerKpis$Response(params: GetCustomerKpis$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<CustomerKpi>>> {
    return getCustomerKpis(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getCustomerKpis$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCustomerKpis(params: GetCustomerKpis$Params, context?: HttpContext): Observable<Array<CustomerKpi>> {
    return this.getCustomerKpis$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<CustomerKpi>>): Array<CustomerKpi> => r.body)
    );
  }

}
