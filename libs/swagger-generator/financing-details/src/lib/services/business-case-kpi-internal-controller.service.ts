/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { BusinessCaseKpi } from '../models/business-case-kpi';
import { getAllKpisForBusinessCase } from '../fn/business-case-kpi-internal-controller/get-all-kpis-for-business-case';
import { GetAllKpisForBusinessCase$Params } from '../fn/business-case-kpi-internal-controller/get-all-kpis-for-business-case';

@Injectable({ providedIn: 'root' })
export class BusinessCaseKpiInternalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getAllKpisForBusinessCase()` */
  static readonly GetAllKpisForBusinessCasePath = '/internal/business-case/{businessCaseId}/kpis';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllKpisForBusinessCase()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllKpisForBusinessCase$Response(params: GetAllKpisForBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<BusinessCaseKpi>>> {
    return getAllKpisForBusinessCase(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllKpisForBusinessCase$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllKpisForBusinessCase(params: GetAllKpisForBusinessCase$Params, context?: HttpContext): Observable<Array<BusinessCaseKpi>> {
    return this.getAllKpisForBusinessCase$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<BusinessCaseKpi>>): Array<BusinessCaseKpi> => r.body)
    );
  }

}
