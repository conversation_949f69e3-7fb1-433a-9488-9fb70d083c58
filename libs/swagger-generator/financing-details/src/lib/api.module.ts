/* tslint:disable */
/* eslint-disable */
import { NgM<PERSON><PERSON>, ModuleWithProviders, <PERSON><PERSON><PERSON>elf, Optional } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiConfiguration, ApiConfigurationParams } from './api-configuration';

import { FinStructureControllerService } from './services/fin-structure-controller.service';
import { KpiMigrationControllerService } from './services/kpi-migration-controller.service';
import { MigrationNeoGptInformationUpdateControllerService } from './services/migration-neo-gpt-information-update-controller.service';
import { MigrationFinancingStructuresControllerService } from './services/migration-financing-structures-controller.service';
import { FinStructureInternalControllerService } from './services/fin-structure-internal-controller.service';
import { FinStructureMirroringInternalControllerService } from './services/fin-structure-mirroring-internal-controller.service';
import { FinStructureS<PERSON>ngControllerService } from './services/fin-structure-sharing-controller.service';
import { SingleClusterDemoControllerService } from './services/single-cluster-demo-controller.service';
import { FinStructureSearchControllerService } from './services/fin-structure-search-controller.service';
import { KpiCommentControllerService } from './services/kpi-comment-controller.service';
import { CustomerKpiControllerService } from './services/customer-kpi-controller.service';
import { BusinessCaseKpiControllerService } from './services/business-case-kpi-controller.service';
import { CustomerKpiInternalControllerService } from './services/customer-kpi-internal-controller.service';
import { BusinessCaseKpiInternalControllerService } from './services/business-case-kpi-internal-controller.service';
import { KpiCommentInternalControllerService } from './services/kpi-comment-internal-controller.service';
import { FinStructureMirroringControllerService } from './services/fin-structure-mirroring-controller.service';
import { FinStructureFinPartnersControllerService } from './services/fin-structure-fin-partners-controller.service';
import { BusinessCasesPhysicalRemovalControllerService } from './services/business-cases-physical-removal-controller.service';

/**
 * Module that provides all services and configuration.
 */
@NgModule({
  imports: [],
  exports: [],
  declarations: [],
  providers: [
    FinStructureControllerService,
    KpiMigrationControllerService,
    MigrationNeoGptInformationUpdateControllerService,
    MigrationFinancingStructuresControllerService,
    FinStructureInternalControllerService,
    FinStructureMirroringInternalControllerService,
    FinStructureSharingControllerService,
    SingleClusterDemoControllerService,
    FinStructureSearchControllerService,
    KpiCommentControllerService,
    CustomerKpiControllerService,
    BusinessCaseKpiControllerService,
    CustomerKpiInternalControllerService,
    BusinessCaseKpiInternalControllerService,
    KpiCommentInternalControllerService,
    FinStructureMirroringControllerService,
    FinStructureFinPartnersControllerService,
    BusinessCasesPhysicalRemovalControllerService,
    ApiConfiguration
  ],
})
export class ApiModule {
  static forRoot(params: ApiConfigurationParams): ModuleWithProviders<ApiModule> {
    return {
      ngModule: ApiModule,
      providers: [
        {
          provide: ApiConfiguration,
          useValue: params
        }
      ]
    }
  }

  constructor( 
    @Optional() @SkipSelf() parentModule: ApiModule,
    @Optional() http: HttpClient
  ) {
    if (parentModule) {
      throw new Error('ApiModule is already loaded. Import in your base AppModule only.');
    }
    if (!http) {
      throw new Error('You need to import the HttpClientModule in your AppModule! \n' +
      'See also https://github.com/angular/angular/issues/20575');
    }
  }
}
