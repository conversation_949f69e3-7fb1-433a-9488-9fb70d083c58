/* tslint:disable */
/* eslint-disable */
import { <PERSON><PERSON><PERSON><PERSON>, ModuleWithProviders, Ski<PERSON><PERSON>elf, Optional } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiConfiguration, ApiConfigurationParams } from './api-configuration';

import { AssignmentAwaitingUpdateEntityControllerService } from './services/assignment-awaiting-update-entity-controller.service';
import { AssignmentAwaitingUpdateSearchControllerService } from './services/assignment-awaiting-update-search-controller.service';
import { ProfileControllerService } from './services/profile-controller.service';
import { UserAssignmentEntityControllerService } from './services/user-assignment-entity-controller.service';
import { UserAssignmentSearchControllerService } from './services/user-assignment-search-controller.service';
import { UserAssignmentControllerService } from './services/user-assignment-controller.service';
import { InternalControllerService } from './services/internal-controller.service';

/**
 * Module that provides all services and configuration.
 */
@NgModule({
  imports: [],
  exports: [],
  declarations: [],
  providers: [
    AssignmentAwaitingUpdateEntityControllerService,
    AssignmentAwaitingUpdateSearchControllerService,
    ProfileControllerService,
    UserAssignmentEntityControllerService,
    UserAssignmentSearchControllerService,
    UserAssignmentControllerService,
    InternalControllerService,
    ApiConfiguration
  ],
})
export class ApiModule {
  static forRoot(params: ApiConfigurationParams): ModuleWithProviders<ApiModule> {
    return {
      ngModule: ApiModule,
      providers: [
        {
          provide: ApiConfiguration,
          useValue: params
        }
      ]
    }
  }

  constructor( 
    @Optional() @SkipSelf() parentModule: ApiModule,
    @Optional() http: HttpClient
  ) {
    if (parentModule) {
      throw new Error('ApiModule is already loaded. Import in your base AppModule only.');
    }
    if (!http) {
      throw new Error('You need to import the HttpClientModule in your AppModule! \n' +
      'See also https://github.com/angular/angular/issues/20575');
    }
  }
}
