/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { CollectionModelEntityModelUserAssignment } from '../models/collection-model-entity-model-user-assignment';
import { EntityModelUserAssignment } from '../models/entity-model-user-assignment';
import { executeSearchUserassignmentGet } from '../fn/user-assignment-search-controller/execute-search-userassignment-get';
import { ExecuteSearchUserassignmentGet$Params } from '../fn/user-assignment-search-controller/execute-search-userassignment-get';
import { executeSearchUserassignmentGet1 } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-1';
import { ExecuteSearchUserassignmentGet1$Params } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-1';
import { executeSearchUserassignmentGet10 } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-10';
import { ExecuteSearchUserassignmentGet10$Params } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-10';
import { executeSearchUserassignmentGet11 } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-11';
import { ExecuteSearchUserassignmentGet11$Params } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-11';
import { executeSearchUserassignmentGet2 } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-2';
import { ExecuteSearchUserassignmentGet2$Params } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-2';
import { executeSearchUserassignmentGet3 } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-3';
import { ExecuteSearchUserassignmentGet3$Params } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-3';
import { executeSearchUserassignmentGet4 } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-4';
import { ExecuteSearchUserassignmentGet4$Params } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-4';
import { executeSearchUserassignmentGet5 } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-5';
import { ExecuteSearchUserassignmentGet5$Params } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-5';
import { executeSearchUserassignmentGet6 } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-6';
import { ExecuteSearchUserassignmentGet6$Params } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-6';
import { executeSearchUserassignmentGet7 } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-7';
import { ExecuteSearchUserassignmentGet7$Params } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-7';
import { executeSearchUserassignmentGet8 } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-8';
import { ExecuteSearchUserassignmentGet8$Params } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-8';
import { executeSearchUserassignmentGet9 } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-9';
import { ExecuteSearchUserassignmentGet9$Params } from '../fn/user-assignment-search-controller/execute-search-userassignment-get-9';
import { PagedModelEntityModelUserAssignment } from '../models/paged-model-entity-model-user-assignment';

@Injectable({ providedIn: 'root' })
export class UserAssignmentSearchControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `executeSearchUserassignmentGet()` */
  static readonly ExecuteSearchUserassignmentGetPath = '/userAssignments/search/countByAssigneeUserIdAndBusinessCaseIdAndStatusIn';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `executeSearchUserassignmentGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet$Response(params?: ExecuteSearchUserassignmentGet$Params, context?: HttpContext): Observable<StrictHttpResponse<number>> {
    return executeSearchUserassignmentGet(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `executeSearchUserassignmentGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet(params?: ExecuteSearchUserassignmentGet$Params, context?: HttpContext): Observable<number> {
    return this.executeSearchUserassignmentGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<number>): number => r.body)
    );
  }

  /** Path part for operation `executeSearchUserassignmentGet1()` */
  static readonly ExecuteSearchUserassignmentGet1Path = '/userAssignments/search/countByAssigneeUserIdAndBusinessCaseIdAndStatusInWithSearch';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `executeSearchUserassignmentGet1()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet1$Response(params?: ExecuteSearchUserassignmentGet1$Params, context?: HttpContext): Observable<StrictHttpResponse<number>> {
    return executeSearchUserassignmentGet1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `executeSearchUserassignmentGet1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet1(params?: ExecuteSearchUserassignmentGet1$Params, context?: HttpContext): Observable<number> {
    return this.executeSearchUserassignmentGet1$Response(params, context).pipe(
      map((r: StrictHttpResponse<number>): number => r.body)
    );
  }

  /** Path part for operation `executeSearchUserassignmentGet2()` */
  static readonly ExecuteSearchUserassignmentGet2Path = '/userAssignments/search/countByUserIdAndStatuses';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `executeSearchUserassignmentGet2()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet2$Response(params?: ExecuteSearchUserassignmentGet2$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelUserAssignment>> {
    return executeSearchUserassignmentGet2(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `executeSearchUserassignmentGet2$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet2(params?: ExecuteSearchUserassignmentGet2$Params, context?: HttpContext): Observable<EntityModelUserAssignment> {
    return this.executeSearchUserassignmentGet2$Response(params, context).pipe(
      map((r: StrictHttpResponse<EntityModelUserAssignment>): EntityModelUserAssignment => r.body)
    );
  }

  /** Path part for operation `executeSearchUserassignmentGet3()` */
  static readonly ExecuteSearchUserassignmentGet3Path = '/userAssignments/search/countByUserIdAndStatusesAndPerspectives';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `executeSearchUserassignmentGet3()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet3$Response(params?: ExecuteSearchUserassignmentGet3$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelUserAssignment>> {
    return executeSearchUserassignmentGet3(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `executeSearchUserassignmentGet3$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet3(params?: ExecuteSearchUserassignmentGet3$Params, context?: HttpContext): Observable<EntityModelUserAssignment> {
    return this.executeSearchUserassignmentGet3$Response(params, context).pipe(
      map((r: StrictHttpResponse<EntityModelUserAssignment>): EntityModelUserAssignment => r.body)
    );
  }

  /** Path part for operation `executeSearchUserassignmentGet4()` */
  static readonly ExecuteSearchUserassignmentGet4Path = '/userAssignments/search/countUniqueBusinessCaseIdsForGivenUserIdAndStatusIn';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `executeSearchUserassignmentGet4()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet4$Response(params?: ExecuteSearchUserassignmentGet4$Params, context?: HttpContext): Observable<StrictHttpResponse<number>> {
    return executeSearchUserassignmentGet4(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `executeSearchUserassignmentGet4$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet4(params?: ExecuteSearchUserassignmentGet4$Params, context?: HttpContext): Observable<number> {
    return this.executeSearchUserassignmentGet4$Response(params, context).pipe(
      map((r: StrictHttpResponse<number>): number => r.body)
    );
  }

  /** Path part for operation `executeSearchUserassignmentGet5()` */
  static readonly ExecuteSearchUserassignmentGet5Path = '/userAssignments/search/countUniqueBusinessCaseIdsForGivenUserIdAndStatusInWithSearch';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `executeSearchUserassignmentGet5()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet5$Response(params?: ExecuteSearchUserassignmentGet5$Params, context?: HttpContext): Observable<StrictHttpResponse<CollectionModelEntityModelUserAssignment>> {
    return executeSearchUserassignmentGet5(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `executeSearchUserassignmentGet5$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet5(params?: ExecuteSearchUserassignmentGet5$Params, context?: HttpContext): Observable<CollectionModelEntityModelUserAssignment> {
    return this.executeSearchUserassignmentGet5$Response(params, context).pipe(
      map((r: StrictHttpResponse<CollectionModelEntityModelUserAssignment>): CollectionModelEntityModelUserAssignment => r.body)
    );
  }

  /** Path part for operation `executeSearchUserassignmentGet6()` */
  static readonly ExecuteSearchUserassignmentGet6Path = '/userAssignments/search/findAllByAssigneeUserIdAndStatusInAndBusinessCaseId';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `executeSearchUserassignmentGet6()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet6$Response(params?: ExecuteSearchUserassignmentGet6$Params, context?: HttpContext): Observable<StrictHttpResponse<PagedModelEntityModelUserAssignment>> {
    return executeSearchUserassignmentGet6(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `executeSearchUserassignmentGet6$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet6(params?: ExecuteSearchUserassignmentGet6$Params, context?: HttpContext): Observable<PagedModelEntityModelUserAssignment> {
    return this.executeSearchUserassignmentGet6$Response(params, context).pipe(
      map((r: StrictHttpResponse<PagedModelEntityModelUserAssignment>): PagedModelEntityModelUserAssignment => r.body)
    );
  }

  /** Path part for operation `executeSearchUserassignmentGet7()` */
  static readonly ExecuteSearchUserassignmentGet7Path = '/userAssignments/search/findAllByAssigneeUserIdAndStatusInAndBusinessCaseIdWithSearch';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `executeSearchUserassignmentGet7()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet7$Response(params?: ExecuteSearchUserassignmentGet7$Params, context?: HttpContext): Observable<StrictHttpResponse<PagedModelEntityModelUserAssignment>> {
    return executeSearchUserassignmentGet7(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `executeSearchUserassignmentGet7$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet7(params?: ExecuteSearchUserassignmentGet7$Params, context?: HttpContext): Observable<PagedModelEntityModelUserAssignment> {
    return this.executeSearchUserassignmentGet7$Response(params, context).pipe(
      map((r: StrictHttpResponse<PagedModelEntityModelUserAssignment>): PagedModelEntityModelUserAssignment => r.body)
    );
  }

  /** Path part for operation `executeSearchUserassignmentGet8()` */
  static readonly ExecuteSearchUserassignmentGet8Path = '/userAssignments/search/findAllByAssigneeUserIdAndStatusPendingWithPagination';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `executeSearchUserassignmentGet8()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet8$Response(params?: ExecuteSearchUserassignmentGet8$Params, context?: HttpContext): Observable<StrictHttpResponse<CollectionModelEntityModelUserAssignment>> {
    return executeSearchUserassignmentGet8(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `executeSearchUserassignmentGet8$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet8(params?: ExecuteSearchUserassignmentGet8$Params, context?: HttpContext): Observable<CollectionModelEntityModelUserAssignment> {
    return this.executeSearchUserassignmentGet8$Response(params, context).pipe(
      map((r: StrictHttpResponse<CollectionModelEntityModelUserAssignment>): CollectionModelEntityModelUserAssignment => r.body)
    );
  }

  /** Path part for operation `executeSearchUserassignmentGet9()` */
  static readonly ExecuteSearchUserassignmentGet9Path = '/userAssignments/search/findUserAssignmentByCreationUserIdAndAssigneeUserIdAndReferenceRecordId';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `executeSearchUserassignmentGet9()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet9$Response(params?: ExecuteSearchUserassignmentGet9$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelUserAssignment>> {
    return executeSearchUserassignmentGet9(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `executeSearchUserassignmentGet9$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet9(params?: ExecuteSearchUserassignmentGet9$Params, context?: HttpContext): Observable<EntityModelUserAssignment> {
    return this.executeSearchUserassignmentGet9$Response(params, context).pipe(
      map((r: StrictHttpResponse<EntityModelUserAssignment>): EntityModelUserAssignment => r.body)
    );
  }

  /** Path part for operation `executeSearchUserassignmentGet10()` */
  static readonly ExecuteSearchUserassignmentGet10Path = '/userAssignments/search/findUserAssignmentsByUserIdAndStatusAndSortByLastUpdatedDateAndGroupByBusinessCaseWithPagination';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `executeSearchUserassignmentGet10()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet10$Response(params?: ExecuteSearchUserassignmentGet10$Params, context?: HttpContext): Observable<StrictHttpResponse<CollectionModelEntityModelUserAssignment>> {
    return executeSearchUserassignmentGet10(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `executeSearchUserassignmentGet10$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet10(params?: ExecuteSearchUserassignmentGet10$Params, context?: HttpContext): Observable<CollectionModelEntityModelUserAssignment> {
    return this.executeSearchUserassignmentGet10$Response(params, context).pipe(
      map((r: StrictHttpResponse<CollectionModelEntityModelUserAssignment>): CollectionModelEntityModelUserAssignment => r.body)
    );
  }

  /** Path part for operation `executeSearchUserassignmentGet11()` */
  static readonly ExecuteSearchUserassignmentGet11Path = '/userAssignments/search/findUserAssignmentsByUserIdAndStatusAndSortByLastUpdatedDateAndGroupByBusinessCaseWithPaginationWithSearch';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `executeSearchUserassignmentGet11()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet11$Response(params?: ExecuteSearchUserassignmentGet11$Params, context?: HttpContext): Observable<StrictHttpResponse<CollectionModelEntityModelUserAssignment>> {
    return executeSearchUserassignmentGet11(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `executeSearchUserassignmentGet11$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchUserassignmentGet11(params?: ExecuteSearchUserassignmentGet11$Params, context?: HttpContext): Observable<CollectionModelEntityModelUserAssignment> {
    return this.executeSearchUserassignmentGet11$Response(params, context).pipe(
      map((r: StrictHttpResponse<CollectionModelEntityModelUserAssignment>): CollectionModelEntityModelUserAssignment => r.body)
    );
  }

}
