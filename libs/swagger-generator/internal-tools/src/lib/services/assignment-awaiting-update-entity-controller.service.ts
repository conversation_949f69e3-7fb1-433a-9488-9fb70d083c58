/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { deleteItemResourceAssignmentawaitingupdateDelete } from '../fn/assignment-awaiting-update-entity-controller/delete-item-resource-assignmentawaitingupdate-delete';
import { DeleteItemResourceAssignmentawaitingupdateDelete$Params } from '../fn/assignment-awaiting-update-entity-controller/delete-item-resource-assignmentawaitingupdate-delete';
import { EntityModelAssignmentAwaitingUpdate } from '../models/entity-model-assignment-awaiting-update';
import { getCollectionResourceAssignmentawaitingupdateGet1$Json } from '../fn/assignment-awaiting-update-entity-controller/get-collection-resource-assignmentawaitingupdate-get-1-json';
import { GetCollectionResourceAssignmentawaitingupdateGet1$Json$Params } from '../fn/assignment-awaiting-update-entity-controller/get-collection-resource-assignmentawaitingupdate-get-1-json';
import { getCollectionResourceAssignmentawaitingupdateGet1$UriList } from '../fn/assignment-awaiting-update-entity-controller/get-collection-resource-assignmentawaitingupdate-get-1-uri-list';
import { GetCollectionResourceAssignmentawaitingupdateGet1$UriList$Params } from '../fn/assignment-awaiting-update-entity-controller/get-collection-resource-assignmentawaitingupdate-get-1-uri-list';
import { getItemResourceAssignmentawaitingupdateGet } from '../fn/assignment-awaiting-update-entity-controller/get-item-resource-assignmentawaitingupdate-get';
import { GetItemResourceAssignmentawaitingupdateGet$Params } from '../fn/assignment-awaiting-update-entity-controller/get-item-resource-assignmentawaitingupdate-get';
import { PagedModelEntityModelAssignmentAwaitingUpdate } from '../models/paged-model-entity-model-assignment-awaiting-update';
import { patchItemResourceAssignmentawaitingupdatePatch } from '../fn/assignment-awaiting-update-entity-controller/patch-item-resource-assignmentawaitingupdate-patch';
import { PatchItemResourceAssignmentawaitingupdatePatch$Params } from '../fn/assignment-awaiting-update-entity-controller/patch-item-resource-assignmentawaitingupdate-patch';
import { postCollectionResourceAssignmentawaitingupdatePost } from '../fn/assignment-awaiting-update-entity-controller/post-collection-resource-assignmentawaitingupdate-post';
import { PostCollectionResourceAssignmentawaitingupdatePost$Params } from '../fn/assignment-awaiting-update-entity-controller/post-collection-resource-assignmentawaitingupdate-post';
import { putItemResourceAssignmentawaitingupdatePut } from '../fn/assignment-awaiting-update-entity-controller/put-item-resource-assignmentawaitingupdate-put';
import { PutItemResourceAssignmentawaitingupdatePut$Params } from '../fn/assignment-awaiting-update-entity-controller/put-item-resource-assignmentawaitingupdate-put';

@Injectable({ providedIn: 'root' })
export class AssignmentAwaitingUpdateEntityControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getCollectionResourceAssignmentawaitingupdateGet1()` */
  static readonly GetCollectionResourceAssignmentawaitingupdateGet1Path = '/assignmentAwaitingUpdates';

  /**
   * get-assignmentawaitingupdate
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getCollectionResourceAssignmentawaitingupdateGet1$Json()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCollectionResourceAssignmentawaitingupdateGet1$Json$Response(params?: GetCollectionResourceAssignmentawaitingupdateGet1$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<PagedModelEntityModelAssignmentAwaitingUpdate>> {
    return getCollectionResourceAssignmentawaitingupdateGet1$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * get-assignmentawaitingupdate
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getCollectionResourceAssignmentawaitingupdateGet1$Json$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCollectionResourceAssignmentawaitingupdateGet1$Json(params?: GetCollectionResourceAssignmentawaitingupdateGet1$Json$Params, context?: HttpContext): Observable<PagedModelEntityModelAssignmentAwaitingUpdate> {
    return this.getCollectionResourceAssignmentawaitingupdateGet1$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<PagedModelEntityModelAssignmentAwaitingUpdate>): PagedModelEntityModelAssignmentAwaitingUpdate => r.body)
    );
  }

  /**
   * get-assignmentawaitingupdate
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getCollectionResourceAssignmentawaitingupdateGet1$UriList()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCollectionResourceAssignmentawaitingupdateGet1$UriList$Response(params?: GetCollectionResourceAssignmentawaitingupdateGet1$UriList$Params, context?: HttpContext): Observable<StrictHttpResponse<string>> {
    return getCollectionResourceAssignmentawaitingupdateGet1$UriList(this.http, this.rootUrl, params, context);
  }

  /**
   * get-assignmentawaitingupdate
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getCollectionResourceAssignmentawaitingupdateGet1$UriList$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCollectionResourceAssignmentawaitingupdateGet1$UriList(params?: GetCollectionResourceAssignmentawaitingupdateGet1$UriList$Params, context?: HttpContext): Observable<string> {
    return this.getCollectionResourceAssignmentawaitingupdateGet1$UriList$Response(params, context).pipe(
      map((r: StrictHttpResponse<string>): string => r.body)
    );
  }

  /** Path part for operation `postCollectionResourceAssignmentawaitingupdatePost()` */
  static readonly PostCollectionResourceAssignmentawaitingupdatePostPath = '/assignmentAwaitingUpdates';

  /**
   * create-assignmentawaitingupdate
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `postCollectionResourceAssignmentawaitingupdatePost()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  postCollectionResourceAssignmentawaitingupdatePost$Response(params: PostCollectionResourceAssignmentawaitingupdatePost$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelAssignmentAwaitingUpdate>> {
    return postCollectionResourceAssignmentawaitingupdatePost(this.http, this.rootUrl, params, context);
  }

  /**
   * create-assignmentawaitingupdate
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `postCollectionResourceAssignmentawaitingupdatePost$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  postCollectionResourceAssignmentawaitingupdatePost(params: PostCollectionResourceAssignmentawaitingupdatePost$Params, context?: HttpContext): Observable<EntityModelAssignmentAwaitingUpdate> {
    return this.postCollectionResourceAssignmentawaitingupdatePost$Response(params, context).pipe(
      map((r: StrictHttpResponse<EntityModelAssignmentAwaitingUpdate>): EntityModelAssignmentAwaitingUpdate => r.body)
    );
  }

  /** Path part for operation `getItemResourceAssignmentawaitingupdateGet()` */
  static readonly GetItemResourceAssignmentawaitingupdateGetPath = '/assignmentAwaitingUpdates/{id}';

  /**
   * get-assignmentawaitingupdate
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getItemResourceAssignmentawaitingupdateGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  getItemResourceAssignmentawaitingupdateGet$Response(params: GetItemResourceAssignmentawaitingupdateGet$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelAssignmentAwaitingUpdate>> {
    return getItemResourceAssignmentawaitingupdateGet(this.http, this.rootUrl, params, context);
  }

  /**
   * get-assignmentawaitingupdate
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getItemResourceAssignmentawaitingupdateGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getItemResourceAssignmentawaitingupdateGet(params: GetItemResourceAssignmentawaitingupdateGet$Params, context?: HttpContext): Observable<EntityModelAssignmentAwaitingUpdate> {
    return this.getItemResourceAssignmentawaitingupdateGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<EntityModelAssignmentAwaitingUpdate>): EntityModelAssignmentAwaitingUpdate => r.body)
    );
  }

  /** Path part for operation `putItemResourceAssignmentawaitingupdatePut()` */
  static readonly PutItemResourceAssignmentawaitingupdatePutPath = '/assignmentAwaitingUpdates/{id}';

  /**
   * update-assignmentawaitingupdate
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `putItemResourceAssignmentawaitingupdatePut()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  putItemResourceAssignmentawaitingupdatePut$Response(params: PutItemResourceAssignmentawaitingupdatePut$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelAssignmentAwaitingUpdate>> {
    return putItemResourceAssignmentawaitingupdatePut(this.http, this.rootUrl, params, context);
  }

  /**
   * update-assignmentawaitingupdate
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `putItemResourceAssignmentawaitingupdatePut$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  putItemResourceAssignmentawaitingupdatePut(params: PutItemResourceAssignmentawaitingupdatePut$Params, context?: HttpContext): Observable<EntityModelAssignmentAwaitingUpdate> {
    return this.putItemResourceAssignmentawaitingupdatePut$Response(params, context).pipe(
      map((r: StrictHttpResponse<EntityModelAssignmentAwaitingUpdate>): EntityModelAssignmentAwaitingUpdate => r.body)
    );
  }

  /** Path part for operation `deleteItemResourceAssignmentawaitingupdateDelete()` */
  static readonly DeleteItemResourceAssignmentawaitingupdateDeletePath = '/assignmentAwaitingUpdates/{id}';

  /**
   * delete-assignmentawaitingupdate
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteItemResourceAssignmentawaitingupdateDelete()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteItemResourceAssignmentawaitingupdateDelete$Response(params: DeleteItemResourceAssignmentawaitingupdateDelete$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return deleteItemResourceAssignmentawaitingupdateDelete(this.http, this.rootUrl, params, context);
  }

  /**
   * delete-assignmentawaitingupdate
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteItemResourceAssignmentawaitingupdateDelete$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteItemResourceAssignmentawaitingupdateDelete(params: DeleteItemResourceAssignmentawaitingupdateDelete$Params, context?: HttpContext): Observable<void> {
    return this.deleteItemResourceAssignmentawaitingupdateDelete$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `patchItemResourceAssignmentawaitingupdatePatch()` */
  static readonly PatchItemResourceAssignmentawaitingupdatePatchPath = '/assignmentAwaitingUpdates/{id}';

  /**
   * patch-assignmentawaitingupdate
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `patchItemResourceAssignmentawaitingupdatePatch()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  patchItemResourceAssignmentawaitingupdatePatch$Response(params: PatchItemResourceAssignmentawaitingupdatePatch$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelAssignmentAwaitingUpdate>> {
    return patchItemResourceAssignmentawaitingupdatePatch(this.http, this.rootUrl, params, context);
  }

  /**
   * patch-assignmentawaitingupdate
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `patchItemResourceAssignmentawaitingupdatePatch$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  patchItemResourceAssignmentawaitingupdatePatch(params: PatchItemResourceAssignmentawaitingupdatePatch$Params, context?: HttpContext): Observable<EntityModelAssignmentAwaitingUpdate> {
    return this.patchItemResourceAssignmentawaitingupdatePatch$Response(params, context).pipe(
      map((r: StrictHttpResponse<EntityModelAssignmentAwaitingUpdate>): EntityModelAssignmentAwaitingUpdate => r.body)
    );
  }

}
