/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { deleteItemResourceUserassignmentDelete } from '../fn/user-assignment-entity-controller/delete-item-resource-userassignment-delete';
import { DeleteItemResourceUserassignmentDelete$Params } from '../fn/user-assignment-entity-controller/delete-item-resource-userassignment-delete';
import { EntityModelUserAssignment } from '../models/entity-model-user-assignment';
import { getCollectionResourceUserassignmentGet1$Json } from '../fn/user-assignment-entity-controller/get-collection-resource-userassignment-get-1-json';
import { GetCollectionResourceUserassignmentGet1$Json$Params } from '../fn/user-assignment-entity-controller/get-collection-resource-userassignment-get-1-json';
import { getCollectionResourceUserassignmentGet1$UriList } from '../fn/user-assignment-entity-controller/get-collection-resource-userassignment-get-1-uri-list';
import { GetCollectionResourceUserassignmentGet1$UriList$Params } from '../fn/user-assignment-entity-controller/get-collection-resource-userassignment-get-1-uri-list';
import { getItemResourceUserassignmentGet } from '../fn/user-assignment-entity-controller/get-item-resource-userassignment-get';
import { GetItemResourceUserassignmentGet$Params } from '../fn/user-assignment-entity-controller/get-item-resource-userassignment-get';
import { PagedModelEntityModelUserAssignment } from '../models/paged-model-entity-model-user-assignment';
import { patchItemResourceUserassignmentPatch } from '../fn/user-assignment-entity-controller/patch-item-resource-userassignment-patch';
import { PatchItemResourceUserassignmentPatch$Params } from '../fn/user-assignment-entity-controller/patch-item-resource-userassignment-patch';
import { postCollectionResourceUserassignmentPost } from '../fn/user-assignment-entity-controller/post-collection-resource-userassignment-post';
import { PostCollectionResourceUserassignmentPost$Params } from '../fn/user-assignment-entity-controller/post-collection-resource-userassignment-post';
import { putItemResourceUserassignmentPut } from '../fn/user-assignment-entity-controller/put-item-resource-userassignment-put';
import { PutItemResourceUserassignmentPut$Params } from '../fn/user-assignment-entity-controller/put-item-resource-userassignment-put';

@Injectable({ providedIn: 'root' })
export class UserAssignmentEntityControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getCollectionResourceUserassignmentGet1()` */
  static readonly GetCollectionResourceUserassignmentGet1Path = '/userAssignments';

  /**
   * get-userassignment
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getCollectionResourceUserassignmentGet1$Json()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCollectionResourceUserassignmentGet1$Json$Response(params?: GetCollectionResourceUserassignmentGet1$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<PagedModelEntityModelUserAssignment>> {
    return getCollectionResourceUserassignmentGet1$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * get-userassignment
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getCollectionResourceUserassignmentGet1$Json$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCollectionResourceUserassignmentGet1$Json(params?: GetCollectionResourceUserassignmentGet1$Json$Params, context?: HttpContext): Observable<PagedModelEntityModelUserAssignment> {
    return this.getCollectionResourceUserassignmentGet1$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<PagedModelEntityModelUserAssignment>): PagedModelEntityModelUserAssignment => r.body)
    );
  }

  /**
   * get-userassignment
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getCollectionResourceUserassignmentGet1$UriList()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCollectionResourceUserassignmentGet1$UriList$Response(params?: GetCollectionResourceUserassignmentGet1$UriList$Params, context?: HttpContext): Observable<StrictHttpResponse<string>> {
    return getCollectionResourceUserassignmentGet1$UriList(this.http, this.rootUrl, params, context);
  }

  /**
   * get-userassignment
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getCollectionResourceUserassignmentGet1$UriList$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCollectionResourceUserassignmentGet1$UriList(params?: GetCollectionResourceUserassignmentGet1$UriList$Params, context?: HttpContext): Observable<string> {
    return this.getCollectionResourceUserassignmentGet1$UriList$Response(params, context).pipe(
      map((r: StrictHttpResponse<string>): string => r.body)
    );
  }

  /** Path part for operation `postCollectionResourceUserassignmentPost()` */
  static readonly PostCollectionResourceUserassignmentPostPath = '/userAssignments';

  /**
   * create-userassignment
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `postCollectionResourceUserassignmentPost()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  postCollectionResourceUserassignmentPost$Response(params: PostCollectionResourceUserassignmentPost$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelUserAssignment>> {
    return postCollectionResourceUserassignmentPost(this.http, this.rootUrl, params, context);
  }

  /**
   * create-userassignment
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `postCollectionResourceUserassignmentPost$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  postCollectionResourceUserassignmentPost(params: PostCollectionResourceUserassignmentPost$Params, context?: HttpContext): Observable<EntityModelUserAssignment> {
    return this.postCollectionResourceUserassignmentPost$Response(params, context).pipe(
      map((r: StrictHttpResponse<EntityModelUserAssignment>): EntityModelUserAssignment => r.body)
    );
  }

  /** Path part for operation `getItemResourceUserassignmentGet()` */
  static readonly GetItemResourceUserassignmentGetPath = '/userAssignments/{id}';

  /**
   * get-userassignment
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getItemResourceUserassignmentGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  getItemResourceUserassignmentGet$Response(params: GetItemResourceUserassignmentGet$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelUserAssignment>> {
    return getItemResourceUserassignmentGet(this.http, this.rootUrl, params, context);
  }

  /**
   * get-userassignment
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getItemResourceUserassignmentGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getItemResourceUserassignmentGet(params: GetItemResourceUserassignmentGet$Params, context?: HttpContext): Observable<EntityModelUserAssignment> {
    return this.getItemResourceUserassignmentGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<EntityModelUserAssignment>): EntityModelUserAssignment => r.body)
    );
  }

  /** Path part for operation `putItemResourceUserassignmentPut()` */
  static readonly PutItemResourceUserassignmentPutPath = '/userAssignments/{id}';

  /**
   * update-userassignment
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `putItemResourceUserassignmentPut()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  putItemResourceUserassignmentPut$Response(params: PutItemResourceUserassignmentPut$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelUserAssignment>> {
    return putItemResourceUserassignmentPut(this.http, this.rootUrl, params, context);
  }

  /**
   * update-userassignment
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `putItemResourceUserassignmentPut$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  putItemResourceUserassignmentPut(params: PutItemResourceUserassignmentPut$Params, context?: HttpContext): Observable<EntityModelUserAssignment> {
    return this.putItemResourceUserassignmentPut$Response(params, context).pipe(
      map((r: StrictHttpResponse<EntityModelUserAssignment>): EntityModelUserAssignment => r.body)
    );
  }

  /** Path part for operation `deleteItemResourceUserassignmentDelete()` */
  static readonly DeleteItemResourceUserassignmentDeletePath = '/userAssignments/{id}';

  /**
   * delete-userassignment
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteItemResourceUserassignmentDelete()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteItemResourceUserassignmentDelete$Response(params: DeleteItemResourceUserassignmentDelete$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return deleteItemResourceUserassignmentDelete(this.http, this.rootUrl, params, context);
  }

  /**
   * delete-userassignment
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteItemResourceUserassignmentDelete$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteItemResourceUserassignmentDelete(params: DeleteItemResourceUserassignmentDelete$Params, context?: HttpContext): Observable<void> {
    return this.deleteItemResourceUserassignmentDelete$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `patchItemResourceUserassignmentPatch()` */
  static readonly PatchItemResourceUserassignmentPatchPath = '/userAssignments/{id}';

  /**
   * patch-userassignment
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `patchItemResourceUserassignmentPatch()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  patchItemResourceUserassignmentPatch$Response(params: PatchItemResourceUserassignmentPatch$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelUserAssignment>> {
    return patchItemResourceUserassignmentPatch(this.http, this.rootUrl, params, context);
  }

  /**
   * patch-userassignment
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `patchItemResourceUserassignmentPatch$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  patchItemResourceUserassignmentPatch(params: PatchItemResourceUserassignmentPatch$Params, context?: HttpContext): Observable<EntityModelUserAssignment> {
    return this.patchItemResourceUserassignmentPatch$Response(params, context).pipe(
      map((r: StrictHttpResponse<EntityModelUserAssignment>): EntityModelUserAssignment => r.body)
    );
  }

}
