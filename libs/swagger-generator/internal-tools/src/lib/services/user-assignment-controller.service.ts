/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { AssignmentsByStatusesCount } from '../models/assignments-by-statuses-count';
import { CountByPerspectivesResponse } from '../models/count-by-perspectives-response';
import { getAssignmentCountByUserIdAndStatus } from '../fn/user-assignment-controller/get-assignment-count-by-user-id-and-status';
import { GetAssignmentCountByUserIdAndStatus$Params } from '../fn/user-assignment-controller/get-assignment-count-by-user-id-and-status';
import { getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary } from '../fn/user-assignment-controller/get-assignment-count-by-user-id-and-statuses-and-perspectives-for-summary';
import { GetAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary$Params } from '../fn/user-assignment-controller/get-assignment-count-by-user-id-and-statuses-and-perspectives-for-summary';
import { getBusinessCasesInfoByUserAssignmentLastUpdatedDate } from '../fn/user-assignment-controller/get-business-cases-info-by-user-assignment-last-updated-date';
import { GetBusinessCasesInfoByUserAssignmentLastUpdatedDate$Params } from '../fn/user-assignment-controller/get-business-cases-info-by-user-assignment-last-updated-date';
import { getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary } from '../fn/user-assignment-controller/get-user-assignments-by-user-id-and-business-case-ids-for-summary';
import { GetUserAssignmentsByUserIdAndBusinessCaseIdsForSummary$Params } from '../fn/user-assignment-controller/get-user-assignments-by-user-id-and-business-case-ids-for-summary';
import { getUserAssignmentsForUserIdAndBusinessCaseId } from '../fn/user-assignment-controller/get-user-assignments-for-user-id-and-business-case-id';
import { GetUserAssignmentsForUserIdAndBusinessCaseId$Params } from '../fn/user-assignment-controller/get-user-assignments-for-user-id-and-business-case-id';
import { PageUserAssignment } from '../models/page-user-assignment';
import { UserAssignment } from '../models/user-assignment';
import { UserAssignmentResponse } from '../models/user-assignment-response';

@Injectable({ providedIn: 'root' })
export class UserAssignmentControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getUserAssignmentsForUserIdAndBusinessCaseId()` */
  static readonly GetUserAssignmentsForUserIdAndBusinessCaseIdPath = '/user-assignments/user';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getUserAssignmentsForUserIdAndBusinessCaseId()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserAssignmentsForUserIdAndBusinessCaseId$Response(params: GetUserAssignmentsForUserIdAndBusinessCaseId$Params, context?: HttpContext): Observable<StrictHttpResponse<PageUserAssignment>> {
    return getUserAssignmentsForUserIdAndBusinessCaseId(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getUserAssignmentsForUserIdAndBusinessCaseId$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserAssignmentsForUserIdAndBusinessCaseId(params: GetUserAssignmentsForUserIdAndBusinessCaseId$Params, context?: HttpContext): Observable<PageUserAssignment> {
    return this.getUserAssignmentsForUserIdAndBusinessCaseId$Response(params, context).pipe(
      map((r: StrictHttpResponse<PageUserAssignment>): PageUserAssignment => r.body)
    );
  }

  /** Path part for operation `getAssignmentCountByUserIdAndStatus()` */
  static readonly GetAssignmentCountByUserIdAndStatusPath = '/user-assignments/user/count/assignments-by-statues';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAssignmentCountByUserIdAndStatus()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAssignmentCountByUserIdAndStatus$Response(params: GetAssignmentCountByUserIdAndStatus$Params, context?: HttpContext): Observable<StrictHttpResponse<AssignmentsByStatusesCount>> {
    return getAssignmentCountByUserIdAndStatus(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAssignmentCountByUserIdAndStatus$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAssignmentCountByUserIdAndStatus(params: GetAssignmentCountByUserIdAndStatus$Params, context?: HttpContext): Observable<AssignmentsByStatusesCount> {
    return this.getAssignmentCountByUserIdAndStatus$Response(params, context).pipe(
      map((r: StrictHttpResponse<AssignmentsByStatusesCount>): AssignmentsByStatusesCount => r.body)
    );
  }

  /** Path part for operation `getBusinessCasesInfoByUserAssignmentLastUpdatedDate()` */
  static readonly GetBusinessCasesInfoByUserAssignmentLastUpdatedDatePath = '/user-assignments/user/business-cases';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getBusinessCasesInfoByUserAssignmentLastUpdatedDate()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCasesInfoByUserAssignmentLastUpdatedDate$Response(params: GetBusinessCasesInfoByUserAssignmentLastUpdatedDate$Params, context?: HttpContext): Observable<StrictHttpResponse<UserAssignmentResponse>> {
    return getBusinessCasesInfoByUserAssignmentLastUpdatedDate(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getBusinessCasesInfoByUserAssignmentLastUpdatedDate$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCasesInfoByUserAssignmentLastUpdatedDate(params: GetBusinessCasesInfoByUserAssignmentLastUpdatedDate$Params, context?: HttpContext): Observable<UserAssignmentResponse> {
    return this.getBusinessCasesInfoByUserAssignmentLastUpdatedDate$Response(params, context).pipe(
      map((r: StrictHttpResponse<UserAssignmentResponse>): UserAssignmentResponse => r.body)
    );
  }

  /** Path part for operation `getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary()` */
  static readonly GetUserAssignmentsByUserIdAndBusinessCaseIdsForSummaryPath = '/user-assignments/summary/user';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary$Response(params: GetUserAssignmentsByUserIdAndBusinessCaseIdsForSummary$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<UserAssignment>>> {
    return getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary(params: GetUserAssignmentsByUserIdAndBusinessCaseIdsForSummary$Params, context?: HttpContext): Observable<Array<UserAssignment>> {
    return this.getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<UserAssignment>>): Array<UserAssignment> => r.body)
    );
  }

  /** Path part for operation `getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary()` */
  static readonly GetAssignmentCountByUserIdAndStatusesAndPerspectivesForSummaryPath = '/user-assignments/summary/user/count/assignments-by-perspectives';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary$Response(params: GetAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary$Params, context?: HttpContext): Observable<StrictHttpResponse<CountByPerspectivesResponse>> {
    return getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary(params: GetAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary$Params, context?: HttpContext): Observable<CountByPerspectivesResponse> {
    return this.getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary$Response(params, context).pipe(
      map((r: StrictHttpResponse<CountByPerspectivesResponse>): CountByPerspectivesResponse => r.body)
    );
  }

}
