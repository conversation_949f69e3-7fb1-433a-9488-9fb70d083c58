/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { AssignmentsByStatusesCount } from '../models/assignments-by-statuses-count';
import { CountByPerspectivesResponse } from '../models/count-by-perspectives-response';
import { getAssignmentCountByUserIdAndStatus1 } from '../fn/internal-controller/get-assignment-count-by-user-id-and-status-1';
import { GetAssignmentCountByUserIdAndStatus1$Params } from '../fn/internal-controller/get-assignment-count-by-user-id-and-status-1';
import { getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary1 } from '../fn/internal-controller/get-assignment-count-by-user-id-and-statuses-and-perspectives-for-summary-1';
import { GetAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary1$Params } from '../fn/internal-controller/get-assignment-count-by-user-id-and-statuses-and-perspectives-for-summary-1';
import { getBusinessCasesInfoByUserAssignmentLastUpdatedDate1 } from '../fn/internal-controller/get-business-cases-info-by-user-assignment-last-updated-date-1';
import { GetBusinessCasesInfoByUserAssignmentLastUpdatedDate1$Params } from '../fn/internal-controller/get-business-cases-info-by-user-assignment-last-updated-date-1';
import { getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary1 } from '../fn/internal-controller/get-user-assignments-by-user-id-and-business-case-ids-for-summary-1';
import { GetUserAssignmentsByUserIdAndBusinessCaseIdsForSummary1$Params } from '../fn/internal-controller/get-user-assignments-by-user-id-and-business-case-ids-for-summary-1';
import { getUserAssignmentsForUserIdAndBusinessCaseId1 } from '../fn/internal-controller/get-user-assignments-for-user-id-and-business-case-id-1';
import { GetUserAssignmentsForUserIdAndBusinessCaseId1$Params } from '../fn/internal-controller/get-user-assignments-for-user-id-and-business-case-id-1';
import { PageUserAssignment } from '../models/page-user-assignment';
import { UserAssignment } from '../models/user-assignment';
import { UserAssignmentResponse } from '../models/user-assignment-response';

@Injectable({ providedIn: 'root' })
export class InternalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getUserAssignmentsForUserIdAndBusinessCaseId1()` */
  static readonly GetUserAssignmentsForUserIdAndBusinessCaseId1Path = '/internal/user-assignments/user/{userId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getUserAssignmentsForUserIdAndBusinessCaseId1()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserAssignmentsForUserIdAndBusinessCaseId1$Response(params: GetUserAssignmentsForUserIdAndBusinessCaseId1$Params, context?: HttpContext): Observable<StrictHttpResponse<PageUserAssignment>> {
    return getUserAssignmentsForUserIdAndBusinessCaseId1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getUserAssignmentsForUserIdAndBusinessCaseId1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserAssignmentsForUserIdAndBusinessCaseId1(params: GetUserAssignmentsForUserIdAndBusinessCaseId1$Params, context?: HttpContext): Observable<PageUserAssignment> {
    return this.getUserAssignmentsForUserIdAndBusinessCaseId1$Response(params, context).pipe(
      map((r: StrictHttpResponse<PageUserAssignment>): PageUserAssignment => r.body)
    );
  }

  /** Path part for operation `getAssignmentCountByUserIdAndStatus1()` */
  static readonly GetAssignmentCountByUserIdAndStatus1Path = '/internal/user-assignments/user/{userId}/count/assignments-by-statues';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAssignmentCountByUserIdAndStatus1()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAssignmentCountByUserIdAndStatus1$Response(params: GetAssignmentCountByUserIdAndStatus1$Params, context?: HttpContext): Observable<StrictHttpResponse<AssignmentsByStatusesCount>> {
    return getAssignmentCountByUserIdAndStatus1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAssignmentCountByUserIdAndStatus1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAssignmentCountByUserIdAndStatus1(params: GetAssignmentCountByUserIdAndStatus1$Params, context?: HttpContext): Observable<AssignmentsByStatusesCount> {
    return this.getAssignmentCountByUserIdAndStatus1$Response(params, context).pipe(
      map((r: StrictHttpResponse<AssignmentsByStatusesCount>): AssignmentsByStatusesCount => r.body)
    );
  }

  /** Path part for operation `getBusinessCasesInfoByUserAssignmentLastUpdatedDate1()` */
  static readonly GetBusinessCasesInfoByUserAssignmentLastUpdatedDate1Path = '/internal/user-assignments/user/{userId}/business-cases';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getBusinessCasesInfoByUserAssignmentLastUpdatedDate1()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCasesInfoByUserAssignmentLastUpdatedDate1$Response(params: GetBusinessCasesInfoByUserAssignmentLastUpdatedDate1$Params, context?: HttpContext): Observable<StrictHttpResponse<UserAssignmentResponse>> {
    return getBusinessCasesInfoByUserAssignmentLastUpdatedDate1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getBusinessCasesInfoByUserAssignmentLastUpdatedDate1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCasesInfoByUserAssignmentLastUpdatedDate1(params: GetBusinessCasesInfoByUserAssignmentLastUpdatedDate1$Params, context?: HttpContext): Observable<UserAssignmentResponse> {
    return this.getBusinessCasesInfoByUserAssignmentLastUpdatedDate1$Response(params, context).pipe(
      map((r: StrictHttpResponse<UserAssignmentResponse>): UserAssignmentResponse => r.body)
    );
  }

  /** Path part for operation `getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary1()` */
  static readonly GetUserAssignmentsByUserIdAndBusinessCaseIdsForSummary1Path = '/internal/user-assignments/summary/user/{userId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary1()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary1$Response(params: GetUserAssignmentsByUserIdAndBusinessCaseIdsForSummary1$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<UserAssignment>>> {
    return getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary1(params: GetUserAssignmentsByUserIdAndBusinessCaseIdsForSummary1$Params, context?: HttpContext): Observable<Array<UserAssignment>> {
    return this.getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary1$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<UserAssignment>>): Array<UserAssignment> => r.body)
    );
  }

  /** Path part for operation `getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary1()` */
  static readonly GetAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary1Path = '/internal/user-assignments/summary/user/{userId}/count/assignments-by-perspectives';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary1()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary1$Response(params: GetAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary1$Params, context?: HttpContext): Observable<StrictHttpResponse<CountByPerspectivesResponse>> {
    return getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary1(params: GetAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary1$Params, context?: HttpContext): Observable<CountByPerspectivesResponse> {
    return this.getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary1$Response(params, context).pipe(
      map((r: StrictHttpResponse<CountByPerspectivesResponse>): CountByPerspectivesResponse => r.body)
    );
  }

}
