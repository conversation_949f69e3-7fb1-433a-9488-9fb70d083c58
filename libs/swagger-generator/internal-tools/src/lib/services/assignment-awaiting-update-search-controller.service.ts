/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { CollectionModelEntityModelAssignmentAwaitingUpdate } from '../models/collection-model-entity-model-assignment-awaiting-update';
import { EntityModelAssignmentAwaitingUpdate } from '../models/entity-model-assignment-awaiting-update';
import { executeSearchAssignmentawaitingupdateGet } from '../fn/assignment-awaiting-update-search-controller/execute-search-assignmentawaitingupdate-get';
import { ExecuteSearchAssignmentawaitingupdateGet$Params } from '../fn/assignment-awaiting-update-search-controller/execute-search-assignmentawaitingupdate-get';
import { executeSearchAssignmentawaitingupdateGet1 } from '../fn/assignment-awaiting-update-search-controller/execute-search-assignmentawaitingupdate-get-1';
import { ExecuteSearchAssignmentawaitingupdateGet1$Params } from '../fn/assignment-awaiting-update-search-controller/execute-search-assignmentawaitingupdate-get-1';

@Injectable({ providedIn: 'root' })
export class AssignmentAwaitingUpdateSearchControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `executeSearchAssignmentawaitingupdateGet()` */
  static readonly ExecuteSearchAssignmentawaitingupdateGetPath = '/assignmentAwaitingUpdates/search/findAssignmentAwaitingUpdateByCreationDateAfter';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `executeSearchAssignmentawaitingupdateGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchAssignmentawaitingupdateGet$Response(params?: ExecuteSearchAssignmentawaitingupdateGet$Params, context?: HttpContext): Observable<StrictHttpResponse<CollectionModelEntityModelAssignmentAwaitingUpdate>> {
    return executeSearchAssignmentawaitingupdateGet(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `executeSearchAssignmentawaitingupdateGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchAssignmentawaitingupdateGet(params?: ExecuteSearchAssignmentawaitingupdateGet$Params, context?: HttpContext): Observable<CollectionModelEntityModelAssignmentAwaitingUpdate> {
    return this.executeSearchAssignmentawaitingupdateGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<CollectionModelEntityModelAssignmentAwaitingUpdate>): CollectionModelEntityModelAssignmentAwaitingUpdate => r.body)
    );
  }

  /** Path part for operation `executeSearchAssignmentawaitingupdateGet1()` */
  static readonly ExecuteSearchAssignmentawaitingupdateGet1Path = '/assignmentAwaitingUpdates/search/findAssignmentAwaitingUpdateByCreationUserIdAndAssigneeUserIdAndBusinessCaseId';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `executeSearchAssignmentawaitingupdateGet1()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchAssignmentawaitingupdateGet1$Response(params?: ExecuteSearchAssignmentawaitingupdateGet1$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelAssignmentAwaitingUpdate>> {
    return executeSearchAssignmentawaitingupdateGet1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `executeSearchAssignmentawaitingupdateGet1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  executeSearchAssignmentawaitingupdateGet1(params?: ExecuteSearchAssignmentawaitingupdateGet1$Params, context?: HttpContext): Observable<EntityModelAssignmentAwaitingUpdate> {
    return this.executeSearchAssignmentawaitingupdateGet1$Response(params, context).pipe(
      map((r: StrictHttpResponse<EntityModelAssignmentAwaitingUpdate>): EntityModelAssignmentAwaitingUpdate => r.body)
    );
  }

}
