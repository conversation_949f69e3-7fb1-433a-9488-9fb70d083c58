{"name": "swagger-generator", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/swagger-generator/src", "prefix": "lib", "tags": ["lib:swagger-generator"], "projectType": "library", "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/swagger-generator/ng-package.json", "tsConfig": "libs/swagger-generator/tsconfig.lib.json"}, "configurations": {"production": {"tsConfig": "libs/swagger-generator/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/swagger-generator/tsconfig.lib.json"}}, "defaultConfiguration": "production"}}}