/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { editNotificationsForUser } from '../fn/notifications-controller/edit-notifications-for-user';
import { EditNotificationsForUser$Params } from '../fn/notifications-controller/edit-notifications-for-user';
import { getAllNotificationsForUser } from '../fn/notifications-controller/get-all-notifications-for-user';
import { GetAllNotificationsForUser$Params } from '../fn/notifications-controller/get-all-notifications-for-user';
import { getAllUnreadNotificationsForUser } from '../fn/notifications-controller/get-all-unread-notifications-for-user';
import { GetAllUnreadNotificationsForUser$Params } from '../fn/notifications-controller/get-all-unread-notifications-for-user';
import { markAllReadForUser } from '../fn/notifications-controller/mark-all-read-for-user';
import { MarkAllReadForUser$Params } from '../fn/notifications-controller/mark-all-read-for-user';
import { markAllSeenForUser } from '../fn/notifications-controller/mark-all-seen-for-user';
import { MarkAllSeenForUser$Params } from '../fn/notifications-controller/mark-all-seen-for-user';
import { PageNotification } from '../models/page-notification';

@Injectable({ providedIn: 'root' })
export class NotificationsControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `markAllSeenForUser()` */
  static readonly MarkAllSeenForUserPath = '/notification/mark-all-seen/{userId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `markAllSeenForUser()` instead.
   *
   * This method doesn't expect any request body.
   */
  markAllSeenForUser$Response(params: MarkAllSeenForUser$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return markAllSeenForUser(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `markAllSeenForUser$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  markAllSeenForUser(params: MarkAllSeenForUser$Params, context?: HttpContext): Observable<void> {
    return this.markAllSeenForUser$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `markAllReadForUser()` */
  static readonly MarkAllReadForUserPath = '/notification/mark-all-read/{userId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `markAllReadForUser()` instead.
   *
   * This method doesn't expect any request body.
   */
  markAllReadForUser$Response(params: MarkAllReadForUser$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return markAllReadForUser(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `markAllReadForUser$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  markAllReadForUser(params: MarkAllReadForUser$Params, context?: HttpContext): Observable<void> {
    return this.markAllReadForUser$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `editNotificationsForUser()` */
  static readonly EditNotificationsForUserPath = '/notification/edit';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `editNotificationsForUser()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  editNotificationsForUser$Response(params: EditNotificationsForUser$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return editNotificationsForUser(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `editNotificationsForUser$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  editNotificationsForUser(params: EditNotificationsForUser$Params, context?: HttpContext): Observable<void> {
    return this.editNotificationsForUser$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getAllNotificationsForUser()` */
  static readonly GetAllNotificationsForUserPath = '/notification/{userId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllNotificationsForUser()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllNotificationsForUser$Response(params: GetAllNotificationsForUser$Params, context?: HttpContext): Observable<StrictHttpResponse<PageNotification>> {
    return getAllNotificationsForUser(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllNotificationsForUser$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllNotificationsForUser(params: GetAllNotificationsForUser$Params, context?: HttpContext): Observable<PageNotification> {
    return this.getAllNotificationsForUser$Response(params, context).pipe(
      map((r: StrictHttpResponse<PageNotification>): PageNotification => r.body)
    );
  }

  /** Path part for operation `getAllUnreadNotificationsForUser()` */
  static readonly GetAllUnreadNotificationsForUserPath = '/notification/unread/{userId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllUnreadNotificationsForUser()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllUnreadNotificationsForUser$Response(params: GetAllUnreadNotificationsForUser$Params, context?: HttpContext): Observable<StrictHttpResponse<PageNotification>> {
    return getAllUnreadNotificationsForUser(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllUnreadNotificationsForUser$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllUnreadNotificationsForUser(params: GetAllUnreadNotificationsForUser$Params, context?: HttpContext): Observable<PageNotification> {
    return this.getAllUnreadNotificationsForUser$Response(params, context).pipe(
      map((r: StrictHttpResponse<PageNotification>): PageNotification => r.body)
    );
  }

}
