/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { getUserNotificationPreferences } from '../fn/preferences-controller/get-user-notification-preferences';
import { GetUserNotificationPreferences$Params } from '../fn/preferences-controller/get-user-notification-preferences';
import { NotificationPreferences } from '../models/notification-preferences';
import { turnOffNotificationsForType } from '../fn/preferences-controller/turn-off-notifications-for-type';
import { TurnOffNotificationsForType$Params } from '../fn/preferences-controller/turn-off-notifications-for-type';
import { turnOnNotificationsForType } from '../fn/preferences-controller/turn-on-notifications-for-type';
import { TurnOnNotificationsForType$Params } from '../fn/preferences-controller/turn-on-notifications-for-type';

@Injectable({ providedIn: 'root' })
export class PreferencesControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `turnOnNotificationsForType()` */
  static readonly TurnOnNotificationsForTypePath = '/preference/turn-on-notification-for-type/{userId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `turnOnNotificationsForType()` instead.
   *
   * This method doesn't expect any request body.
   */
  turnOnNotificationsForType$Response(params: TurnOnNotificationsForType$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return turnOnNotificationsForType(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `turnOnNotificationsForType$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  turnOnNotificationsForType(params: TurnOnNotificationsForType$Params, context?: HttpContext): Observable<void> {
    return this.turnOnNotificationsForType$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `turnOffNotificationsForType()` */
  static readonly TurnOffNotificationsForTypePath = '/preference/turn-off-notification-for-type/{userId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `turnOffNotificationsForType()` instead.
   *
   * This method doesn't expect any request body.
   */
  turnOffNotificationsForType$Response(params: TurnOffNotificationsForType$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return turnOffNotificationsForType(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `turnOffNotificationsForType$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  turnOffNotificationsForType(params: TurnOffNotificationsForType$Params, context?: HttpContext): Observable<void> {
    return this.turnOffNotificationsForType$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getUserNotificationPreferences()` */
  static readonly GetUserNotificationPreferencesPath = '/preference/{userId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getUserNotificationPreferences()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserNotificationPreferences$Response(params: GetUserNotificationPreferences$Params, context?: HttpContext): Observable<StrictHttpResponse<NotificationPreferences>> {
    return getUserNotificationPreferences(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getUserNotificationPreferences$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserNotificationPreferences(params: GetUserNotificationPreferences$Params, context?: HttpContext): Observable<NotificationPreferences> {
    return this.getUserNotificationPreferences$Response(params, context).pipe(
      map((r: StrictHttpResponse<NotificationPreferences>): NotificationPreferences => r.body)
    );
  }

}
