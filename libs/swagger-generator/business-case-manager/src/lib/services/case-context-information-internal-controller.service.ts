/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { DefaultParticipantCasePermissionSet } from '../models/default-participant-case-permission-set';
import { getContextCreationInformationInternal } from '../fn/case-context-information-internal-controller/get-context-creation-information-internal';
import { GetContextCreationInformationInternal$Params } from '../fn/case-context-information-internal-controller/get-context-creation-information-internal';
import { getDefaultCasePermissionsInternal } from '../fn/case-context-information-internal-controller/get-default-case-permissions-internal';
import { GetDefaultCasePermissionsInternal$Params } from '../fn/case-context-information-internal-controller/get-default-case-permissions-internal';
import { getParticipantCasePermissionSetsForBusinessCases } from '../fn/case-context-information-internal-controller/get-participant-case-permission-sets-for-business-cases';
import { GetParticipantCasePermissionSetsForBusinessCases$Params } from '../fn/case-context-information-internal-controller/get-participant-case-permission-sets-for-business-cases';
import { getParticipantPermissionSetInternal } from '../fn/case-context-information-internal-controller/get-participant-permission-set-internal';
import { GetParticipantPermissionSetInternal$Params } from '../fn/case-context-information-internal-controller/get-participant-permission-set-internal';
import { getParticipantVisibilityInternal } from '../fn/case-context-information-internal-controller/get-participant-visibility-internal';
import { GetParticipantVisibilityInternal$Params } from '../fn/case-context-information-internal-controller/get-participant-visibility-internal';
import { ParticipantCasePermissionSetEntity } from '../models/participant-case-permission-set-entity';
import { ParticipantVisibilityDto } from '../models/participant-visibility-dto';
import { reloadCaseContext } from '../fn/case-context-information-internal-controller/reload-case-context';
import { ReloadCaseContext$Params } from '../fn/case-context-information-internal-controller/reload-case-context';
import { ReloadContextRequest } from '../models/reload-context-request';

@Injectable({ providedIn: 'root' })
export class CaseContextInformationInternalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `reloadCaseContext()` */
  static readonly ReloadCaseContextPath = '/internal/context-case-information/reload-context';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `reloadCaseContext()` instead.
   *
   * This method doesn't expect any request body.
   */
  reloadCaseContext$Response(params: ReloadCaseContext$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return reloadCaseContext(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `reloadCaseContext$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  reloadCaseContext(params: ReloadCaseContext$Params, context?: HttpContext): Observable<void> {
    return this.reloadCaseContext$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getParticipantCasePermissionSetsForBusinessCases()` */
  static readonly GetParticipantCasePermissionSetsForBusinessCasesPath = '/internal/context-case-information/participant-case-permission-sets';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getParticipantCasePermissionSetsForBusinessCases()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  getParticipantCasePermissionSetsForBusinessCases$Response(params: GetParticipantCasePermissionSetsForBusinessCases$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<ParticipantCasePermissionSetEntity>>> {
    return getParticipantCasePermissionSetsForBusinessCases(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getParticipantCasePermissionSetsForBusinessCases$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  getParticipantCasePermissionSetsForBusinessCases(params: GetParticipantCasePermissionSetsForBusinessCases$Params, context?: HttpContext): Observable<Array<ParticipantCasePermissionSetEntity>> {
    return this.getParticipantCasePermissionSetsForBusinessCases$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<ParticipantCasePermissionSetEntity>>): Array<ParticipantCasePermissionSetEntity> => r.body)
    );
  }

  /** Path part for operation `getContextCreationInformationInternal()` */
  static readonly GetContextCreationInformationInternalPath = '/internal/context-case-information/{businessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getContextCreationInformationInternal()` instead.
   *
   * This method doesn't expect any request body.
   */
  getContextCreationInformationInternal$Response(params: GetContextCreationInformationInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<ReloadContextRequest>> {
    return getContextCreationInformationInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getContextCreationInformationInternal$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getContextCreationInformationInternal(params: GetContextCreationInformationInternal$Params, context?: HttpContext): Observable<ReloadContextRequest> {
    return this.getContextCreationInformationInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<ReloadContextRequest>): ReloadContextRequest => r.body)
    );
  }

  /** Path part for operation `getParticipantPermissionSetInternal()` */
  static readonly GetParticipantPermissionSetInternalPath = '/internal/context-case-information/{businessCaseId}/get-participant-permission-set';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getParticipantPermissionSetInternal()` instead.
   *
   * This method doesn't expect any request body.
   */
  getParticipantPermissionSetInternal$Response(params: GetParticipantPermissionSetInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<ParticipantCasePermissionSetEntity>> {
    return getParticipantPermissionSetInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getParticipantPermissionSetInternal$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getParticipantPermissionSetInternal(params: GetParticipantPermissionSetInternal$Params, context?: HttpContext): Observable<ParticipantCasePermissionSetEntity> {
    return this.getParticipantPermissionSetInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<ParticipantCasePermissionSetEntity>): ParticipantCasePermissionSetEntity => r.body)
    );
  }

  /** Path part for operation `getDefaultCasePermissionsInternal()` */
  static readonly GetDefaultCasePermissionsInternalPath = '/internal/context-case-information/{businessCaseId}/get-default-case-permissions';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getDefaultCasePermissionsInternal()` instead.
   *
   * This method doesn't expect any request body.
   */
  getDefaultCasePermissionsInternal$Response(params: GetDefaultCasePermissionsInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<DefaultParticipantCasePermissionSet>>> {
    return getDefaultCasePermissionsInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getDefaultCasePermissionsInternal$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getDefaultCasePermissionsInternal(params: GetDefaultCasePermissionsInternal$Params, context?: HttpContext): Observable<Array<DefaultParticipantCasePermissionSet>> {
    return this.getDefaultCasePermissionsInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<DefaultParticipantCasePermissionSet>>): Array<DefaultParticipantCasePermissionSet> => r.body)
    );
  }

  /** Path part for operation `getParticipantVisibilityInternal()` */
  static readonly GetParticipantVisibilityInternalPath = '/internal/context-case-information/participant-visibility/{businessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getParticipantVisibilityInternal()` instead.
   *
   * This method doesn't expect any request body.
   */
  getParticipantVisibilityInternal$Response(params: GetParticipantVisibilityInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<ParticipantVisibilityDto>> {
    return getParticipantVisibilityInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getParticipantVisibilityInternal$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getParticipantVisibilityInternal(params: GetParticipantVisibilityInternal$Params, context?: HttpContext): Observable<ParticipantVisibilityDto> {
    return this.getParticipantVisibilityInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<ParticipantVisibilityDto>): ParticipantVisibilityDto => r.body)
    );
  }

}
