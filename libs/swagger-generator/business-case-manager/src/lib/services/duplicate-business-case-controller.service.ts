/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { duplicateBusinessCase } from '../fn/duplicate-business-case-controller/duplicate-business-case';
import { DuplicateBusinessCase$Params } from '../fn/duplicate-business-case-controller/duplicate-business-case';
import { DuplicateBusinessCaseResponse } from '../models/duplicate-business-case-response';

@Injectable({ providedIn: 'root' })
export class DuplicateBusinessCaseControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `duplicateBusinessCase()` */
  static readonly DuplicateBusinessCasePath = '/duplicate/{businessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `duplicateBusinessCase()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  duplicateBusinessCase$Response(params: DuplicateBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<DuplicateBusinessCaseResponse>> {
    return duplicateBusinessCase(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `duplicateBusinessCase$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  duplicateBusinessCase(params: DuplicateBusinessCase$Params, context?: HttpContext): Observable<DuplicateBusinessCaseResponse> {
    return this.duplicateBusinessCase$Response(params, context).pipe(
      map((r: StrictHttpResponse<DuplicateBusinessCaseResponse>): DuplicateBusinessCaseResponse => r.body)
    );
  }

}
