/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { addContactPersons } from '../fn/contact-person-controller/add-contact-persons';
import { AddContactPersons$Params } from '../fn/contact-person-controller/add-contact-persons';
import { deleteAllContactPersons } from '../fn/contact-person-controller/delete-all-contact-persons';
import { DeleteAllContactPersons$Params } from '../fn/contact-person-controller/delete-all-contact-persons';
import { deleteSingleContactPerson } from '../fn/contact-person-controller/delete-single-contact-person';
import { DeleteSingleContactPerson$Params } from '../fn/contact-person-controller/delete-single-contact-person';

@Injectable({ providedIn: 'root' })
export class ContactPersonControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `addContactPersons()` */
  static readonly AddContactPersonsPath = '/contact-person/{businessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `addContactPersons()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   *
   * @deprecated
   */
  addContactPersons$Response(params: AddContactPersons$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return addContactPersons(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `addContactPersons$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   *
   * @deprecated
   */
  addContactPersons(params: AddContactPersons$Params, context?: HttpContext): Observable<Array<string>> {
    return this.addContactPersons$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

  /** Path part for operation `deleteAllContactPersons()` */
  static readonly DeleteAllContactPersonsPath = '/contact-person/{businessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteAllContactPersons()` instead.
   *
   * This method doesn't expect any request body.
   *
   * @deprecated
   */
  deleteAllContactPersons$Response(params: DeleteAllContactPersons$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return deleteAllContactPersons(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteAllContactPersons$Response()` instead.
   *
   * This method doesn't expect any request body.
   *
   * @deprecated
   */
  deleteAllContactPersons(params: DeleteAllContactPersons$Params, context?: HttpContext): Observable<void> {
    return this.deleteAllContactPersons$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `deleteSingleContactPerson()` */
  static readonly DeleteSingleContactPersonPath = '/contact-person/{businessCaseId}/{userId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteSingleContactPerson()` instead.
   *
   * This method doesn't expect any request body.
   *
   * @deprecated
   */
  deleteSingleContactPerson$Response(params: DeleteSingleContactPerson$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return deleteSingleContactPerson(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteSingleContactPerson$Response()` instead.
   *
   * This method doesn't expect any request body.
   *
   * @deprecated
   */
  deleteSingleContactPerson(params: DeleteSingleContactPerson$Params, context?: HttpContext): Observable<Array<string>> {
    return this.deleteSingleContactPerson$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

}
