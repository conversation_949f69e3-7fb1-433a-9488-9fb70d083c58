/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { addPermissionToParticipant } from '../fn/case-permission-controller/add-permission-to-participant';
import { AddPermissionToParticipant$Params } from '../fn/case-permission-controller/add-permission-to-participant';
import { deletePermissionFromParticipant } from '../fn/case-permission-controller/delete-permission-from-participant';
import { DeletePermissionFromParticipant$Params } from '../fn/case-permission-controller/delete-permission-from-participant';
import { getParticipantPermission } from '../fn/case-permission-controller/get-participant-permission';
import { GetParticipantPermission$Params } from '../fn/case-permission-controller/get-participant-permission';
import { ParticipantCasePermissionSetEntity } from '../models/participant-case-permission-set-entity';

@Injectable({ providedIn: 'root' })
export class CasePermissionControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `addPermissionToParticipant()` */
  static readonly AddPermissionToParticipantPath = '/business-case-permissions/add-permission-to-participant';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `addPermissionToParticipant()` instead.
   *
   * This method doesn't expect any request body.
   */
  addPermissionToParticipant$Response(params: AddPermissionToParticipant$Params, context?: HttpContext): Observable<StrictHttpResponse<ParticipantCasePermissionSetEntity>> {
    return addPermissionToParticipant(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `addPermissionToParticipant$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  addPermissionToParticipant(params: AddPermissionToParticipant$Params, context?: HttpContext): Observable<ParticipantCasePermissionSetEntity> {
    return this.addPermissionToParticipant$Response(params, context).pipe(
      map((r: StrictHttpResponse<ParticipantCasePermissionSetEntity>): ParticipantCasePermissionSetEntity => r.body)
    );
  }

  /** Path part for operation `getParticipantPermission()` */
  static readonly GetParticipantPermissionPath = '/business-case-permissions/get-participant-permission';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getParticipantPermission()` instead.
   *
   * This method doesn't expect any request body.
   */
  getParticipantPermission$Response(params: GetParticipantPermission$Params, context?: HttpContext): Observable<StrictHttpResponse<ParticipantCasePermissionSetEntity>> {
    return getParticipantPermission(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getParticipantPermission$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getParticipantPermission(params: GetParticipantPermission$Params, context?: HttpContext): Observable<ParticipantCasePermissionSetEntity> {
    return this.getParticipantPermission$Response(params, context).pipe(
      map((r: StrictHttpResponse<ParticipantCasePermissionSetEntity>): ParticipantCasePermissionSetEntity => r.body)
    );
  }

  /** Path part for operation `deletePermissionFromParticipant()` */
  static readonly DeletePermissionFromParticipantPath = '/business-case-permissions/delete-permission-from-participant';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deletePermissionFromParticipant()` instead.
   *
   * This method doesn't expect any request body.
   */
  deletePermissionFromParticipant$Response(params: DeletePermissionFromParticipant$Params, context?: HttpContext): Observable<StrictHttpResponse<ParticipantCasePermissionSetEntity>> {
    return deletePermissionFromParticipant(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deletePermissionFromParticipant$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deletePermissionFromParticipant(params: DeletePermissionFromParticipant$Params, context?: HttpContext): Observable<ParticipantCasePermissionSetEntity> {
    return this.deletePermissionFromParticipant$Response(params, context).pipe(
      map((r: StrictHttpResponse<ParticipantCasePermissionSetEntity>): ParticipantCasePermissionSetEntity => r.body)
    );
  }

}
