/* tslint:disable */
/* eslint-disable */
import { FieldDto } from '../models/field-dto';
export interface Information {
  businessCaseId: string;
  creationDate?: string;
  field?: FieldDto;
  id?: string;
  informationState?: 'ACTIVE' | 'DELETED';
  isPromoted?: boolean;
  /** @deprecated */isPublic?: boolean;
  key: string;
  lastModifiedDate?: string;
  revisionId?: string;
  synced?: boolean;
  userId?: string;
  value: {
};
}
