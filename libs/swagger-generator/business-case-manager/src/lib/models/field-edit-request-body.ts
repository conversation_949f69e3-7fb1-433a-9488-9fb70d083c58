/* tslint:disable */
/* eslint-disable */
export interface FieldEditRequestBody {
  categoryId?: string;
  description?: string;
  fieldMetaData?: {
};
  fieldType?: 'SHORT_TEXT' | 'LONG_TEXT' | 'INTEGER' | 'DECIMAL' | 'MONETARY' | 'DATE' | 'DOCUMENT' | 'MONTHS' | 'PERCENT' | 'BOOLEAN' | 'LOCATION' | 'SELECT' | 'TABLE' | 'DATE_RANGE' | 'COMPOSITE' | 'MULTI_SELECT' | 'FINANCING_PARTNERS';
  isPublic?: boolean;
  label?: string;
}
