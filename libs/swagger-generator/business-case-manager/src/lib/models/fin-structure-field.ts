/* tslint:disable */
/* eslint-disable */
import { FieldOwner } from '../models/field-owner';
export interface FinStructureField {
  approvalStatus?: 'PENDING' | 'APPROVED';
  assetType?: 'HOTEL' | 'OTHER';
  categoryId?: string;
  defaultInformation?: string;
  dependantFields?: Array<string>;
  description?: string;
  disabled?: boolean;
  dynamic?: boolean;
  expression?: string;
  fieldMetaData?: {
};
  fieldOwner?: FieldOwner;
  fieldSetName?: string;
  fieldType?: 'SHORT_TEXT' | 'LONG_TEXT' | 'INTEGER' | 'DECIMAL' | 'MONETARY' | 'DATE' | 'DOCUMENT' | 'MONTHS' | 'PERCENT' | 'BOOLEAN' | 'LOCATION' | 'SELECT' | 'TABLE' | 'DATE_RANGE' | 'COMPOSITE' | 'MULTI_SELECT' | 'FINANCING_PARTNERS';
  group?: string;
  id?: string;
  isHidden?: boolean;
  /** @deprecated */isMultiSelect?: boolean;
  isPromoted?: boolean;
  isPublic?: boolean;
  isReadOnly?: boolean;
  isRequired?: boolean;
  key?: string;
  label?: string;
  ordinal?: number;
  portalVisibility?: 'VISIBLE' | 'REQUESTED' | 'NOT_SET';
  priority?: number;
  restoredFromRevision?: boolean;
  restoredFromRevisionId?: string;
  value?: {
};
  visibilityExpression?: string;
}
