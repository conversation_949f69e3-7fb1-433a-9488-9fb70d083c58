/* tslint:disable */
/* eslint-disable */
import { Address } from '../models/address';
import { BaseInfo } from '../models/base-info';
import { CollaborationSettings } from '../models/collaboration-settings';
import { Coordinates } from '../models/coordinates';
import { Email } from '../models/email';
export interface Customer {
  address?: Address;
  allowedMfaTypes?: Array<'EMAIL' | 'SMS' | 'AUTHENTICATION_APP'>;
  bankingGroup?: 'VOLKSBANK' | 'SPARKASSE' | 'OTHER';
  baseInfo?: BaseInfo;
  collaborationSettings?: CollaborationSettings;
  coordinates?: Coordinates;
  createdBy?: string;
  creationDate?: string;
  custom?: {
};
  customerOnboardingStatus?: 'NOT_ONBOARDED' | 'ONBOARDED';
  customerStateStatus?: 'CREATED' | 'ACTIVE' | 'DELETED';
  customerStatus?: 'REGULAR' | 'GUEST';
  customerType?: 'BANK' | 'REAL_ESTATE' | 'CORPORATE' | 'INTERNAL' | 'FSP';
  email?: Email;
  id?: string;
  internal?: boolean;
  key?: string;
  lastModifiedDate?: string;
  logo?: string;
  mfaEnforced?: boolean;
  name?: string;
  salesChannel?: 'NEOSHARE' | 'BMS';
  themeName?: string;
  website?: string;
}
