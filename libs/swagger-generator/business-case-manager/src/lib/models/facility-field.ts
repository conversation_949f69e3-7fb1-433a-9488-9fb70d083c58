/* tslint:disable */
/* eslint-disable */
export interface FacilityField {
  description?: string;
  eligibleForCalculation?: boolean;
  fieldMetaData?: {
};
  fieldType?: 'SHORT_TEXT' | 'LONG_TEXT' | 'INTEGER' | 'DECIMAL' | 'MONETARY' | 'DATE' | 'DOCUMENT' | 'MONTHS' | 'PERCENT' | 'BOOLEAN' | 'LOCATION' | 'SELECT' | 'TABLE' | 'DATE_RANGE' | 'COMPOSITE' | 'MULTI_SELECT' | 'FINANCING_PARTNERS';
  group?: string;
  isEditable?: boolean;
  isUpdated?: boolean;
  key?: string;
  label?: string;
  ordinal?: number;
  restoredFromRevision?: boolean;
  restoredFromRevisionId?: string;
  value?: {
};
}
