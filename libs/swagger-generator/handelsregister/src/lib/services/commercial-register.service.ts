/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { commercialRegisterControllerCrawlAndUpsertOrGetCompanyDocuments } from '../fn/commercial-register/commercial-register-controller-crawl-and-upsert-or-get-company-documents';
import { CommercialRegisterControllerCrawlAndUpsertOrGetCompanyDocuments$Params } from '../fn/commercial-register/commercial-register-controller-crawl-and-upsert-or-get-company-documents';
import { commercialRegisterControllerDeleteAndRefetchCompany } from '../fn/commercial-register/commercial-register-controller-delete-and-refetch-company';
import { CommercialRegisterControllerDeleteAndRefetchCompany$Params } from '../fn/commercial-register/commercial-register-controller-delete-and-refetch-company';
import { commercialRegisterControllerDownloadChronologicalHardCopyPrintout } from '../fn/commercial-register/commercial-register-controller-download-chronological-hard-copy-printout';
import { CommercialRegisterControllerDownloadChronologicalHardCopyPrintout$Params } from '../fn/commercial-register/commercial-register-controller-download-chronological-hard-copy-printout';
import { commercialRegisterControllerDownloadCurrentHardCopyPrintout } from '../fn/commercial-register/commercial-register-controller-download-current-hard-copy-printout';
import { CommercialRegisterControllerDownloadCurrentHardCopyPrintout$Params } from '../fn/commercial-register/commercial-register-controller-download-current-hard-copy-printout';
import { commercialRegisterControllerDownloadHistoricHardCopyPrintout } from '../fn/commercial-register/commercial-register-controller-download-historic-hard-copy-printout';
import { CommercialRegisterControllerDownloadHistoricHardCopyPrintout$Params } from '../fn/commercial-register/commercial-register-controller-download-historic-hard-copy-printout';
import { commercialRegisterControllerGetHasCrawlError } from '../fn/commercial-register/commercial-register-controller-get-has-crawl-error';
import { CommercialRegisterControllerGetHasCrawlError$Params } from '../fn/commercial-register/commercial-register-controller-get-has-crawl-error';
import { commercialRegisterControllerGetLinkAvailability } from '../fn/commercial-register/commercial-register-controller-get-link-availability';
import { CommercialRegisterControllerGetLinkAvailability$Params } from '../fn/commercial-register/commercial-register-controller-get-link-availability';
import { CompanyDocumentsResponseDto } from '../models/company-documents-response-dto';
import { DocumentAvailabilityResponseDto } from '../models/document-availability-response-dto';
import { HasCrawlErrorResponseDto } from '../models/has-crawl-error-response-dto';

@Injectable({ providedIn: 'root' })
export class CommercialRegisterService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `commercialRegisterControllerGetHasCrawlError()` */
  static readonly CommercialRegisterControllerGetHasCrawlErrorPath = '/register/has-crawl-error';

  /**
   * Check if company has error while crawling documents
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `commercialRegisterControllerGetHasCrawlError()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  commercialRegisterControllerGetHasCrawlError$Response(params: CommercialRegisterControllerGetHasCrawlError$Params, context?: HttpContext): Observable<StrictHttpResponse<HasCrawlErrorResponseDto>> {
    return commercialRegisterControllerGetHasCrawlError(this.http, this.rootUrl, params, context);
  }

  /**
   * Check if company has error while crawling documents
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `commercialRegisterControllerGetHasCrawlError$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  commercialRegisterControllerGetHasCrawlError(params: CommercialRegisterControllerGetHasCrawlError$Params, context?: HttpContext): Observable<HasCrawlErrorResponseDto> {
    return this.commercialRegisterControllerGetHasCrawlError$Response(params, context).pipe(
      map((r: StrictHttpResponse<HasCrawlErrorResponseDto>): HasCrawlErrorResponseDto => r.body)
    );
  }

  /** Path part for operation `commercialRegisterControllerGetLinkAvailability()` */
  static readonly CommercialRegisterControllerGetLinkAvailabilityPath = '/register/document-availability';

  /**
   * Availability of documents in the category 'AD', 'HD' and 'DK'.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `commercialRegisterControllerGetLinkAvailability()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  commercialRegisterControllerGetLinkAvailability$Response(params: CommercialRegisterControllerGetLinkAvailability$Params, context?: HttpContext): Observable<StrictHttpResponse<DocumentAvailabilityResponseDto>> {
    return commercialRegisterControllerGetLinkAvailability(this.http, this.rootUrl, params, context);
  }

  /**
   * Availability of documents in the category 'AD', 'HD' and 'DK'.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `commercialRegisterControllerGetLinkAvailability$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  commercialRegisterControllerGetLinkAvailability(params: CommercialRegisterControllerGetLinkAvailability$Params, context?: HttpContext): Observable<DocumentAvailabilityResponseDto> {
    return this.commercialRegisterControllerGetLinkAvailability$Response(params, context).pipe(
      map((r: StrictHttpResponse<DocumentAvailabilityResponseDto>): DocumentAvailabilityResponseDto => r.body)
    );
  }

  /** Path part for operation `commercialRegisterControllerDownloadCurrentHardCopyPrintout()` */
  static readonly CommercialRegisterControllerDownloadCurrentHardCopyPrintoutPath = '/register/download/current-printout';

  /**
   * Downloads the current hard copy printout from the handelsregister.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `commercialRegisterControllerDownloadCurrentHardCopyPrintout()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  commercialRegisterControllerDownloadCurrentHardCopyPrintout$Response(params: CommercialRegisterControllerDownloadCurrentHardCopyPrintout$Params, context?: HttpContext): Observable<StrictHttpResponse<Blob>> {
    return commercialRegisterControllerDownloadCurrentHardCopyPrintout(this.http, this.rootUrl, params, context);
  }

  /**
   * Downloads the current hard copy printout from the handelsregister.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `commercialRegisterControllerDownloadCurrentHardCopyPrintout$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  commercialRegisterControllerDownloadCurrentHardCopyPrintout(params: CommercialRegisterControllerDownloadCurrentHardCopyPrintout$Params, context?: HttpContext): Observable<Blob> {
    return this.commercialRegisterControllerDownloadCurrentHardCopyPrintout$Response(params, context).pipe(
      map((r: StrictHttpResponse<Blob>): Blob => r.body)
    );
  }

  /** Path part for operation `commercialRegisterControllerDownloadChronologicalHardCopyPrintout()` */
  static readonly CommercialRegisterControllerDownloadChronologicalHardCopyPrintoutPath = '/register/download/chronological-printout';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `commercialRegisterControllerDownloadChronologicalHardCopyPrintout()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  commercialRegisterControllerDownloadChronologicalHardCopyPrintout$Response(params: CommercialRegisterControllerDownloadChronologicalHardCopyPrintout$Params, context?: HttpContext): Observable<StrictHttpResponse<Blob>> {
    return commercialRegisterControllerDownloadChronologicalHardCopyPrintout(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `commercialRegisterControllerDownloadChronologicalHardCopyPrintout$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  commercialRegisterControllerDownloadChronologicalHardCopyPrintout(params: CommercialRegisterControllerDownloadChronologicalHardCopyPrintout$Params, context?: HttpContext): Observable<Blob> {
    return this.commercialRegisterControllerDownloadChronologicalHardCopyPrintout$Response(params, context).pipe(
      map((r: StrictHttpResponse<Blob>): Blob => r.body)
    );
  }

  /** Path part for operation `commercialRegisterControllerDownloadHistoricHardCopyPrintout()` */
  static readonly CommercialRegisterControllerDownloadHistoricHardCopyPrintoutPath = '/register/download/historic-printout';

  /**
   * Downloads the historic hard copy printout from the handelsregister.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `commercialRegisterControllerDownloadHistoricHardCopyPrintout()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  commercialRegisterControllerDownloadHistoricHardCopyPrintout$Response(params: CommercialRegisterControllerDownloadHistoricHardCopyPrintout$Params, context?: HttpContext): Observable<StrictHttpResponse<Blob>> {
    return commercialRegisterControllerDownloadHistoricHardCopyPrintout(this.http, this.rootUrl, params, context);
  }

  /**
   * Downloads the historic hard copy printout from the handelsregister.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `commercialRegisterControllerDownloadHistoricHardCopyPrintout$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  commercialRegisterControllerDownloadHistoricHardCopyPrintout(params: CommercialRegisterControllerDownloadHistoricHardCopyPrintout$Params, context?: HttpContext): Observable<Blob> {
    return this.commercialRegisterControllerDownloadHistoricHardCopyPrintout$Response(params, context).pipe(
      map((r: StrictHttpResponse<Blob>): Blob => r.body)
    );
  }

  /** Path part for operation `commercialRegisterControllerCrawlAndUpsertOrGetCompanyDocuments()` */
  static readonly CommercialRegisterControllerCrawlAndUpsertOrGetCompanyDocumentsPath = '/register/document-list/get-or-crawl';

  /**
   * Attempts to retrieve a list of company documents given the company's register information.
   *
   * If no company is found, the company is instead crawled in its entirety from the Handelsregister.
   * Performance will suck in this case.
   *
   *
   * compatibility.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `commercialRegisterControllerCrawlAndUpsertOrGetCompanyDocuments()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   *
   * @deprecated
   */
  commercialRegisterControllerCrawlAndUpsertOrGetCompanyDocuments$Response(params: CommercialRegisterControllerCrawlAndUpsertOrGetCompanyDocuments$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<CompanyDocumentsResponseDto>>> {
    return commercialRegisterControllerCrawlAndUpsertOrGetCompanyDocuments(this.http, this.rootUrl, params, context);
  }

  /**
   * Attempts to retrieve a list of company documents given the company's register information.
   *
   * If no company is found, the company is instead crawled in its entirety from the Handelsregister.
   * Performance will suck in this case.
   *
   *
   * compatibility.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `commercialRegisterControllerCrawlAndUpsertOrGetCompanyDocuments$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   *
   * @deprecated
   */
  commercialRegisterControllerCrawlAndUpsertOrGetCompanyDocuments(params: CommercialRegisterControllerCrawlAndUpsertOrGetCompanyDocuments$Params, context?: HttpContext): Observable<Array<CompanyDocumentsResponseDto>> {
    return this.commercialRegisterControllerCrawlAndUpsertOrGetCompanyDocuments$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<CompanyDocumentsResponseDto>>): Array<CompanyDocumentsResponseDto> => r.body)
    );
  }

  /** Path part for operation `commercialRegisterControllerDeleteAndRefetchCompany()` */
  static readonly CommercialRegisterControllerDeleteAndRefetchCompanyPath = '/register/delete-and-refetch-company';

  /**
   * FOR INTERNAL USE ONLY. Deletes the given company and refetches it.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `commercialRegisterControllerDeleteAndRefetchCompany()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  commercialRegisterControllerDeleteAndRefetchCompany$Response(params: CommercialRegisterControllerDeleteAndRefetchCompany$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return commercialRegisterControllerDeleteAndRefetchCompany(this.http, this.rootUrl, params, context);
  }

  /**
   * FOR INTERNAL USE ONLY. Deletes the given company and refetches it.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `commercialRegisterControllerDeleteAndRefetchCompany$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  commercialRegisterControllerDeleteAndRefetchCompany(params: CommercialRegisterControllerDeleteAndRefetchCompany$Params, context?: HttpContext): Observable<void> {
    return this.commercialRegisterControllerDeleteAndRefetchCompany$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
