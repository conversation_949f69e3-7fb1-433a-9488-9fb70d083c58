/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { adminControllerTest } from '../fn/admin/admin-controller-test';
import { AdminControllerTest$Params } from '../fn/admin/admin-controller-test';
import { adminControllerTest2 } from '../fn/admin/admin-controller-test-2';
import { AdminControllerTest2$Params } from '../fn/admin/admin-controller-test-2';

@Injectable({ providedIn: 'root' })
export class AdminService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `adminControllerTest()` */
  static readonly AdminControllerTestPath = '/ec2/spawn';

  /**
   * FOR INTERNAL USE ONLY. Spawns an EC2 instance with an elastic IP address.
   *
   * Be cautious when using this method because spawning EC2 instances costs money.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `adminControllerTest()` instead.
   *
   * This method doesn't expect any request body.
   */
  adminControllerTest$Response(params?: AdminControllerTest$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return adminControllerTest(this.http, this.rootUrl, params, context);
  }

  /**
   * FOR INTERNAL USE ONLY. Spawns an EC2 instance with an elastic IP address.
   *
   * Be cautious when using this method because spawning EC2 instances costs money.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `adminControllerTest$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  adminControllerTest(params?: AdminControllerTest$Params, context?: HttpContext): Observable<void> {
    return this.adminControllerTest$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `adminControllerTest2()` */
  static readonly AdminControllerTest2Path = '/ec2/kill-by/{ip}';

  /**
   * FOR INTERNAL USE ONLY. Deletes the given allocated public IP address.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `adminControllerTest2()` instead.
   *
   * This method doesn't expect any request body.
   */
  adminControllerTest2$Response(params: AdminControllerTest2$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return adminControllerTest2(this.http, this.rootUrl, params, context);
  }

  /**
   * FOR INTERNAL USE ONLY. Deletes the given allocated public IP address.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `adminControllerTest2$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  adminControllerTest2(params: AdminControllerTest2$Params, context?: HttpContext): Observable<void> {
    return this.adminControllerTest2$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
