/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { backgroundJobsControllerCrawlExistingCompanies } from '../fn/background-jobs/background-jobs-controller-crawl-existing-companies';
import { BackgroundJobsControllerCrawlExistingCompanies$Params } from '../fn/background-jobs/background-jobs-controller-crawl-existing-companies';
import { backgroundJobsControllerTriggerCompaniesWithCrawlErrors } from '../fn/background-jobs/background-jobs-controller-trigger-companies-with-crawl-errors';
import { BackgroundJobsControllerTriggerCompaniesWithCrawlErrors$Params } from '../fn/background-jobs/background-jobs-controller-trigger-companies-with-crawl-errors';
import { backgroundJobsControllerTriggerCompanyUpdate } from '../fn/background-jobs/background-jobs-controller-trigger-company-update';
import { BackgroundJobsControllerTriggerCompanyUpdate$Params } from '../fn/background-jobs/background-jobs-controller-trigger-company-update';
import { backgroundJobsControllerTriggerMissingDocuments } from '../fn/background-jobs/background-jobs-controller-trigger-missing-documents';
import { BackgroundJobsControllerTriggerMissingDocuments$Params } from '../fn/background-jobs/background-jobs-controller-trigger-missing-documents';

@Injectable({ providedIn: 'root' })
export class BackgroundJobsService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `backgroundJobsControllerTriggerMissingDocuments()` */
  static readonly BackgroundJobsControllerTriggerMissingDocumentsPath = '/background-jobs/trigger-missing-documents';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `backgroundJobsControllerTriggerMissingDocuments()` instead.
   *
   * This method doesn't expect any request body.
   */
  backgroundJobsControllerTriggerMissingDocuments$Response(params?: BackgroundJobsControllerTriggerMissingDocuments$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return backgroundJobsControllerTriggerMissingDocuments(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `backgroundJobsControllerTriggerMissingDocuments$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  backgroundJobsControllerTriggerMissingDocuments(params?: BackgroundJobsControllerTriggerMissingDocuments$Params, context?: HttpContext): Observable<void> {
    return this.backgroundJobsControllerTriggerMissingDocuments$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `backgroundJobsControllerTriggerCompaniesWithCrawlErrors()` */
  static readonly BackgroundJobsControllerTriggerCompaniesWithCrawlErrorsPath = '/background-jobs/trigger-companies-with-crawl-errors';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `backgroundJobsControllerTriggerCompaniesWithCrawlErrors()` instead.
   *
   * This method doesn't expect any request body.
   */
  backgroundJobsControllerTriggerCompaniesWithCrawlErrors$Response(params?: BackgroundJobsControllerTriggerCompaniesWithCrawlErrors$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return backgroundJobsControllerTriggerCompaniesWithCrawlErrors(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `backgroundJobsControllerTriggerCompaniesWithCrawlErrors$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  backgroundJobsControllerTriggerCompaniesWithCrawlErrors(params?: BackgroundJobsControllerTriggerCompaniesWithCrawlErrors$Params, context?: HttpContext): Observable<void> {
    return this.backgroundJobsControllerTriggerCompaniesWithCrawlErrors$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `backgroundJobsControllerTriggerCompanyUpdate()` */
  static readonly BackgroundJobsControllerTriggerCompanyUpdatePath = '/background-jobs/fetch-documents/company';

  /**
   * FOR INTERNAL USE ONLY. Triggers document download process for a given company
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `backgroundJobsControllerTriggerCompanyUpdate()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  backgroundJobsControllerTriggerCompanyUpdate$Response(params: BackgroundJobsControllerTriggerCompanyUpdate$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return backgroundJobsControllerTriggerCompanyUpdate(this.http, this.rootUrl, params, context);
  }

  /**
   * FOR INTERNAL USE ONLY. Triggers document download process for a given company
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `backgroundJobsControllerTriggerCompanyUpdate$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  backgroundJobsControllerTriggerCompanyUpdate(params: BackgroundJobsControllerTriggerCompanyUpdate$Params, context?: HttpContext): Observable<void> {
    return this.backgroundJobsControllerTriggerCompanyUpdate$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `backgroundJobsControllerCrawlExistingCompanies()` */
  static readonly BackgroundJobsControllerCrawlExistingCompaniesPath = '/background-jobs/fetch-documents';

  /**
   * FOR INTERNAL USE ONLY. Triggers document download process for existing companies
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `backgroundJobsControllerCrawlExistingCompanies()` instead.
   *
   * This method doesn't expect any request body.
   */
  backgroundJobsControllerCrawlExistingCompanies$Response(params?: BackgroundJobsControllerCrawlExistingCompanies$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return backgroundJobsControllerCrawlExistingCompanies(this.http, this.rootUrl, params, context);
  }

  /**
   * FOR INTERNAL USE ONLY. Triggers document download process for existing companies
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `backgroundJobsControllerCrawlExistingCompanies$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  backgroundJobsControllerCrawlExistingCompanies(params?: BackgroundJobsControllerCrawlExistingCompanies$Params, context?: HttpContext): Observable<void> {
    return this.backgroundJobsControllerCrawlExistingCompanies$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
