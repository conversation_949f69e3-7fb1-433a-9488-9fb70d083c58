/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { announcementsControllerTriggerUpdate } from '../fn/announcements/announcements-controller-trigger-update';
import { AnnouncementsControllerTriggerUpdate$Params } from '../fn/announcements/announcements-controller-trigger-update';
import { announcementsControllerUpdateLinksAvailability } from '../fn/announcements/announcements-controller-update-links-availability';
import { AnnouncementsControllerUpdateLinksAvailability$Params } from '../fn/announcements/announcements-controller-update-links-availability';

@Injectable({ providedIn: 'root' })
export class AnnouncementsService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `announcementsControllerTriggerUpdate()` */
  static readonly AnnouncementsControllerTriggerUpdatePath = '/announcements/trigger-update';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `announcementsControllerTriggerUpdate()` instead.
   *
   * This method doesn't expect any request body.
   */
  announcementsControllerTriggerUpdate$Response(params?: AnnouncementsControllerTriggerUpdate$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return announcementsControllerTriggerUpdate(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `announcementsControllerTriggerUpdate$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  announcementsControllerTriggerUpdate(params?: AnnouncementsControllerTriggerUpdate$Params, context?: HttpContext): Observable<void> {
    return this.announcementsControllerTriggerUpdate$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `announcementsControllerUpdateLinksAvailability()` */
  static readonly AnnouncementsControllerUpdateLinksAvailabilityPath = '/announcements/trigger-update-link';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `announcementsControllerUpdateLinksAvailability()` instead.
   *
   * This method doesn't expect any request body.
   */
  announcementsControllerUpdateLinksAvailability$Response(params?: AnnouncementsControllerUpdateLinksAvailability$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return announcementsControllerUpdateLinksAvailability(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `announcementsControllerUpdateLinksAvailability$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  announcementsControllerUpdateLinksAvailability(params?: AnnouncementsControllerUpdateLinksAvailability$Params, context?: HttpContext): Observable<void> {
    return this.announcementsControllerUpdateLinksAvailability$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
