/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { CompanyDocumentsResponseDto } from '../models/company-documents-response-dto';
import { CompanyFolderResponseDto } from '../models/company-folder-response-dto';
import { documentControllerDownloadCompanyDocument } from '../fn/document-download/document-controller-download-company-document';
import { DocumentControllerDownloadCompanyDocument$Params } from '../fn/document-download/document-controller-download-company-document';
import { documentControllerRequestCompanyDocumentFolders } from '../fn/document-download/document-controller-request-company-document-folders';
import { DocumentControllerRequestCompanyDocumentFolders$Params } from '../fn/document-download/document-controller-request-company-document-folders';
import { documentControllerRequestCompanyDocumentList } from '../fn/document-download/document-controller-request-company-document-list';
import { DocumentControllerRequestCompanyDocumentList$Params } from '../fn/document-download/document-controller-request-company-document-list';

@Injectable({ providedIn: 'root' })
export class DocumentDownloadService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `documentControllerRequestCompanyDocumentList()` */
  static readonly DocumentControllerRequestCompanyDocumentListPath = '/document/list';

  /**
   * Retrieves a list of company documents from the database for a single company given the company's register
   * information.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `documentControllerRequestCompanyDocumentList()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  documentControllerRequestCompanyDocumentList$Response(params: DocumentControllerRequestCompanyDocumentList$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<CompanyDocumentsResponseDto>>> {
    return documentControllerRequestCompanyDocumentList(this.http, this.rootUrl, params, context);
  }

  /**
   * Retrieves a list of company documents from the database for a single company given the company's register
   * information.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `documentControllerRequestCompanyDocumentList$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  documentControllerRequestCompanyDocumentList(params: DocumentControllerRequestCompanyDocumentList$Params, context?: HttpContext): Observable<Array<CompanyDocumentsResponseDto>> {
    return this.documentControllerRequestCompanyDocumentList$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<CompanyDocumentsResponseDto>>): Array<CompanyDocumentsResponseDto> => r.body)
    );
  }

  /** Path part for operation `documentControllerRequestCompanyDocumentFolders()` */
  static readonly DocumentControllerRequestCompanyDocumentFoldersPath = '/document/folders';

  /**
   * Retrieves a folder structure list of company documents from the database for a single company given the company's register
   * information.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `documentControllerRequestCompanyDocumentFolders()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  documentControllerRequestCompanyDocumentFolders$Response(params: DocumentControllerRequestCompanyDocumentFolders$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<CompanyFolderResponseDto>>> {
    return documentControllerRequestCompanyDocumentFolders(this.http, this.rootUrl, params, context);
  }

  /**
   * Retrieves a folder structure list of company documents from the database for a single company given the company's register
   * information.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `documentControllerRequestCompanyDocumentFolders$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  documentControllerRequestCompanyDocumentFolders(params: DocumentControllerRequestCompanyDocumentFolders$Params, context?: HttpContext): Observable<Array<CompanyFolderResponseDto>> {
    return this.documentControllerRequestCompanyDocumentFolders$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<CompanyFolderResponseDto>>): Array<CompanyFolderResponseDto> => r.body)
    );
  }

  /** Path part for operation `documentControllerDownloadCompanyDocument()` */
  static readonly DocumentControllerDownloadCompanyDocumentPath = '/document/download/{id}';

  /**
   * Downloads a document of a company given its id and company register information.
   *
   * It is mandatory that the document exists in the document tree file index. If the document is not available on the
   * S3 storage this method will fallback to download the document from the handelsregister. Downloading the file from
   * the handelsregister will take substantially more time. Finally, the document is stored on the S3 and the S3 key is
   * upserted to the database.
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `documentControllerDownloadCompanyDocument()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  documentControllerDownloadCompanyDocument$Response(params: DocumentControllerDownloadCompanyDocument$Params, context?: HttpContext): Observable<StrictHttpResponse<Blob>> {
    return documentControllerDownloadCompanyDocument(this.http, this.rootUrl, params, context);
  }

  /**
   * Downloads a document of a company given its id and company register information.
   *
   * It is mandatory that the document exists in the document tree file index. If the document is not available on the
   * S3 storage this method will fallback to download the document from the handelsregister. Downloading the file from
   * the handelsregister will take substantially more time. Finally, the document is stored on the S3 and the S3 key is
   * upserted to the database.
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `documentControllerDownloadCompanyDocument$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  documentControllerDownloadCompanyDocument(params: DocumentControllerDownloadCompanyDocument$Params, context?: HttpContext): Observable<Blob> {
    return this.documentControllerDownloadCompanyDocument$Response(params, context).pipe(
      map((r: StrictHttpResponse<Blob>): Blob => r.body)
    );
  }

}
