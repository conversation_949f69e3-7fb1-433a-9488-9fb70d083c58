import { UserListHelperService } from '@fincloud/core/services';
import { TEMPLATE_FIELD_KEYS } from '@fincloud/core/types';
import {
  selectCustomerKey,
  selectCustomerType,
  selectIsRealEstateCustomer,
  selectIsVolksbankGroupWithBmsSales,
} from '@fincloud/state/customer';
import { selectUserCustomerKey, selectUserToken } from '@fincloud/state/user';
import { BUSINESS_CASE_DASHBOARD_FEATURE_KEY } from '@fincloud/state/utils';
import { Criteria } from '@fincloud/swagger-generator/application';
import {
  Invitation,
  ParticipantCasePermissionSetEntity,
} from '@fincloud/swagger-generator/business-case-manager';
import {
  BusinessCaseParticipantCustomer,
  Company,
  ExchangeBusinessCase,
  InformationRecord,
} from '@fincloud/swagger-generator/exchange';
import {
  BusinessCasePermission,
  BusinessCaseType,
  CustomerType,
  FinancingRepaidState,
  FinancingStructureType,
  InvitationStatus,
  ParticipationType,
} from '@fincloud/types/enums';
import {
  BusinessCaseDashboardState,
  ChartData,
  FacilityFieldViewModel,
  UserToken,
} from '@fincloud/types/models';
import {
  BUSINESS_CASE_TYPE_LABELS,
  PERCEPTION_TRANSLATIONS_MAP,
} from '@fincloud/utils';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { isNil, keyBy, values } from 'lodash-es';
import { buildFinancingRepaid } from '../utils/build-financing-repaid';
import { buildPerceptionOptions } from '../utils/perception-options';
import { transformChartData } from '../utils/transform-chart-data';
import { selectActiveIndexFinanceRouterPath } from './navigation.selectors';

export const selectBusinessCaseDashboardState =
  createFeatureSelector<BusinessCaseDashboardState>(
    BUSINESS_CASE_DASHBOARD_FEATURE_KEY,
  );

export const selectBusinessCase = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): ExchangeBusinessCase | null => {
    return state.businessCase;
  },
);

export const selectParticipantsCustomer = createSelector(
  selectBusinessCase,
  (businessCase: ExchangeBusinessCase) => {
    return businessCase?.participants;
  },
);

export const selectBusinessCaseState = createSelector(
  selectBusinessCase,
  (businessCase: ExchangeBusinessCase) => businessCase?.state,
);

export const selectBusinessCaseType = createSelector(
  selectBusinessCase,
  (businessCase: ExchangeBusinessCase): BusinessCaseType =>
    businessCase?.businessCaseType as BusinessCaseType,
);

export const selectBusinessCaseLeadCustomerKey = createSelector(
  selectBusinessCase,
  (businessCase: ExchangeBusinessCase): string => businessCase?.leadCustomerKey,
);

export const selectIsUserPartnerLead = createSelector(
  selectBusinessCaseLeadCustomerKey,
  selectUserCustomerKey,
  (leadCustomerKey: string, userCustomerKey: string): boolean => {
    return leadCustomerKey === userCustomerKey;
  },
);

export const selectEditTemplateMode = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): boolean => {
    return state.editMode;
  },
);

export const selectHasCustomerDataExportToken = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): boolean =>
    state.hasCustomerDataExportToken,
);

export const selectDataExportForCaseBeenAllowed = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): boolean =>
    state.hasDataExportBeenAllowed,
);

export const selectDataExportConditionsMet = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): boolean =>
    state.areConditionForDataExportMet,
);

export const selectIsFetchingDataExportPreconditions = createSelector(
  selectDataExportConditionsMet,
  (areConditionsMet: boolean): boolean => areConditionsMet === null,
);

export const selectUsersById = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => {
    return state.userNamesById;
  },
);

export const selectCustomerNamesByKey = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => {
    return state.customerNamesByKey;
  },
);

export const selectNonLeadPartners = createSelector(
  selectParticipantsCustomer,
  (participants: BusinessCaseParticipantCustomer[]) => {
    return participants?.filter((p) => !p.lead) ?? [];
  },
);

export const selectActiveParticipants = createSelector(
  selectNonLeadPartners,
  (participants: BusinessCaseParticipantCustomer[]) => {
    return participants?.every((p) => p.state)
      ? participants.filter((p) => p.state === 'ACTIVE')
      : participants;
  },
);

export const selectCriteria = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): Criteria[] => {
    return state.criteria;
  },
);

export const selectLeadPartner = createSelector(
  selectParticipantsCustomer,
  (participants: BusinessCaseParticipantCustomer[]) => {
    return participants?.find((p) => p.lead) ?? null;
  },
);

export const selectLastVisitedUrl = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => {
    return state.lastVisitedUrl ?? '';
  },
);

export const selectIsBusinessCaseActive = createSelector(
  selectBusinessCaseState,
  (businessCaseState): boolean => {
    return (
      businessCaseState === 'ACTIVE_PRIVATE' ||
      businessCaseState === 'ACTIVE_PUBLIC'
    );
  },
);

// TODO: WAIT FOR BE
export const selectBusinessCaseTypeLabel = createSelector(
  selectBusinessCaseDashboardState,
  (): string => {
    return BUSINESS_CASE_TYPE_LABELS['FINANCING_CASE'] ?? '';
  },
);

export const selectIsBusinessCaseHeaderInView = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): boolean => {
    return state.isCaseHeaderInView;
  },
);

export const selectIsBusinessCaseTypeFinancing = createSelector(
  selectBusinessCaseType,
  (businessCaseType) => {
    return businessCaseType === BusinessCaseType.FINANCING_CASE;
  },
);

export const selectIsEmployeeOfLeadCustomer = createSelector(
  selectBusinessCase,
  selectUserToken,
  (businessCase: ExchangeBusinessCase, token: UserToken): boolean => {
    return businessCase && token
      ? token.customer_key === businessCase.leadCustomerKey
      : false;
  },
);

export const selectIsRepresentingCurrentUserCustomer = createSelector(
  selectBusinessCase,
  selectUserToken,
  (businessCase: ExchangeBusinessCase, token: UserToken): boolean => {
    return businessCase && token
      ? !!businessCase.participants
          .find((p) => p.customerKey === token.customer_key)
          ?.users.some((u) => u.userId === token.sub)
      : false;
  },
);

export const selectCustomerBusinessCaseContext = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): ParticipantCasePermissionSetEntity => {
    return state.customerContext;
  },
);

export const selectBusinessCaseId = createSelector(
  selectBusinessCase,
  selectCustomerBusinessCaseContext,
  (businessCase, customerContext): string => {
    return businessCase?.id || customerContext?.businessCaseId;
  },
);

export const selectBusinessCaseCompanyId = createSelector(
  selectBusinessCase,
  (businessCase: ExchangeBusinessCase): string => {
    return businessCase?.companyId;
  },
);

export const selectBusinessCaseCustomerPermissions = createSelector(
  selectCustomerBusinessCaseContext,
  (customerContext): string[] => customerContext?.permissions || [],
);

export const selectHasBusinessCasePermission = (permissionCode: string) =>
  createSelector(
    selectBusinessCaseCustomerPermissions,
    (permissions): boolean => {
      return permissions.includes(permissionCode);
    },
  );

export const selectHasAnyBusinessCasePermission = (
  permissionsToCheck: string[],
) =>
  createSelector(
    selectBusinessCaseCustomerPermissions,
    (customerContextPermissions): boolean => {
      return permissionsToCheck.some((permissionCode) =>
        customerContextPermissions.includes(permissionCode),
      );
    },
  );

export const selectCustomerContextWithBusinessCase = createSelector(
  selectCustomerBusinessCaseContext,
  selectBusinessCase,
  (customerCtx, businessCase) => ({ customerCtx, businessCase }),
);

export const selectBusinessCaseContactPersonIds = createSelector(
  selectBusinessCase,
  (businessCase: ExchangeBusinessCase) => businessCase?.contactPersonIds,
);

export const selectCustomerParticipantsUsers = createSelector(
  selectParticipantsCustomer,
  selectUserToken,
  selectBusinessCaseContactPersonIds,
  selectUsersById,
  (businessCaseParticipants, userToken, contactPersonIds, usersById) => {
    return (
      businessCaseParticipants
        .find(
          (participantCustomer) =>
            participantCustomer.customerKey === userToken.customer_key,
        )
        ?.users.filter(
          (participantCustomerUser) =>
            !contactPersonIds.includes(participantCustomerUser.userId),
        )
        .map((participantCustomerUser) => participantCustomerUser.userId)
        .map((userId) => usersById[userId])
        .filter(Boolean) ?? []
    );
  },
);

export const selectIsParticipationTypeLeader = createSelector(
  selectCustomerBusinessCaseContext,
  (state: ParticipantCasePermissionSetEntity) => {
    return (
      (state?.participationType as ParticipationType) ===
      ParticipationType.LEADER
    );
  },
);

export const selectIsParticipationTypeCollaborator = createSelector(
  selectCustomerBusinessCaseContext,
  (state) => {
    return state?.participationType === ParticipationType.COLLABORATOR;
  },
);

const selectParticipantsPermissions = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => state?.participantsPermissions,
);

export const selectBusinessCaseParticipantsPermissions = createSelector(
  selectParticipantsPermissions,
  (participantsPermissions: ParticipantCasePermissionSetEntity[]) => {
    if (isNil(participantsPermissions)) {
      return {};
    }
    return keyBy(participantsPermissions, 'customerKey');
  },
);

export const selectStructuredFinancingConfiguration = createSelector(
  selectBusinessCase,
  (businessCase) => {
    return businessCase?.structuredFinancingConfiguration;
  },
);

export const selectFinancingStructureType = createSelector(
  selectStructuredFinancingConfiguration,
  (structuredFinancingConfiguration) => {
    return structuredFinancingConfiguration?.financingStructureType;
  },
);

export const selectIsBusinessCaseRealEstate = createSelector(
  selectFinancingStructureType,
  (financingStructureType) => {
    return financingStructureType === FinancingStructureType.REAL_ESTATE;
  },
);

export const selectIsBusinessCaseMiscellaneous = createSelector(
  selectFinancingStructureType,
  (financingStructureType) => {
    return financingStructureType === FinancingStructureType.MISCELLANEOUS;
  },
);

export const selectIsBusinessCaseCorporate = createSelector(
  selectFinancingStructureType,
  (financingStructureType) => {
    return financingStructureType === FinancingStructureType.CORPORATE;
  },
);

export const selectBusinessCaseBreakdown = createSelector(
  selectBusinessCase,
  selectUserToken,
  selectCustomerNamesByKey,
  selectBusinessCaseParticipantsPermissions,
  (businessCase, userToken, customerNamesByKey, participantsPermissions) => {
    return {
      businessCase,
      userToken,
      customerKeysNames: Object.values(customerNamesByKey),
      participantsPermissions,
    };
  },
);

export const selectBusinessCaseInformation = createSelector(
  selectBusinessCase,
  (businessCase) => {
    return businessCase?.information;
  },
);

export const selectBusinessCaseInformationValues = createSelector(
  selectBusinessCaseInformation,
  (businessCaseInformation) => values(businessCaseInformation),
);

export const selectBusinessCaseInformationById = createSelector(
  selectBusinessCaseInformationValues,
  (businessCaseInformationValues) => {
    return keyBy(businessCaseInformationValues, 'id');
  },
);

export const selectBusinessCaseParticipationType = createSelector(
  selectCustomerBusinessCaseContext,
  (state: ParticipantCasePermissionSetEntity) => {
    return state?.participationType as ParticipationType;
  },
);

export const selectCurrentUserPartner = createSelector(
  selectParticipantsCustomer,
  selectUserCustomerKey,
  (participants: BusinessCaseParticipantCustomer[], userTokenCustomerKey) => {
    return (
      participants?.find((p) => p.customerKey === userTokenCustomerKey) ?? null
    );
  },
);

export const selectDocumentsFiles = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => {
    return state?.documentsFiles;
  },
);

export const selectAutoGeneratedBusinessCaseName = createSelector(
  selectBusinessCase,
  (businessCase: ExchangeBusinessCase) => {
    return businessCase?.autoGeneratedBusinessCaseName;
  },
);

export const selectCanSeeTopicChats = createSelector(
  selectHasBusinessCasePermission(BusinessCasePermission.BCP_00131),
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00051]),
  (hasPermissionToCreate, hasPermissionToView) => {
    return hasPermissionToCreate && hasPermissionToView;
  },
);

export const selectCollaborationTab = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => {
    return state.selectedCollaborationTab;
  },
);

export const selectHasPermissionToSeeParticipationTabCorporateRealEstate =
  createSelector(
    selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00061]),
    selectHasAnyBusinessCasePermission([
      BusinessCasePermission.BCP_00140,
      BusinessCasePermission.BCP_00063,
    ]),
    selectBusinessCaseParticipationType,
    (hasPermission, manageMyParticipation, participationType) => {
      return participationType === ParticipationType.LEADER
        ? hasPermission && manageMyParticipation
        : hasPermission;
    },
  );

export const selectHasPermissionsToSeeFinancingStructureTabCorporateRealEstate =
  createSelector(
    selectHasAnyBusinessCasePermission([
      BusinessCasePermission.BCP_00064,
      BusinessCasePermission.BCP_00066,
    ]),
    (hasPermission) => {
      return hasPermission;
    },
  );

export const selectFacilityFields = createSelector(
  selectBusinessCaseDashboardState,
  (state) => state.facilities,
);

export const selectFacilities = createSelector(
  selectFacilityFields,
  (fields) => {
    const facilities = (fields ?? []).map((f) => {
      const facilityFields: FacilityFieldViewModel[] = [...f.facilityFields];
      return { ...f, facilityFields };
    });

    return facilities;
  },
);

export const selectFinancingVolumeCorporate = createSelector(
  selectIsBusinessCaseCorporate,
  selectFacilities,
  (isCorporate, facilities) => {
    if (!isCorporate || isNil(facilities) || facilities.length === 0) {
      return 0;
    }

    const value = Number(
      facilities
        .map((facility) => facility.facilityFields)
        ?.flat()
        .find((field) => field.key === TEMPLATE_FIELD_KEYS.FinancingVolume)
        ?.value,
    );
    return isNaN(value) ? 0 : value;
  },
);

export const selectAmountToBeCollectedCorporate = createSelector(
  selectCurrentUserPartner,
  selectFinancingVolumeCorporate,
  (ownAmount, financingVolume) =>
    financingVolume -
    (!isNil(ownAmount) ? ownAmount.totalParticipationAmount : 0),
);

export const selectBreakdownStatisticsCorporate = createSelector(
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00063]),
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00140]),
  selectCustomerType,
  (viewPieChartBank, viewPieChartCorpFsp, customerType) => {
    if (customerType === CustomerType.REAL_ESTATE) {
      return false;
    }
    if (customerType === CustomerType.BANK) {
      return viewPieChartBank;
    }
    return viewPieChartCorpFsp;
  },
);

export const selectTotalParticipationAmount = createSelector(
  selectCurrentUserPartner,
  (currentUserPartner) => {
    return currentUserPartner?.totalParticipationAmount;
  },
);

export const selectChartDataCorporateMiscellaneous = createSelector(
  selectTotalParticipationAmount,
  (totalParticipationAmount) => {
    let chartData: ChartData[];
    if (!totalParticipationAmount) {
      chartData = [];
    } else {
      chartData = [
        {
          name: $localize`:@@dashboard.businessCase.collected:Gesammelt`,
          value: totalParticipationAmount,
        } as ChartData,
      ];
    }
    return transformChartData(chartData);
  },
);

export const selectFinancingVolumeMiscellaneous = createSelector(
  selectIsBusinessCaseMiscellaneous,
  selectBusinessCase,
  (isMiscellaneous, businessCase) => {
    if (!isMiscellaneous || isNil(businessCase)) {
      return 0;
    }
    const value = Number(businessCase.information.financingVolume.value);
    return isNaN(value) ? 0 : value;
  },
);

export const selectAmountToBeCollectedMiscellaneous = createSelector(
  selectCurrentUserPartner,
  selectFinancingVolumeMiscellaneous,
  (ownAmount, financingVolume) =>
    financingVolume -
    (!isNil(ownAmount) ? ownAmount.totalParticipationAmount : 0),
);

export const selectBreakdownStatisticsMiscellaneousVisibility = createSelector(
  selectHasAnyBusinessCasePermission([
    BusinessCasePermission.BCP_00063,
    BusinessCasePermission.BCP_00140,
  ]),
  selectIsBusinessCaseMiscellaneous,
  selectBusinessCaseParticipationType,
  (hasPermission, isMiscellaneousCase, participationType) => {
    if (
      !isMiscellaneousCase ||
      ![ParticipationType.LEADER, ParticipationType.PARTICIPANT].includes(
        participationType,
      )
    ) {
      return false;
    }
    return hasPermission;
  },
);

export const selectMirroredFieldKeys = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => state?.mirroredFieldKeys,
);

export const selectMirroredCalculatableFieldKeys = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => state?.calculatableMirroredFieldKeys,
);

export const selectIsLead = createSelector(
  selectBusinessCaseLeadCustomerKey,
  selectCustomerKey,
  (leadCustomerKey, customerKey: string) => {
    return leadCustomerKey === customerKey;
  },
);

export const selectIsSubCase = createSelector(
  selectBusinessCase,
  (businessCase: ExchangeBusinessCase) => {
    return !!businessCase?.masterBusinessCaseId;
  },
);

export const selectHasSyncedFields = createSelector(
  selectBusinessCaseInformation,
  (businessCaseInformation: InformationRecord): boolean => {
    return Object.values(businessCaseInformation || {}).some(
      (informationField) => informationField?.synced,
    );
  },
);

export const selectLinkedFieldsBannerVisibility = createSelector(
  selectIsSubCase,
  selectEditTemplateMode,
  selectHasSyncedFields,
  (isSubCase, editTemplate, hasSyncedFields) => {
    return isSubCase && editTemplate && hasSyncedFields;
  },
);

export const selectLinkedFieldsBannerVisibilityFS = createSelector(
  selectLinkedFieldsBannerVisibility,
  selectIsLead,
  (shouldShowBanner, isLead) => {
    return shouldShowBanner && isLead;
  },
);

export const selectParticipationMaxAmount = createSelector(
  selectBusinessCase,
  (businessCase) => {
    return businessCase?.maxParticipationAmount;
  },
);

export const selectParticipationMinAmount = createSelector(
  selectBusinessCase,
  (businessCase) => {
    return businessCase?.minParticipationAmount;
  },
);

export const selectCanManageBusinessCase = createSelector(
  selectHasAnyBusinessCasePermission([
    BusinessCasePermission.BCP_00030, // manage contact person in the case
    BusinessCasePermission.BCP_00031, // manage users in case
    BusinessCasePermission.BCP_00033, // manage FAQ
    BusinessCasePermission.BCP_00034, // complete reactivate case
    BusinessCasePermission.BCP_00037, // duplicate case
    BusinessCasePermission.BCP_00038, // show CADR in the case
  ]),
  (hasPermissions) => {
    return hasPermissions;
  },
);

export const selectCanManageOwnCaseUsers = createSelector(
  selectHasAnyBusinessCasePermission([
    BusinessCasePermission.BCP_00031,
    BusinessCasePermission.BCP_00035, // initiate sync with dracoon
    BusinessCasePermission.BCP_00036, // initiate sync with nextfolder
  ]),
  (hasPermissions) => {
    return hasPermissions;
  },
);

// --- Merged content from business-case-refactoring selectors ---
export const selectIsBusinessCaseLoading = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): boolean => {
    return state.isBusinessCaseLoading;
  },
);
export const selectGlobalRequiredFieldKeys = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => {
    return state.globalRequiredFieldKeys ?? [];
  },
);
export const selectRefsCaseCommonFields = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => {
    return state?.commonFields || [];
  },
);

export const selectTotalInvestmentAmount = createSelector(
  selectRefsCaseCommonFields,
  (fields) => {
    const remappedFields: { [key: string]: number } = {};
    fields.forEach((field) => {
      remappedFields[field.key] = parseFloat(field.value?.toString() || '0');
    });

    return remappedFields.totalInvestmentAmount;
  },
);

export const selectIsRealEstateTabOpened = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => {
    return state?.isRealEstateTabOpened;
  },
);
export const selectCompanyName = createSelector(
  selectBusinessCase,
  (businessCase: ExchangeBusinessCase) => {
    return businessCase?.company?.companyInfo?.legalName;
  },
);
export const selectAllParticipantsCaseVisibility = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => {
    return state?.allParticipantsCaseVisibility;
  },
);
export const selectCanUserSeeOtherParticipants = createSelector(
  selectAllParticipantsCaseVisibility,
  selectIsUserPartnerLead,
  (allParticipantsCaseVisibility: boolean, isUserPartnerLead: boolean) => {
    if (allParticipantsCaseVisibility === null) {
      return null;
    }
    return isUserPartnerLead || allParticipantsCaseVisibility;
  },
);
export const selectCurrentCustomerCaseUsers = createSelector(
  selectCurrentUserPartner,
  (partner) => {
    return partner?.users ?? [];
  },
);
export const selectUserCorrespondingCompanyId = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => {
    return state.userCorrespondingCompanyId;
  },
);
export const selectBusinessCaseVisibilityForNewCustomer = createSelector(
  selectBusinessCase,
  (businessCase: ExchangeBusinessCase) => {
    return businessCase?.participantVisibilityEnabledConfig;
  },
);
export const selectBusinessCaseVisibilityForCustomer = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => {
    return state?.allParticipantsCaseVisibility;
  },
);
export const selectBusinessCaseApplications = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => {
    return state.applications;
  },
);
export const selectLeaderCustomerKey = createSelector(
  selectBusinessCase,
  (businessCase: ExchangeBusinessCase) => {
    return businessCase?.leadCustomerKey;
  },
);
export const selectDefaultRoles = createSelector(
  selectBusinessCase,
  (businessCase: ExchangeBusinessCase) => {
    return businessCase?.businessCaseRoles ?? [];
  },
);
// TODO: selectors merge
// export const selectAutoGeneratedBusinessCaseName = createSelector(
//   selectBusinessCaseDashboardState,
//   (state: BusinessCaseDashboardState) => {
//     return state.currentUsersFromMyOrganization?.autoGeneratedBusinessCaseName;
//   },
// );

export const selectCurrentUsersFromMyOrganization = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => state.currentUsersFromMyOrganization,
);

export const selectCaseParticipantsFromMyOrganization = createSelector(
  selectCurrentUsersFromMyOrganization,
  (currentUsersFromMyOrganization) => {
    const users = currentUsersFromMyOrganization?.users;
    return users?.map((user) => UserListHelperService.mapUserToTableRow(user));
  },
);
// TODO: selectors merge
// export const selectIsBusinessCaseRealEstate = createSelector(
//   selectBusinessCaseDashboardState,
//   (state: BusinessCaseDashboardState) => {
//     return (
//       state?.businessCase?.structuredFinancingConfiguration
//         ?.financingStructureType === CustomerType.REAL_ESTATE
//     );
//   },
// );

export const selectBusinessCaseStatuses = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => state.businessCaseStatuses,
);

export const selectReasonsForReactivatingCase = createSelector(
  selectBusinessCase,
  selectBusinessCaseStatuses,
  (businessCase: ExchangeBusinessCase, businessCaseStatuses: string[]) => {
    if (!businessCase) return [];
    return buildPerceptionOptions(businessCaseStatuses).map((perception) => {
      return buildFinancingRepaid(perception, true);
    });
  },
);

export const selectReasonsForClosingBusinessCase = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): string[] =>
    state.reasonsForClosingBusinessCase,
);

export const selectBusinessCaseStatusesState = createSelector(
  selectBusinessCaseStatuses,
  selectReasonsForClosingBusinessCase,
  selectIsBusinessCaseActive,
  (
    businessCaseStatuses: string[],
    reasonsForClosingBusinessCase: string[],
    isActive: boolean,
  ) => {
    if (!businessCaseStatuses) {
      return [];
    }

    if (!isActive && !reasonsForClosingBusinessCase) {
      return [];
    }
    const options = isActive
      ? businessCaseStatuses
      : reasonsForClosingBusinessCase;

    return buildPerceptionOptions(options);
  },
);

export const selectDefaultPerception = createSelector(
  selectBusinessCase,
  selectCustomerKey,
  selectIsBusinessCaseActive,
  (
    businessCase: ExchangeBusinessCase,
    customerKey: string,
    isActive: boolean,
  ) => {
    if (businessCase) {
      if (!isActive) {
        const leadParticipant = businessCase.participants?.find(
          (participant) => participant.lead,
        );
        if (
          leadParticipant?.participantPerceptionForCaseState ===
          FinancingRepaidState.FINANCING_REPAID
        ) {
          return FinancingRepaidState.FINANCING_REPAID_INACTIVE;
        }

        return leadParticipant?.participantPerceptionForCaseState;
      }

      const customer = businessCase.participants?.find(
        (participant) => participant.customerKey === customerKey,
      );

      if (
        customer?.participantPerceptionForCaseState ===
        FinancingRepaidState.FINANCING_REPAID
      ) {
        return FinancingRepaidState.FINANCING_REPAID_ACTIVE;
      }

      return customer?.participantPerceptionForCaseState;
    }
    return null;
  },
);
export const selectReasonForClosedCase = createSelector(
  selectDefaultPerception,
  (perception) => {
    return PERCEPTION_TRANSLATIONS_MAP.get(perception);
  },
);
export const selectReasonsForClosingCase = createSelector(
  selectReasonsForClosingBusinessCase,
  (reasonsForClosingBusinessCase: string[]) => {
    return buildPerceptionOptions(reasonsForClosingBusinessCase).map(
      (perception) => {
        return buildFinancingRepaid(perception, false);
      },
    );
  },
);

const selectInvitations = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): Invitation[] => state.invitations,
);

export const selectParticipantHasAcceptedInvitation = createSelector(
  selectInvitations,
  selectBusinessCase,
  selectCustomerKey,
  (
    invitations: Invitation[],
    businessCase: ExchangeBusinessCase,
    customerKey: string,
  ) => {
    const invitedCustomer = invitations.find(
      (invitation) =>
        invitation.invitedCustomerKey === customerKey &&
        invitation.businessCaseId === businessCase?.id,
    );

    return invitedCustomer?.invitationStatus === InvitationStatus.ACCEPTED;
  },
);

export const selectIsPartOfParticipants = createSelector(
  selectParticipantsCustomer,
  selectCustomerKey,
  (participants: BusinessCaseParticipantCustomer[], customerKey: string) => {
    return !!participants?.find(
      (participant) => participant.customerKey === customerKey,
    );
  },
);

export const selectBusinessCaseDashboardCardData = createSelector(
  selectParticipantHasAcceptedInvitation,
  selectBusinessCaseStatusesState,
  selectDefaultPerception,
  selectReasonForClosedCase,
  selectIsLead,
  selectIsBusinessCaseActive,
  selectUserCorrespondingCompanyId,
  selectIsPartOfParticipants,
  (
    participantHasBeenAccepted,
    statusOptions,
    defaultPerception,
    reasonForClosedState,
    isLead,
    isCaseActive,
    ownCompanyId,
    isPartOfParticipants,
  ) => {
    const modifiedStatusOptions = statusOptions.map((statusOption) => {
      return buildFinancingRepaid(statusOption, isCaseActive);
    });

    return {
      statusOptions: modifiedStatusOptions,
      defaultPerception,
      reasonForClosedState,
      isLead,
      isCaseActive,
      ownCompanyId,
      participantHasBeenAccepted,
      isPartOfParticipants,
    };
  },
);
export const selectHasPermissionToSeeMyParticipation = createSelector(
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00061]),
  selectHasAnyBusinessCasePermission([
    BusinessCasePermission.BCP_00140,
    BusinessCasePermission.BCP_00063,
  ]),
  selectBusinessCaseParticipationType,
  (hasPermission, visibilityOn, participationType) => {
    return participationType === ParticipationType.LEADER
      ? hasPermission && visibilityOn
      : hasPermission;
  },
);
export const selectBusinessCaseContactInfoData = createSelector(
  selectBusinessCase,
  selectCustomerNamesByKey,
  selectUsersById,
  (businessCase, customerNamesByKeys, usersByIds) => ({
    businessCase,
    customerNamesByKeys,
    usersByIds,
  }),
);
export const selectIsCADRLinked = createSelector(
  selectBusinessCase,
  (businessCase) => businessCase?.isCADRLinked,
);
export const selectBusinessCaseCompany = createSelector(
  selectBusinessCase,
  (businessCase: ExchangeBusinessCase) => businessCase?.company,
);

export const selectBusinessCaseCompanyInfo = createSelector(
  selectBusinessCaseCompany,
  (company: Company) => company?.companyInfo,
);

export const selectCompanyId = createSelector(
  selectBusinessCaseCompany,
  (company: Company) => company?.id,
);
export const selectBusinessCaseDataRoomTab = createSelector(
  selectBusinessCaseId,
  selectCustomerKey,
  selectCompanyId,
  (businessCaseId, customerKey, companyId) => ({
    businessCaseId,
    customerKey,
    companyId,
  }),
);
export const selectStatisticsCorporateRealEstateVisibility = createSelector(
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00063]),
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00140]),
  selectCustomerType,
  (viewPieChartBank, viewPieChartCorpFsp, customerType) => {
    if (customerType === CustomerType.REAL_ESTATE) {
      return false;
    }
    if (customerType === CustomerType.BANK) {
      return viewPieChartBank;
    }
    return viewPieChartCorpFsp;
  },
);
export const selectParticipationTabMiscellaneousVisibility = createSelector(
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00063]),
  selectCustomerType,
  (manageMyParticipation, customerType) => {
    return manageMyParticipation && customerType === CustomerType.BANK;
  },
);
export const selectEditModeWithPermission = createSelector(
  selectEditTemplateMode,
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00065]),
  (isEditMode, hasPermissionForFinancingCase) => {
    return isEditMode && hasPermissionForFinancingCase;
  },
);

export const selectSingleNorthDataCompanies = createSelector(
  selectBusinessCaseDashboardState,
  (state) => state.singleCompanyNorthDataCompanies,
);

export const selectMultiNorthDataCompanies = createSelector(
  selectBusinessCaseDashboardState,
  (state) => state.multiCompanyNorthDataCompanies,
);

export const selectHighlighted = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => state.highlighted,
);

export const selectShowExportAllowSectionInCase = createSelector(
  selectIsVolksbankGroupWithBmsSales,
  selectIsBusinessCaseRealEstate,
  (hasVolksBankTypeAndBmsPermission, isBusinessCaseRealEstate): boolean =>
    hasVolksBankTypeAndBmsPermission && isBusinessCaseRealEstate,
);

export const selectCanAllowDataExport = createSelector(
  selectBusinessCaseDashboardState,
  (state): boolean => state.areConditionForDataExportMet,
);
export const selectBusinessCaseStatusOptions = createSelector(
  selectBusinessCaseDashboardCardData,
  (businessCaseDashboardCardData) =>
    businessCaseDashboardCardData.statusOptions,
);
export const selectBusinessCaseDefaultPerception = createSelector(
  selectBusinessCaseDashboardCardData,
  (businessCaseDashboardCardData) =>
    businessCaseDashboardCardData.defaultPerception,
);

export const selectBusinessCaseCurrentStatus = createSelector(
  selectBusinessCaseStatusOptions,
  selectBusinessCaseDefaultPerception,
  (statusOptions, defaultPerception) => {
    return statusOptions?.find((item) => item.value === defaultPerception)
      ?.label;
  },
);

export const selectCanShowDataExportTips = createSelector(
  selectIsVolksbankGroupWithBmsSales,
  selectHasCustomerDataExportToken,
  (isVolksbankWithBmsChannel, hasActiveDataExportCustomerToken) => {
    return isVolksbankWithBmsChannel && hasActiveDataExportCustomerToken;
  },
);

export const selectDocumentFieldCategories = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => state.documentFieldCategories,
);

export const selectDocumentFieldCategoryOptions = createSelector(
  selectDocumentFieldCategories,
  (documentFieldCategories) =>
    documentFieldCategories?.map(({ id, name }) => ({
      label: name,
      value: id,
    })) ?? [],
);

export const selectFinancingDetailsCorporateView = createSelector(
  selectEditTemplateMode,
  selectActiveIndexFinanceRouterPath,
  selectIsRealEstateCustomer,
  selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00065]),
  (editMode, activeIndex, isRealEstateCustomer, hasEditPermission) => {
    return {
      editMode,
      isRealEstateCustomer,
      hasEditPermission,
      activeIndex: activeIndex > 0 ? activeIndex - 1 : activeIndex,
      // activeIndex is build from own-fs , shared-fs and participation. Shared is only availbale for Real Estate
      // therefor possible views in our case are 0 (own-fs) and 1(participation which is at index 2)
    };
  },
);
