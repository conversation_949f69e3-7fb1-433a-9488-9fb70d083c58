import { selectIsFormerParticipant } from '@fincloud/state/access';
// TODO: circular dependency

import { selectUserToken } from '@fincloud/state/user';
import { BUSINESS_CASE_DASHBOARD_FEATURE_KEY } from '@fincloud/state/utils';
import { Application } from '@fincloud/swagger-generator/application';
import { ApplicationState, InvitationStatus } from '@fincloud/types/enums';
import { BusinessCaseDashboardState, UserToken } from '@fincloud/types/models';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { find, orderBy, uniqBy } from 'lodash-es';
import {
  selectCurrentUserPartner,
  selectIsUserPartnerLead,
} from './business-case.selectors';
import { selectSelectedInvitation } from './invitations.selectors';

const selectBusinessCaseDashboardState =
  createFeatureSelector<BusinessCaseDashboardState>(
    BUSINESS_CASE_DASHBOARD_FEATURE_KEY,
  );

const selectStateApplications = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): Application[] => state.applications,
);

export const selectApplications = createSelector(
  selectStateApplications,
  selectIsFormerParticipant,
  (
    applications: Application[],
    isFormerParticipant: boolean,
  ): Application[] => {
    const dedupedApplications = uniqBy(
      orderBy(applications ?? [], (a) => a.startedOn, 'desc'),
      (a) => a.customerKey,
    );
    return dedupedApplications.filter((application) => {
      const shouldIgnoreApplication =
        isFormerParticipant && application.state === ApplicationState.ACCEPTED;

      if (shouldIgnoreApplication) {
        // Allow re-apply flow for former participants
        return false;
      }
      return true;
    });
  },
);

export const selectApplicationsLoaded = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): boolean => {
    return state.applicationsLoaded;
  },
);

const selectInvitationsLoaded = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): boolean => {
    return state.invitationsLoaded;
  },
);

export const selectAreApplicationsAndInvitationsLoaded = createSelector(
  selectApplicationsLoaded,
  selectInvitationsLoaded,
  (applicationsLoaded, invitationsLoaded): boolean => {
    return !!applicationsLoaded && !!invitationsLoaded;
  },
);

export const selectAcceptedApplication = createSelector(
  selectApplications,
  selectUserToken,
  (applications: Application[], token: UserToken): Application => {
    return find(applications, {
      state: 'ACCEPTED',
      customerKey: token?.customer_key,
    });
  },
);

export const selectSubmittedApplication = createSelector(
  selectApplications,
  selectUserToken,
  (applications: Application[], token: UserToken): Application => {
    return find(applications, {
      state: 'SUBMITTED',
      customerKey: token?.customer_key,
    });
  },
);

export const selectSelectedApplication = createSelector(
  selectApplications,
  selectUserToken,
  (applications: Application[], token: UserToken): Application => {
    return find(applications, {
      customerKey: token?.customer_key,
    });
  },
);

export const selectApplicationLoaded = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): Application => {
    return state.applicationLoaded;
  },
);

export const selectShowApplicationView = createSelector(
  selectCurrentUserPartner,
  selectSelectedInvitation,
  selectIsUserPartnerLead,
  (participant, invitation, isLead) => {
    if ((!invitation || invitation.applicationId) && !participant && !isLead) {
      return true;
    }
    return [InvitationStatus.DECLINED, InvitationStatus.CANCELED].includes(
      invitation?.invitationStatus as InvitationStatus,
    );
  },
);
