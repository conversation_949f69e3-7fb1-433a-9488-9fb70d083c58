import { FinTreeNode } from '@fincloud/ui/tree-menu';

export function collapseNodeIfParentNodeIsCollapse<T>(
  node: FinTreeNode<T>,
): FinTreeNode<T> {
  const cloned = structuredClone(node);

  const processNode = (node: FinTreeNode<T>) => {
    if (!node.expanded) {
      // If this node is collapsed, collapse all its children
      collapseChildren(node);
    } else if (node.children) {
      // Continue traversing the tree
      node.children.forEach((child) => processNode(child));
    }
  };

  const collapseChildren = (node: FinTreeNode<T>) => {
    if (node.children) {
      node.children.forEach((child) => {
        child.expanded = false;
        collapseChildren(child);
      });
    }
  };

  processNode(cloned);

  return cloned;
}
