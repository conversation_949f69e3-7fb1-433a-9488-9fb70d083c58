import { COMPANY_ANALYSIS_FEATURE_KEY } from '@fincloud/state/utils';
import { Company } from '@fincloud/swagger-generator/company';
import {
  CompanyAnalysisState,
  DataRoomTemplateFieldData,
  UploadFilesBreakdown,
} from '@fincloud/types/models';
import { createFeatureSelector, createSelector } from '@ngrx/store';

const selectCompanyAnalysisState = createFeatureSelector<CompanyAnalysisState>(
  COMPANY_ANALYSIS_FEATURE_KEY,
);

export const selectCompanyId = createSelector(
  selectCompanyAnalysisState,
  (state: CompanyAnalysisState) => {
    return state.companyId;
  },
);

export const selectCompany = createSelector(
  selectCompanyAnalysisState,
  (state: CompanyAnalysisState) => {
    return state.company;
  },
);

export const selectCompanyCustomerKey = createSelector(
  selectCompany,
  (company: Company) => {
    return company?.customerKey;
  },
);

export const selectCompanyDataRoomInformation = createSelector(
  selectCompanyAnalysisState,
  (state: CompanyAnalysisState) => {
    return state.information;
  },
);

export const selectCustomerCadrTemplate = createSelector(
  selectCompanyAnalysisState,
  (state: CompanyAnalysisState) => {
    return state.cadrTemplate;
  },
);

export const selectDocumentFieldCategories = createSelector(
  selectCompanyAnalysisState,
  (state: CompanyAnalysisState) => {
    return state?.documentFieldCategories;
  },
);

export const selectDocumentFieldCategoryOptions = createSelector(
  selectDocumentFieldCategories,
  (categories) =>
    categories?.map(({ name, id }) => ({
      label: name,
      value: id,
    })) ?? [],
);

export const selectCompanyDataRoomUploadFilesBreakdownList = createSelector(
  selectCompanyAnalysisState,
  (state: CompanyAnalysisState): UploadFilesBreakdown[] => {
    return state.uploadFilesBreakdownList;
  },
);

export const selectOwnCompanyDataRoomTemplateFieldData = createSelector(
  selectCompany,
  selectCompanyDataRoomUploadFilesBreakdownList,
  (company, uploadingFilesBreakdownList): DataRoomTemplateFieldData => ({
    dataRoomOwnerName: company.companyInfo.legalName,
    dataRoomOwnerId: company.id,
    uploadingFilesBreakdownList,
    isCADR: true,
    hasDataRoomWriteAccess: true,
    fieldActionsConfig: {
      supportsChat: false,
      supportsFieldDescription: true,
      supportsParticipantFieldRequest: false,
      supportsFieldVisibility: false,
      supportsFolderStructure: false,
    },
  }),
);
