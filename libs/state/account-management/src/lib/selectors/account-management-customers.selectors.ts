import {
  selectIsAccountManager,
  selectUserCustomerKey,
} from '@fincloud/state/user';
import {
  Customer,
  InternalPortalCustomerDto,
} from '@fincloud/swagger-generator/authorization-server';
import {
  CadrTemplate,
  Template,
} from '@fincloud/swagger-generator/business-case-manager';
import { CadrTemplateDto } from '@fincloud/swagger-generator/company';
import { CustomerStatus } from '@fincloud/types/enums';
import {
  AccountManagementCustomerFilters,
  AccountManagementState,
  CustomerWithUsername,
  FluidTableSorting,
  SnapshotCustomerAdditionalInfo,
} from '@fincloud/types/models';
import { createSelector } from '@ngrx/store';
import { selectAccountManagementState } from './account-management.selectors';

export const selectSelectedCustomer = createSelector(
  selectAccountManagementState,
  (state): InternalPortalCustomerDto => state?.selectedCustomer,
);

export const selectCustomerList = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): CustomerWithUsername[] => state.customerList,
);

export const selectIsCustomersLoading = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): boolean => state.isCustomersLoading,
);

export const selectCustomersAdditionalInfo = createSelector(
  selectAccountManagementState,
  (
    state: AccountManagementState,
  ): {
    [customerKey: string]: SnapshotCustomerAdditionalInfo;
  } => state.customersAdditionalInfo,
);

export const selectRegularCustomerCount = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): number => state.regularCustomerCount,
);

export const selectGuestCustomerCount = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): number => state.regularCustomerCount,
);

export const selectTotalCustomersCount = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): number => state.totalCustomersCount,
);

export const selectRegularCustomersSorting = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): FluidTableSorting =>
    state.selectedRegularCustomersSorting,
);

export const selectGuestCustomersSorting = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): FluidTableSorting =>
    state.selectedGuestCustomersSorting,
);

export const selectCustomerStatus = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): CustomerStatus => state.customerStatus,
);

export const selectCustomerFilters = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): AccountManagementCustomerFilters =>
    state.customerFilters,
);

export const selectGetAllBusinessCaseTemplates = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): Template[] =>
    state?.allBusinessCaseTemplates,
);

export const selectGetAllCadrTemplates = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): CadrTemplate => state?.cadrTemplate,
);

export const selectGetIsCadrSelected = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): boolean => state?.isCadrSelected,
);

export const selectGetSelectedCustomerBusinessCase = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): Template | CadrTemplateDto =>
    state?.selectedBusinessCase,
);

export const selectIsCadrCreatedForAccountManager = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): boolean => state?.hasCadr,
);

export const selectIsNewTemplateModeAccountManager = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): boolean => state?.isNewTemplateMode,
);

export const selectIsCreateOrEditLoading = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): boolean => state.isCreateOrEditLoading,
);

export const selectSelectedCustomerLogo = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): string => state.selectedCustomerLogo,
);

export const selectSelectedManagedCustomer = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): Customer => state.selectedManagedCustomer,
);

export const selectCustomerErrorCode = createSelector(
  selectAccountManagementState,
  (state: AccountManagementState): string => state.customerErrorCode,
);

export const selectCustomerListCounts = createSelector(
  selectRegularCustomerCount,
  selectGuestCustomerCount,
  selectTotalCustomersCount,
  (regularCustomerCount, guestCustomerCount, totalCustomersCount) => ({
    regularCustomerCount,
    guestCustomerCount,
    totalCustomersCount,
  }),
);

export const selectCustomersSorting = createSelector(
  selectRegularCustomersSorting,
  selectGuestCustomersSorting,
  (
    regularCustomersSorting,
    guestCustomersSorting,
  ): {
    regularCustomersSorting: FluidTableSorting;
    guestCustomersSorting: FluidTableSorting;
  } => ({
    regularCustomersSorting,
    guestCustomersSorting,
  }),
);

export const selectGetSelectedUserCustomerKey = createSelector(
  selectSelectedCustomer,
  selectUserCustomerKey,
  selectIsAccountManager,
  (
    selectedCustomer: InternalPortalCustomerDto,
    selectedUserCustomerKey: string,
    isAccountManager: boolean,
  ): string => {
    if (isAccountManager) {
      return selectedCustomer?.customerKey;
    }

    return selectedUserCustomerKey;
  },
);
