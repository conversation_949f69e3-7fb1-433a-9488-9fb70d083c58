import {
  AbstractValueAccessor,
  makeControlValidatorProvider,
  makeControlValueAccessorProvider,
} from '@fincloud/core/form';

import { NgClass, NgTemplateOutlet, SlicePipe } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
  inject,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { DotsLoaderComponent } from '@fincloud/components/dots-loader';
import { IconComponent } from '@fincloud/components/icons';
import { TruncatedTextComponent } from '@fincloud/components/truncated-text';
import {
  DropdownPosition,
  NgFooterTemplateDirective,
  NgLabelTemplateDirective,
  NgLoadingSpinnerTemplateDirective,
  NgMultiLabelTemplateDirective,
  NgOptionTemplateDirective,
  NgSelectComponent,
  NgTagTemplateDirective,
} from '@ng-select/ng-select';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { debounceTime, filter } from 'rxjs/operators';

@Component({
  selector: 'ui-select',
  templateUrl: './select.component.html',
  styleUrls: ['./select.component.scss'],
  providers: [
    makeControlValueAccessorProvider(SelectComponent),
    makeControlValidatorProvider(SelectComponent),
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgSelectComponent,
    FormsModule,
    NgClass,
    NgMultiLabelTemplateDirective,
    TruncatedTextComponent,
    NgLabelTemplateDirective,
    NgTemplateOutlet,
    NgOptionTemplateDirective,
    IconComponent,
    NgTagTemplateDirective,
    NgFooterTemplateDirective,
    NgLoadingSpinnerTemplateDirective,
    DotsLoaderComponent,
    SlicePipe,
  ],
})
export class SelectComponent
  extends AbstractValueAccessor<string>
  implements OnInit, AfterViewInit, OnDestroy
{
  private destroyRef = inject(DestroyRef);

  @ViewChild('select') select: NgSelectComponent;

  @Input()
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options: Record<string, any>[] | string[];

  @Input()
  labelKey = 'label';

  @Input()
  valueKey: string;

  @Input()
  placeholder = $localize`:@@select.initialPlaceHolder:Keine ausgewählt`;

  @Input()
  typeToSearchText =
    $localize`:@@select.typeToSearchText:Text eingeben zum Suchen`;

  @Input()
  notFoundText = $localize`:@@select.notfoundText:Keine Ergebnisse gefunden`;

  @Input()
  loadingText = $localize`:@@spinner.loadingMessage:Wird geladen`;

  @Input()
  size: 'regular' | 'medium' | 'large' = 'regular';

  @Input()
  background: 'dark' | 'light' = 'light';

  @Input()
  borderRadius: 'border-normal' | 'border-round' = 'border-normal';

  @Input()
  isDisabled = false;

  @Input()
  typeAhead: Subject<string>;

  @Input()
  groupBy: string;

  @Input()
  virtualScroll = true;

  @Input()
  addTag = false;

  @Input()
  addTagText = 'Artikel hinzufügen';

  @Input()
  hideBorder: boolean;

  @Input()
  hideArrow = false;

  @Input()
  multiple = false;

  @Input()
  maxPlaceholderItems: number;

  @Input()
  useCustomOptionTemplate = false;

  @Input()
  customTmplDelete = false;

  @Input()
  searchable = true;

  @Input()
  editableSearchTerm: boolean;

  @Input()
  attachTo: 'body' | 'component' = 'component';

  @Input()
  closeOnSelect = true;

  @Input()
  customOptionItemTemplate: TemplateRef<unknown>;

  @Input()
  customLabelTemplate: TemplateRef<unknown>;

  @Input()
  customTagTemplate: TemplateRef<unknown>;

  @Input()
  customSelectedItemTemplate: TemplateRef<unknown>;

  @Input()
  customFooterTemplate: TemplateRef<unknown>;

  @Input()
  isCustomFooterVisible = true;

  @Input()
  searchFn: (term: string, item: never) => boolean;

  @Input()
  clearModelOnBlur = false;

  @Input()
  isClearable = true;

  @Input()
  forceHasError = false;

  @Input()
  loading = false;

  @Input() hasInstantFeedback = true;

  @Input()
  dropdownPosition: DropdownPosition = 'auto';

  @Input() focused$: Observable<boolean>;

  @Input() truncateItem = false;

  @Input() maxWithTruncated: string;

  @Input()
  //Required if patching value of the select in Form control but doesn't matter if you return true or false :D
  compareWith: (a: unknown, b: unknown) => boolean;

  @Output()
  selectionChange = new EventEmitter();

  @Output()
  // eslint-disable-next-line @angular-eslint/no-output-native
  blur = new EventEmitter();

  @Output()
  // eslint-disable-next-line @angular-eslint/no-output-native
  focus = new EventEmitter();

  @Output()
  deleteOptionClicked = new EventEmitter();

  @Output()
  searchChange = new EventEmitter<string>();

  get appendTo() {
    return {
      body: 'body',
      component: undefined,
    }[this.attachTo];
  }

  onSearchTextChange$ = new BehaviorSubject<string>(null);

  private shouldFocus = false;
  private timeout: NodeJS.Timeout;

  triggerBlur() {
    this.select.blur();
  }

  onSearchTextChange(searchValue: {
    term: string;
    items: { value: string; name: string }[];
  }) {
    if (!searchValue.term) {
      return;
    }

    this.onSearchTextChange$.next(searchValue.term);
  }

  ngOnInit() {
    this.onSearchTextChange$
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter(Boolean),
        debounceTime(400),
      )
      .subscribe((text: string) => {
        this.searchChange.emit(text);
      });

    this.listenForFocusChange();

    if (this.groupBy) {
      this.virtualScroll = false; // if grouping is enabled, disable virtualScroll because it will cause jumpy scrolling
    }

    if (this.size === 'large' && this.hideBorder == undefined) {
      // keep existing behaviour after allowing border style to be controlled from outside (hideBorder input)
      // components with size large used to have no border
      this.hideBorder = true;
    }
  }

  ngAfterViewInit() {
    if (this.shouldFocus) {
      this.shouldFocus = false;

      if (this.select) {
        this.focusSelectAfterDelay();
      }
    }
  }

  ngOnDestroy() {
    if (this.timeout) {
      clearTimeout(this.timeout);
    }
  }

  onModelChange(value: unknown) {
    this.selectionChange.emit(value);
    this.triggerBlur();
  }

  onBlur() {
    if (this.clearModelOnBlur) {
      this.select.clearModel();
    }

    this.onTouch();
    this.blur.emit();
  }

  deleteItem(event: MouseEvent, item: unknown) {
    event.preventDefault();
    event.stopPropagation();

    this.deleteOptionClicked.emit(item);
  }

  onFocus() {
    this.focus.emit();
  }

  clearSelect() {
    this.select.clearModel();
  }

  closeDropdown() {
    this.select.close();
  }

  private listenForFocusChange() {
    if (this.focused$) {
      this.focused$
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe((focused: boolean) => {
          if (this.select && focused) {
            this.focusSelectAfterDelay();
          } else if (!this.select && focused) {
            this.shouldFocus = true;
          }
        });
    }
  }

  private focusSelectAfterDelay() {
    if (this.timeout) {
      clearTimeout(this.timeout);
    }

    this.timeout = setTimeout(
      (() => {
        this.select.focus();
      }).bind(this),
      400,
    );
  }
}
